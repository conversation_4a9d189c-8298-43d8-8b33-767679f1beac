#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能招聘系统启动器
同时启动三个主要服务：
1. 简历评估系统 (端口8008)
2. JD生成器 (端口8012) 
3. Boss直聘爬虫系统 (端口8000)
"""

import multiprocessing
import os
import sys
import time


def run_resume_score():
    """启动简历评估系统"""
    print("🚀 启动简历评估系统 (端口8008)...")
    try:
        # 导入并运行简历评估系统
        from resume_score_new import main as resume_main
        resume_main()
    except Exception as e:
        print(f"❌ 简历评估系统启动失败: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc()


def run_jd_generator():
    """启动JD生成器"""
    print("🚀 启动JD生成器 (端口8012)...")
    try:
        # 导入并运行JD生成器
        from jd_generator_api import main as jd_main
        jd_main()
    except Exception as e:
        print(f"❌ JD生成器启动失败: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc()

def run_rpa_boss():
    """启动Boss直聘爬虫系统"""
    print("🚀 启动Boss直聘爬虫系统 (端口8000)...")
    try:
        # 保存当前工作目录
        original_cwd = os.getcwd()

        # 切换到high_rpa_boss目录
        high_rpa_boss_path = os.path.join(original_cwd, 'high_rpa_boss')
        if os.path.exists(high_rpa_boss_path):
            os.chdir(high_rpa_boss_path)
            print(f"📁 切换到工作目录: {high_rpa_boss_path}")
        else:
            print(f"⚠️ 警告: 找不到目录 {high_rpa_boss_path}")

        # 导入并运行爬虫系统
        import sys
        sys.path.append('.')
        from high_rpa_boss.run import main as rpa_main
        rpa_main()

        # 恢复原始工作目录
        os.chdir(original_cwd)

    except Exception as e:
        print(f"❌ Boss直聘爬虫系统启动失败: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc()

        # 确保恢复原始工作目录
        try:
            os.chdir(original_cwd)
        except:
            pass


def check_dependencies():
    """检查必要的依赖和文件"""
    print("🔍 检查系统依赖...")
    
    # 导入路径管理器
    from path_manager import path_manager
    
    # 获取程序运行目录（exe所在目录）
    base_path = path_manager.base_path
    print(f"📁 程序运行目录: {base_path}")
    
    # 检查必要的目录
    required_dirs = ['Prompt', 'frontend']
    for dir_name in required_dirs:
        dir_path = path_manager.get_path(dir_name)
        if not os.path.exists(dir_path):
            print(f"❌ 缺少必要目录: {dir_path}")
            return False
        print(f"✅ 目录存在: {dir_path}")
    
    # 创建logs目录（如果不存在）
    logs_path = path_manager.get_logs_dir()
    if not os.path.exists(logs_path):
        try:
            os.makedirs(logs_path, exist_ok=True)
            print(f"✅ 创建目录: {logs_path}")
        except Exception as e:
            print(f"❌ 创建logs目录失败: {e}")
            return False
    else:
        print(f"✅ 目录存在: {logs_path}")
    
    # 检查配置文件
    config_path = path_manager.get_path('config.py')
    if not os.path.exists(config_path):
        print(f"❌ 缺少配置文件: {config_path}")
        return False
    print(f"✅ 配置文件存在: {config_path}")
    
    return True


def main():
    """主启动函数"""
    print("=" * 60)
    print("🎯 智能招聘系统启动器")
    print("=" * 60)

    # 检查依赖
    if not check_dependencies():
        print("❌ 系统依赖检查失败，请检查项目文件")
        sys.exit(1)

    print("\n📋 系统服务信息:")
    print("  • 简历评估系统: http://localhost:8008")
    print("  • JD生成器: http://localhost:8012")
    print("  • Boss直聘爬虫: http://localhost:8000")
    print("\n⏳ 正在启动所有服务...")

    # 创建进程
    processes = []

    # 启动简历评估系统
    p1 = multiprocessing.Process(target=run_resume_score, name="ResumeScore")
    processes.append(p1)

    # 启动JD生成器
    p2 = multiprocessing.Process(target=run_jd_generator, name="JDGenerator")
    processes.append(p2)

    # 启动爬虫系统
    p3 = multiprocessing.Process(target=run_rpa_boss, name="RpaBoss")
    processes.append(p3)

    # 启动所有进程
    for p in processes:
        p.start()
        time.sleep(1)  # 稍微延迟，避免端口冲突

    print("✅ 所有服务已启动")
    print("\n🌐 服务访问地址:")
    print("  • 简历评估系统: http://localhost:8008")
    print("  • JD生成器: http://localhost:8012")
    print("  • Boss直聘爬虫: http://localhost:8000")
    print("\n💡 提示: 按 Ctrl+C 停止所有服务")
    print("=" * 60)

    # 启动前端
    import webbrowser
    try:
        webbrowser.open("http://localhost:8008")
    except Exception as e:
        print(f"⚠️ 无法自动打开浏览器，请手动访问 http://localhost:8008\n错误信息: {e}")


    try:
        # 等待所有进程
        for p in processes:
            p.join()
    except KeyboardInterrupt:
        print("\n🛑 收到停止信号，正在关闭所有服务...")
        for p in processes:
            if p.is_alive():
                p.terminate()
                p.join(timeout=5)
                if p.is_alive():
                    p.kill()
        print("✅ 所有服务已停止")
    except Exception as e:
        print(f"❌ 系统运行出错: {e}")
        # 确保清理所有进程
        for p in processes:
            if p.is_alive():
                p.terminate()
                p.join(timeout=5)


if __name__ == "__main__":
    # 这是Windows上打包multiprocessing应用的必需步骤
    multiprocessing.freeze_support()
    
    # 导入路径管理器并设置工作目录
    from path_manager import path_manager
    
    # 设置工作目录为exe文件所在目录
    os.chdir(path_manager.base_path)
    print(f"📁 设置工作目录: {path_manager.base_path}")
    
    main()
