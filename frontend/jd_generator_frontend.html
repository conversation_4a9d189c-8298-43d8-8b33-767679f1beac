<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title>智能JD生成器</title>
    <style>
        :root {
            --primary-color: #4a6cf7;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --border-color: #dee2e6;
            --border-radius: 6px;
            --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f7fb;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            min-height: calc(100vh - 40px);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
        }

        .header h1 {
            color: var(--primary-color);
            font-size: 28px;
            margin-bottom: 10px;
        }

        .header p {
            color: var(--secondary-color);
            font-size: 16px;
        }

        .main-content {
            display: flex;
            flex: 1;
            gap: 20px;
        }

        .conversation-panel {
            flex: 3;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .progress-panel {
            flex: 1;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 20px;
            height: fit-content;
        }

        .progress-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: var(--dark-color);
        }

        .progress-steps {
            list-style-type: none;
        }

        .progress-step {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        .progress-step.active {
            opacity: 1;
        }

        .progress-step.completed .step-number {
            background-color: var(--success-color);
        }

        .step-number {
            width: 30px;
            height: 30px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
        }

        .step-text {
            font-size: 14px;
        }

        .conversation-area {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 20px;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            background-color: #f9f9f9;
            min-height: 300px;
            max-height: 500px;
        }

        .message {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: var(--border-radius);
            max-width: 80%;
        }

        .message.system {
            background-color: #e9ecef;
            align-self: flex-start;
        }

        .message.user {
            background-color: var(--primary-color);
            color: white;
            align-self: flex-end;
            margin-left: auto;
        }

        .options-area {
            margin-bottom: 20px;
        }

        .option-group {
            margin-bottom: 15px;
        }

        .option-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .option-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: all 0.2s;
        }

        .option-item:hover {
            background-color: #f0f0f0;
        }

        .option-item.selected {
            background-color: rgba(74, 108, 247, 0.1);
            border-color: var(--primary-color);
        }

        .option-item input[type="checkbox"],
        .option-item input[type="radio"] {
            margin-right: 10px;
        }

        .custom-input {
            margin-top: 5px;
            margin-left: 25px;
            width: calc(100% - 25px);
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            display: none;
        }

        .custom-input.visible {
            display: block;
        }

        .input-area {
            display: flex;
            gap: 10px;
        }

        .input-field {
            flex: 1;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-family: inherit;
            resize: none;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: var(--border-radius);
            background-color: var(--primary-color);
            color: white;
            cursor: pointer;
            font-weight: bold;
            transition: background-color 0.2s;
        }

        .btn:hover {
            background-color: #3a5bd9;
        }

        .btn:disabled {
            background-color: var(--secondary-color);
            cursor: not-allowed;
        }

        .btn-secondary {
            background-color: var(--secondary-color);
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }

        .welcome-screen {
            text-align: center;
            padding: 30px;
        }

        .welcome-screen h2 {
            margin-bottom: 20px;
            color: var(--primary-color);
        }

        .welcome-screen p {
            margin-bottom: 15px;
            font-size: 16px;
        }

        .welcome-screen textarea {
            width: 100%;
            height: 150px;
            margin-bottom: 20px;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-family: inherit;
            resize: vertical;
        }

        .result-screen {
            padding: 20px;
        }

        .result-screen h2 {
            margin-bottom: 20px;
            color: var(--success-color);
        }

        .result-content {
            white-space: pre-wrap;
            background-color: #f9f9f9;
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            margin-bottom: 20px;
            max-height: 500px;
            overflow-y: auto;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        .loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 200px;
        }

        .loading-spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-left-color: var(--primary-color);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin-bottom: 15px;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        /* 新增样式 */
        .summary-screen {
            padding: 20px;
        }

        .summary-content {
            background-color: #f9f9f9;
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            margin-bottom: 20px;
            max-height: 400px;
            overflow-y: auto;
            min-height: 200px;
        }

        .summary-content[contenteditable="true"] {
            background-color: white;
            border: 2px solid var(--primary-color);
            outline: none;
        }

        .summary-content[contenteditable="true"]:focus {
            border-color: #3a5bd9;
            box-shadow: 0 0 5px rgba(74, 108, 247, 0.3);
        }

        .summary-item {
            margin-bottom: 15px;
            padding: 10px;
            background-color: white;
            border-radius: var(--border-radius);
            border-left: 4px solid var(--primary-color);
        }

        .summary-item h4 {
            margin-bottom: 8px;
            color: var(--primary-color);
        }

        .summary-item p {
            margin: 0;
            color: var(--dark-color);
        }

        .jd-modification-screen {
            display: flex;
            gap: 20px;
            height: 100vh;
            padding: 20px;
            box-sizing: border-box;
        }

        .conversation-history {
            flex: 1;
            background-color: white;
            border-radius: var(--border-radius);
            padding: 20px;
            display: flex;
            flex-direction: column;
            border: 1px solid var(--border-color);
        }

        .jd-preview {
            flex: 1;
            background-color: white;
            border-radius: var(--border-radius);
            padding: 20px;
            display: flex;
            flex-direction: column;
            border: 1px solid var(--border-color);
        }

        .jd-content {
            flex: 1;
            background-color: #f9f9f9;
            padding: 15px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            overflow-y: auto;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }

        .modification-input {
            margin-top: 15px;
            display: flex;
            gap: 10px;
        }

        .modification-input textarea {
            flex: 1;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            resize: vertical;
            min-height: 60px;
        }

        .modification-input button {
            padding: 10px 20px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
        }

        .modification-input button:hover {
            background-color: #3a5bd9;
        }

        /* 隐藏右侧导航 */
        .jd-modification-screen .progress-panel {
            display: none;
        }

        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }

            .conversation-panel, .progress-panel {
                width: 100%;
            }
        }
    </style>
</head>
<body>
<div class="container">
    <div class="header">
        <h1>智能JD生成器</h1>
        <p>基于大模型的职位描述生成工具，通过8轮对话，生成专业、完整的招聘JD</p>
        <div id="userInfo" style="margin-top: 15px; padding: 10px; background: rgba(74, 108, 247, 0.1); border-radius: 5px; display: none;">
            <span id="currentUserName" style="font-weight: bold; color: #4a6cf7;"></span>
            <span id="currentUserCompany" style="margin-left: 10px; color: #666;"></span>
            <button onclick="logout()" style="margin-left: 15px; padding: 5px 10px; background: #dc3545; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 12px;">退出登录</button>
        </div>
    </div>

    <div class="main-content">
        <div class="conversation-panel" id="conversationPanel">
            <!-- 欢迎界面 -->
            <div class="welcome-screen" id="welcomeScreen">
                <h2>欢迎使用智能JD生成器</h2>
                <p>请先简单描述一下您的公司情况和要招聘的岗位基本信息：</p>
                <textarea id="companyDescription"
                          placeholder="例如：我们是一家互联网科技公司，主要做企业SaaS服务，现在需要招聘一名前端开发工程师..."></textarea>
                <button class="btn" id="startConversationBtn">确认，开始对话</button>
            </div>

            <!-- 对话界面 -->
            <div id="conversationScreen" style="display: none; flex-direction: column; height: 100%;">
                <div class="conversation-area" id="conversationArea"></div>

                <div class="options-area" id="optionsArea">
                    <!-- 选项将动态生成 -->
                </div>

                <div class="input-area">
                    <textarea class="input-field" id="userInput" placeholder="请输入您的回答..." rows="2"></textarea>
                    <button class="btn" id="sendBtn">发送</button>
                    <button class="btn btn-secondary" id="nextBtn" style="display: none;">下一题</button>
                </div>
            </div>

            <!-- 结果界面 -->
            <div class="result-screen" id="resultScreen" style="display: none;">
                <h2>JD生成完成</h2>
                <div class="result-content" id="resultContent"></div>
                <div class="action-buttons">
                    <button class="btn" id="copyBtn">复制JD</button>
                    <button class="btn btn-secondary" id="restartBtn">重新生成</button>
                    <button class="btn" id="evaluateResumesBtn" style="background-color: #28a745;">开始评估简历</button>
                </div>
            </div>

            <!-- 总结界面 -->
            <div class="summary-screen" id="summaryScreen" style="display: none;">
                <h2>信息总结与确认</h2>
                <div class="summary-content" contenteditable="true" id="summaryContent"></div>
                <div class="action-buttons">
                    <button class="btn" id="confirmBtn">确认信息，生成JD</button>
                    <button class="btn btn-secondary" id="backBtn">返回修改</button>
                </div>
            </div>

            <!-- JD修改界面 -->
            <div class="jd-modification-screen" id="jdModificationScreen"
                 style="display: none; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: white; z-index: 1000;">
                <div class="conversation-history">
                    <h3>对话历史</h3>
                    <div class="conversation-area" id="modificationConversationArea"></div>
                    <div class="modification-input">
                        <textarea id="modificationInput"
                                  placeholder="请输入您的修改要求，例如：我想把学历要求中的大专换成本科，或者技能要求的语言更通俗一点..."></textarea>
                        <button id="modifyBtn">修改JD</button>
                    </div>
                </div>
                <div class="jd-preview">
                    <h3>JD预览</h3>
                    <div class="jd-content" id="jdPreviewContent"></div>
                    <div class="action-buttons" style="margin-top: 15px;">
                        <button class="btn" id="finalCopyBtn">复制JD</button>
                        <button class="btn btn-secondary" id="finalRestartBtn">重新生成</button>
                        <button class="btn" id="finalEvaluateResumesBtn" style="background-color: #28a745;">开始评估简历
                        </button>
                    </div>
                </div>
            </div>

            <!-- 加载界面 -->
            <div class="loading" id="loadingScreen" style="display: none;">
                <div class="loading-spinner"></div>
                <p>正在生成JD，请稍候...</p>
            </div>
        </div>

        <div class="progress-panel">
            <h3 class="progress-title">对话进度</h3>
            <ul class="progress-steps" id="progressSteps">
                <li class="progress-step" data-step="0">
                    <div class="step-number">1</div>
                    <div class="step-text">基础定位</div>
                </li>
                <li class="progress-step" data-step="1">
                    <div class="step-number">2</div>
                    <div class="step-text">核心职责确定</div>
                </li>
                <li class="progress-step" data-step="2">
                    <div class="step-number">3</div>
                    <div class="step-text">核心技能需求</div>
                </li>
                <li class="progress-step" data-step="3">
                    <div class="step-number">4</div>
                    <div class="step-text">扩展技能和工具</div>
                </li>
                <li class="progress-step" data-step="4">
                    <div class="step-number">5</div>
                    <div class="step-text">候选人背景偏好</div>
                </li>
                <li class="progress-step" data-step="5">
                    <div class="step-number">6</div>
                    <div class="step-text">经验要求与能力深度</div>
                </li>
                <li class="progress-step" data-step="6">
                    <div class="step-number">7</div>
                    <div class="step-text">软技能与工作方式</div>
                </li>
                <li class="progress-step" data-step="7">
                    <div class="step-number">8</div>
                    <div class="step-text">信息总结与确认</div>
                </li>
            </ul>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        // 检查用户登录状态
        checkUserLoginStatus();
        
        // 全局变量
        const state = {
            currentRound: 0,
            selectedOptions: {},
            customInputs: {},
            companyDescription: '',
            conversationHistory: [],
            currentUser: null  // 添加当前用户信息
        };

        // 检查用户登录状态
        function checkUserLoginStatus() {
            // 从localStorage获取用户信息
            const userInfo = localStorage.getItem('currentUser');
            if (userInfo) {
                try {
                    state.currentUser = JSON.parse(userInfo);
                    console.log('已获取登录用户信息:', state.currentUser);
                } catch (e) {
                    console.error('解析用户信息失败:', e);
                    state.currentUser = null;
                }
            }
            
            // 如果没有登录用户，显示登录提示
            if (!state.currentUser) {
                hideUserInfo(); // 隐藏用户信息
                showLoginPrompt();
            } else {
                // 如果已登录，显示用户信息
                showUserInfo();
            }
        }

        // 显示登录提示
        function showLoginPrompt() {
            const loginPrompt = document.createElement('div');
            loginPrompt.id = 'loginPrompt';
            loginPrompt.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                z-index: 1000;
                text-align: center;
                max-width: 400px;
            `;
            
            loginPrompt.innerHTML = `
                <h3 style="margin-bottom: 20px; color: #4a6cf7;">需要登录</h3>
                <p style="margin-bottom: 20px; color: #666;">请先登录系统以使用JD生成功能</p>
                <div style="margin-bottom: 20px;">
                    <input type="text" id="tempUsername" placeholder="请输入用户名" 
                           style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; margin-bottom: 10px;">
                    <input type="text" id="tempCompany" placeholder="请输入公司名称" 
                           style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                </div>
                <div>
                    <button onclick="setTempUser()" style="background: #4a6cf7; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin-right: 10px; cursor: pointer;">确定</button>
                    <button onclick="closeLoginPrompt()" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">取消</button>
                </div>
            `;
            
            document.body.appendChild(loginPrompt);
        }

        // 设置临时用户
        function setTempUser() {
            const username = document.getElementById('tempUsername').value.trim();
            const company = document.getElementById('tempCompany').value.trim();
            
            if (!username) {
                alert('请输入用户名');
                return;
            }
            
            // 设置用户信息
            state.currentUser = {
                name: username,
                company: company || '用户公司'
            };
            
            // 保存到localStorage
            localStorage.setItem('currentUser', JSON.stringify(state.currentUser));
            
            // 设置后端当前用户
            fetch('/api/v1/set_current_user', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    user_name: username,
                    company: company || '用户公司'
                })
            }).then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('后端用户设置成功:', data.message);
                    showUserInfo(); // 显示用户信息
                }
            }).catch(error => {
                console.error('设置后端用户失败:', error);
            });
            
            closeLoginPrompt();
        }

        // 关闭登录提示
        function closeLoginPrompt() {
            const loginPrompt = document.getElementById('loginPrompt');
            if (loginPrompt) {
                loginPrompt.remove();
            }
        }

        // 获取当前用户名
        function getCurrentUserName() {
            if (state.currentUser && state.currentUser.name) {
                return state.currentUser.name;
            }
            return null;
        }

        // 显示用户信息
        function showUserInfo() {
            const userInfoDiv = document.getElementById('userInfo');
            const currentUserName = document.getElementById('currentUserName');
            const currentUserCompany = document.getElementById('currentUserCompany');

            if (userInfoDiv && currentUserName && currentUserCompany) {
                userInfoDiv.style.display = 'block';
                currentUserName.textContent = state.currentUser.name;
                currentUserCompany.textContent = state.currentUser.company;
            }
        }

        // 隐藏用户信息
        function hideUserInfo() {
            const userInfoDiv = document.getElementById('userInfo');
            if (userInfoDiv) {
                userInfoDiv.style.display = 'none';
            }
        }

        // 退出登录
        function logout() {
            localStorage.removeItem('currentUser');
            state.currentUser = null;
            hideUserInfo(); // 隐藏用户信息
            showLoginPrompt(); // 显示登录提示
            alert('已退出登录');
        }

        // DOM元素
        const elements = {
            welcomeScreen: document.getElementById('welcomeScreen'),
            conversationScreen: document.getElementById('conversationScreen'),
            resultScreen: document.getElementById('resultScreen'),
            summaryScreen: document.getElementById('summaryScreen'),
            jdModificationScreen: document.getElementById('jdModificationScreen'),
            loadingScreen: document.getElementById('loadingScreen'),
            companyDescription: document.getElementById('companyDescription'),
            startConversationBtn: document.getElementById('startConversationBtn'),
            conversationArea: document.getElementById('conversationArea'),
            optionsArea: document.getElementById('optionsArea'),
            userInput: document.getElementById('userInput'),
            sendBtn: document.getElementById('sendBtn'),
            nextBtn: document.getElementById('nextBtn'),
            resultContent: document.getElementById('resultContent'),
            copyBtn: document.getElementById('copyBtn'),
            restartBtn: document.getElementById('restartBtn'),
            progressSteps: document.getElementById('progressSteps'),
            summaryContent: document.getElementById('summaryContent'),
            confirmBtn: document.getElementById('confirmBtn'),
            backBtn: document.getElementById('backBtn'),
            modificationConversationArea: document.getElementById('modificationConversationArea'),
            modificationInput: document.getElementById('modificationInput'),
            modifyBtn: document.getElementById('modifyBtn'),
            jdPreviewContent: document.getElementById('jdPreviewContent'),
            finalCopyBtn: document.getElementById('finalCopyBtn'),
            finalRestartBtn: document.getElementById('finalRestartBtn')
        };

        // 初始化事件监听
        elements.startConversationBtn.addEventListener('click', startConversation);
        elements.sendBtn.addEventListener('click', sendUserInput);
        elements.nextBtn.addEventListener('click', goToNextQuestion);
        elements.copyBtn.addEventListener('click', copyResultToClipboard);
        elements.restartBtn.addEventListener('click', restartConversation);
        elements.confirmBtn.addEventListener('click', confirmAndGenerateJD);
        elements.backBtn.addEventListener('click', goBackToConversation);
        elements.modifyBtn.addEventListener('click', modifyJD);
        elements.finalCopyBtn.addEventListener('click', copyFinalJDToClipboard);
        elements.finalRestartBtn.addEventListener('click', restartConversation);

        // 添加评估简历按钮的事件监听
        const evaluateResumesBtn = document.getElementById('evaluateResumesBtn');
        const finalEvaluateResumesBtn = document.getElementById('finalEvaluateResumesBtn');

        if (evaluateResumesBtn) {
            evaluateResumesBtn.addEventListener('click', redirectToResumeEvaluation);
        }

        if (finalEvaluateResumesBtn) {
            finalEvaluateResumesBtn.addEventListener('click', redirectToResumeEvaluation);
        }

        // 跳转到简历评估系统
        async function redirectToResumeEvaluation() {
            // 获取当前JD内容
            const jdContent = elements.resultContent.textContent || elements.jdPreviewContent.textContent;

            if (!jdContent) {
                alert('没有可用的JD内容');
                return;
            }

            // 显示加载状态
            showLoading(true);

            try {
                // 提取岗位名称
                const positionNameMatch = jdContent.match(/^#*\s*(.*?)(?:\n|$)/);
                const positionName = positionNameMatch ? positionNameMatch[1].trim() : "未知岗位";

                // 生成文件名
                const timestamp = new Date().toISOString().replace(/[-:]/g, '').replace('T', '_').substring(0, 15);
                const filename = `${positionName}_${timestamp}.md`;

                // 保存JD到后端
                const response = await fetch('http://localhost:8002/save_jd', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        content: jdContent,
                        filename: filename,
                        user_name: getCurrentUserName() // 传递用户名
                    })
                });

                const result = await response.json();

                if (!result.success) {
                    throw new Error(result.message || '保存JD失败');
                }

                // 保存到localStorage以便在简历评估系统中使用
                localStorage.setItem('currentJD', jdContent);
                localStorage.setItem('currentPositionName', positionName);
                localStorage.setItem('currentJDPath', result.file_path);

                // 隐藏加载状态
                showLoading(false);

                // 跳转到简历评估系统
                window.location.href = 'http://localhost:8002';
            } catch (error) {
                showLoading(false);
                console.error('保存JD失败:', error);
                alert('保存JD失败: ' + error.message);
            }
        }

        elements.userInput.addEventListener('keypress', function (e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendUserInput();
            }
        });
        elements.modificationInput.addEventListener('keypress', function (e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                modifyJD();
            }
        });

        // 开始对话
        function startConversation() {
            const description = elements.companyDescription.value.trim();
            if (!description) {
                alert('请输入公司和岗位基本情况描述');
                return;
            }

            state.companyDescription = description;
            elements.welcomeScreen.style.display = 'none';
            elements.conversationScreen.style.display = 'flex';

            // 添加系统消息
            addMessage('system', '欢迎使用智能JD生成器，我将通过8轮对话帮您生成专业的招聘JD。');

            // 更新进度
            updateProgress(0);

            // 开始第一轮对话
            fetchNextQuestion();
        }

        // 获取下一个问题
        function fetchNextQuestion(userInput, selectedData) {
            showLoading(true);
            elements.sendBtn.disabled = true;
            elements.nextBtn.disabled = true;

            // 只有在用户提交选项时，才更新 state
            if (selectedData) {
                const {round, values} = selectedData;
                if (!state.selectedOptions[round]) {
                    state.selectedOptions[round] = [];
                }
                state.selectedOptions[round].push(values.join(', '));
            }

            fetch('/api/v1/conversation', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    company_description: state.companyDescription,
                    current_round: state.currentRound,
                    selected_options: state.selectedOptions,
                    custom_inputs: state.customInputs,
                    user_input: userInput,
                    user_name: getCurrentUserName() // 传递用户名
                })
            })
                .then(response => response.json())
                .then(data => {
                    showLoading(false);
                    elements.sendBtn.disabled = false;
                    elements.nextBtn.disabled = false;

                    if (data.success) {
                        // 检查是否是总结轮次
                        if (data.is_summary_round) {
                            showSummaryScreen(data.message);
                            return;
                        }

                        // 先更新当前轮次
                        state.currentRound = data.next_round;
                        updateProgress(state.currentRound);

                        // 调试信息
                        console.log('后端返回的next_round:', data.next_round);
                        console.log('更新后的state.currentRound:', state.currentRound);

                        // 添加AI回复到对话区域
                        addMessage('system', data.message);

                        // 显示选项或准备文本输入
                        if (data.options && data.options.length > 0) {
                            // 有选项，显示选择题（所有都是多选）
                            showOptions(data.options, state.currentRound);
                            elements.nextBtn.style.display = 'block';
                            elements.sendBtn.style.display = 'none';
                        } else {
                            // 没有选项，是开放题或补充问题
                            elements.optionsArea.innerHTML = '';
                            elements.nextBtn.style.display = 'none';
                            elements.sendBtn.style.display = 'block';
                            elements.userInput.focus();
                        }

                        // 检查是否完成所有对话
                        if (state.currentRound >= 8) {
                            showResult(data.message);
                            return;
                        }
                    } else {
                        alert('获取问题失败: ' + data.message);
                    }
                })
                .catch(error => {
                    showLoading(false);
                    elements.sendBtn.disabled = false;
                    elements.nextBtn.disabled = false;
                    console.error('Error:', error);
                    alert('网络错误，请重试');
                });
        }

        // 显示选项 - 所有问题都使用多选框
        function showOptions(options, roundIndex) {
            elements.optionsArea.innerHTML = '';
            // 使用固定的questionIndex，避免name属性变化
            const questionIndex = 0;

            const optionGroup = document.createElement('div');
            optionGroup.className = 'option-group';

            options.forEach((option, index) => {
                const optionItem = document.createElement('div');
                optionItem.className = 'option-item';

                const input = document.createElement('input');
                input.type = 'checkbox'; // 所有问题都使用多选框
                input.name = `question_${roundIndex}_${questionIndex}`;
                input.value = option;
                input.id = `option_${roundIndex}_${questionIndex}_${index}`;

                const label = document.createElement('label');
                label.htmlFor = input.id;
                label.textContent = option;

                optionItem.appendChild(input);
                optionItem.appendChild(label);
                optionGroup.appendChild(optionItem);

                // 处理"其他"选项的自定义输入
                if (option.includes('其他') || option.includes('请说明')) {
                    const customInput = document.createElement('input');
                    customInput.type = 'text';
                    customInput.className = 'custom-input';
                    customInput.placeholder = '请详细说明...';
                    customInput.setAttribute('data-for', input.id);

                    optionItem.appendChild(customInput);

                    input.addEventListener('change', function () {
                        if (this.checked) {
                            customInput.classList.add('visible');
                            customInput.focus();
                        } else {
                            customInput.classList.remove('visible');
                            customInput.value = '';
                        }
                    });
                }

                // 点击选项区域选中选项
                optionItem.addEventListener('click', function (e) {
                    if (e.target.tagName !== 'INPUT' && e.target.tagName !== 'LABEL') {
                        input.click();
                    }
                });

                // 更新选中状态样式
                input.addEventListener('change', function () {
                    document.querySelectorAll(`input[name="${input.name}"]`).forEach(el => {
                        el.closest('.option-item').classList.toggle('selected', el.checked);
                    });
                });
            });

            elements.optionsArea.appendChild(optionGroup);
        }

        // 处理选择题的下一题按钮
        function goToNextQuestion() {
            const roundIndex = state.currentRound;
            // 获取当前轮次所有选中的选项
            const selectedInputs = document.querySelectorAll(`input[name^="question_${roundIndex}_"]:checked`);

            // 调试信息
            console.log('当前轮次:', roundIndex);
            console.log('选中的选项数量:', selectedInputs.length);
            console.log('所有选项:', document.querySelectorAll(`input[name^="question_${roundIndex}_"]`));
            console.log('选中的选项:', selectedInputs);
            console.log('state.currentRound:', state.currentRound);

            if (selectedInputs.length === 0) {
                alert('请至少选择一个选项');
                return;
            }

            // 收集选中的值，包括自定义输入
            const selectedValues = Array.from(selectedInputs).map(input => {
                const customInput = document.querySelector(`input.custom-input[data-for="${input.id}"]`);
                if (customInput && customInput.classList.contains('visible') && customInput.value.trim()) {
                    return `${input.value}: ${customInput.value.trim()}`;
                }
                return input.value;
            });

            // 显示用户选择
            addMessage('user', selectedValues.join(', '));

            // 调用API获取下一个问题或补充问题
            fetchNextQuestion(null, {round: roundIndex, values: selectedValues});
        }

        // "发送"按钮的点击事件处理 (用于回答补充问题)
        function sendUserInput() {
            const userInput = elements.userInput.value.trim();
            const userResponse = userInput || '(无补充)'; // 如果为空，也发送一个占位符

            addMessage('user', userResponse);
            elements.userInput.value = '';

            // 调用API获取下一个问题
            fetchNextQuestion(userResponse, null);
        }

        // 显示结果
        function showResult(jdContent) {
            elements.conversationScreen.style.display = 'none';
            elements.summaryScreen.style.display = 'none';
            elements.resultScreen.style.display = 'block';
            elements.jdModificationScreen.style.display = 'none';
            elements.resultContent.textContent = jdContent;

            // 更新进度为完成
            updateProgress(8);
        }

        // 显示JD修改界面
        function showJDModificationScreen(jdContent) {
            elements.conversationScreen.style.display = 'none';
            elements.summaryScreen.style.display = 'none';
            elements.resultScreen.style.display = 'none';
            elements.jdModificationScreen.style.display = 'flex';

            // 显示JD内容
            elements.jdPreviewContent.textContent = jdContent;

            // 复制对话历史到修改界面的对话区域
            elements.modificationConversationArea.innerHTML = '';
            state.conversationHistory.forEach(msg => {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${msg.type}`;
                messageDiv.textContent = msg.content;
                elements.modificationConversationArea.appendChild(messageDiv);
            });

            // 更新进度为完成
            updateProgress(8);
        }

        // 复制结果到剪贴板
        function copyResultToClipboard() {
            const jdContent = elements.resultContent.textContent;
            navigator.clipboard.writeText(jdContent)
                .then(() => {
                    alert('JD已复制到剪贴板');
                })
                .catch(err => {
                    console.error('复制失败:', err);
                    alert('复制失败，请手动复制');
                });
        }

        // 重新开始对话
        function restartConversation() {
            // 重置状态
            state.currentRound = 0;
            state.selectedOptions = {};
            state.customInputs = {};
            state.conversationHistory = [];

            // 重置界面
            elements.resultScreen.style.display = 'none';
            elements.summaryScreen.style.display = 'none';
            elements.jdModificationScreen.style.display = 'none';
            elements.welcomeScreen.style.display = 'block';
            elements.conversationArea.innerHTML = '';
            elements.optionsArea.innerHTML = '';
            elements.userInput.value = '';

            // 重置进度
            document.querySelectorAll('.progress-step').forEach(step => {
                step.classList.remove('active', 'completed');
            });
        }

        // 添加消息到对话区域
        function addMessage(type, content) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = content;

            elements.conversationArea.appendChild(messageDiv);
            elements.conversationArea.scrollTop = elements.conversationArea.scrollHeight;

            // 保存到历史记录
            state.conversationHistory.push({
                type,
                content
            });
        }

        // 更新进度
        function updateProgress(currentRound) {
            document.querySelectorAll('.progress-step').forEach((step, index) => {
                step.classList.remove('active', 'completed');

                if (index < currentRound) {
                    step.classList.add('completed');
                } else if (index === currentRound) {
                    step.classList.add('active');
                }
            });
        }

        // 显示/隐藏加载状态
        function showLoading(show) {
            if (show) {
                elements.loadingScreen.style.display = 'flex';
            } else {
                elements.loadingScreen.style.display = 'none';
            }
        }

        // 显示总结界面
        function showSummaryScreen(message) {
            elements.conversationScreen.style.display = 'none';
            elements.summaryScreen.style.display = 'block';
            elements.summaryContent.innerHTML = ''; // 清空之前的总结内容

            // 直接显示消息内容
            elements.summaryContent.textContent = message;

            updateProgress(7); // 进度显示为总结轮次
        }

        // 确认信息并生成JD
        function confirmAndGenerateJD() {
            // 获取编辑后的总结内容
            const summaryContent = elements.summaryContent.innerText || elements.summaryContent.textContent;
            if (!summaryContent) {
                alert('请先查看并确认信息总结，然后点击确认生成JD。');
                return;
            }

            showLoading(true);
            fetch('/api/v1/generate_jd', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    company_description: state.companyDescription,
                    summary_data: summaryContent,
                    user_name: getCurrentUserName() // 传递用户名
                })
            })
                .then(response => response.json())
                .then(data => {
                    showLoading(false);
                    if (data.success) {
                        showJDModificationScreen(data.jd_content);
                    } else {
                        alert('生成JD失败: ' + data.message);
                    }
                })
                .catch(error => {
                    showLoading(false);
                    console.error('Error:', error);
                    alert('网络错误，请重试');
                });
        }

        // 返回上一轮对话
        function goBackToConversation() {
            elements.summaryScreen.style.display = 'none';
            elements.conversationScreen.style.display = 'flex';
            // 重新获取当前轮次的问题
            fetchNextQuestion(null, null);
        }

        // 修改JD
        function modifyJD() {
            const modificationInput = elements.modificationInput.value.trim();
            if (!modificationInput) {
                alert('请输入您的修改要求。');
                return;
            }

            const originalJD = elements.jdPreviewContent.textContent;
            if (!originalJD) {
                alert('没有可修改的JD内容。');
                return;
            }

            showLoading(true);
            fetch('/api/v1/modify_jd', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    original_jd: originalJD,
                    modification_request: modificationInput,
                    conversation_history: state.conversationHistory,
                    user_name: getCurrentUserName() // 传递用户名
                })
            })
                .then(response => response.json())
                .then(data => {
                    showLoading(false);
                    if (data.success) {
                        // 更新JD预览内容
                        elements.jdPreviewContent.textContent = data.modified_jd;
                        // 清空修改输入框
                        elements.modificationInput.value = '';
                        // 添加修改记录到对话历史
                        addModificationMessage(modificationInput, data.modified_jd);

                        // 更新localStorage中的JD内容
                        localStorage.setItem('currentJD', data.modified_jd);

                        // 提取岗位名称
                        const positionNameMatch = data.modified_jd.match(/^#*\s*(.*?)(?:\n|$)/);
                        const positionName = positionNameMatch ? positionNameMatch[1].trim() : "未知岗位";
                        localStorage.setItem('currentPositionName', positionName);

                        alert('JD修改成功！');
                    } else {
                        alert('修改JD失败: ' + data.message);
                    }
                })
                .catch(error => {
                    showLoading(false);
                    console.error('Error:', error);
                    alert('网络错误，请重试');
                });
        }

        // 添加修改记录到对话历史
        function addModificationMessage(request, modifiedJD) {
            const modificationDiv = document.createElement('div');
            modificationDiv.className = 'message user';
            modificationDiv.textContent = `修改要求: ${request}`;
            elements.modificationConversationArea.appendChild(modificationDiv);

            const responseDiv = document.createElement('div');
            responseDiv.className = 'message system';
            responseDiv.textContent = 'JD已根据您的要求进行修改。';
            elements.modificationConversationArea.appendChild(responseDiv);

            elements.modificationConversationArea.scrollTop = elements.modificationConversationArea.scrollHeight;
        }

        // 复制最终生成的JD到剪贴板
        function copyFinalJDToClipboard() {
            const jdContent = elements.resultContent.textContent;
            navigator.clipboard.writeText(jdContent)
                .then(() => {
                    alert('JD已复制到剪贴板');
                })
                .catch(err => {
                    console.error('复制失败:', err);
                    alert('复制失败，请手动复制');
                });
        }
    });
</script>
</body>
</html>