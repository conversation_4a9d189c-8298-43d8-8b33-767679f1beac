<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能简历筛选工具 v4.0</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #ffffff;
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 登录页面样式 */
        .login-section {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .login-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 500px;
            text-align: center;
        }

        .login-header {
            margin-bottom: 30px;
        }

        .login-header h1 {
            color: #2563eb;
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .login-header p {
            color: #666;
            font-size: 1rem;
        }

        .login-form {
            margin-top: 30px;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #2563eb;
        }

        .login-btn {
            background: #2563eb;
            color: white;
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }

        .login-btn:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(37, 99, 235, 0.3);
        }

        .boss-login-btn {
            background: #10b981;
            color: white;
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }

        .boss-login-btn:hover {
            background: #059669;
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }

        .login-tabs {
            display: flex;
            margin-bottom: 30px;
            border-bottom: 2px solid #e2e8f0;
        }

        .login-tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .login-tab.active {
            border-bottom-color: #2563eb;
            color: #2563eb;
        }

        .login-tab:hover {
            background: #f8fafc;
        }

        .login-content {
            display: none;
        }

        .login-content.active {
            display: block;
        }

        .boss-login-info {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: left;
        }

        .boss-login-info h4 {
            color: #166534;
            margin-bottom: 10px;
            font-size: 1rem;
        }

        .boss-login-info p {
            color: #166534;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        /* 主应用页面样式 */
        .app-section {
            display: none;
            min-height: 100vh;
        }

        .app-header {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #2563eb;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
            overflow: hidden;
            position: relative;
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }

        .user-avatar .avatar-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
        }

        .user-details h3 {
            color: #333;
            margin-bottom: 5px;
        }

        .user-details p {
            color: #666;
            font-size: 0.9rem;
        }

        .logout-btn {
            background: #ef4444;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            background: #dc2626;
        }

        /* 功能标签 */
        .function-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            padding: 5px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .function-tab {
            flex: 1;
            padding: 12px 20px;
            text-align: center;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
            color: #666;
        }

        .function-tab.active {
            background: #2563eb;
            color: white;
        }

        .function-tab:hover:not(.active) {
            background: #f1f5f9;
        }

        /* 主要内容区域 */
        .main-content {
            background: white;
            padding: 30px;
            margin-bottom: 20px;
        }

        .section-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* 两列布局 */
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-top: 20px;
        }

        .content-column {
            background: #f8fafc;
            border-radius: 12px;
            padding: 25px;
        }

        .column-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .column-description {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        /* JD选择区域 */
        .jd-selection {
            margin-bottom: 20px;
        }

        .jd-dropdown {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1rem;
            background: white;
            margin-bottom: 15px;
        }

        .jd-dropdown:focus {
            outline: none;
            border-color: #2563eb;
        }

        .jd-option h4 {
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .jd-option p {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        /* 简历上传区域 */
        .upload-area {
            border: 2px dashed #cbd5e1;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            background: white;
            margin-bottom: 15px;
        }

        .upload-area:hover {
            border-color: #2563eb;
            background: #f8fafc;
        }

        .upload-area.dragover {
            border-color: #2563eb;
            background: #f1f5f9;
        }

        .upload-icon {
            font-size: 2.5rem;
            color: #2563eb;
            margin-bottom: 15px;
        }

        .upload-text {
            font-size: 1rem;
            color: #666;
            margin-bottom: 8px;
        }

        .upload-hint {
            font-size: 0.85rem;
            color: #999;
            line-height: 1.4;
        }

        .file-select-btn {
            background: #2563eb;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
        }

        .file-select-btn:hover {
            background: #1d4ed8;
        }

        /* 查找按钮 */
        .search-btn {
            background: #10b981;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 15px 30px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 30px;
            width: 100%;
        }

        .search-btn:hover {
            background: #059669;
        }

        .search-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }

        .search-btn-disabled-text {
            color: #666;
            font-size: 0.9rem;
            text-align: center;
            margin-top: 10px;
        }

        /* 查找等待页面 */
        .searching-section {
            display: none;
            text-align: center;
            padding: 60px 20px;
        }

        .searching-animation {
            width: 100px;
            height: 100px;
            border: 4px solid #e2e8f0;
            border-top: 4px solid #2563eb;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 30px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .searching-text {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 10px;
        }

        .searching-subtext {
            color: #666;
            font-size: 1rem;
        }

        /* 结果页面 */
        .results-section {
            display: none;
        }

        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .results-summary {
            background: #f8fafc;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #2563eb;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        /* 候选人卡片 */
        .candidates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }

        .candidate-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .candidate-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .candidate-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .candidate-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
        }

        .ai-score {
            background: #2563eb;
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .candidate-info {
            margin-bottom: 15px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .info-label {
            color: #666;
        }

        .info-value {
            color: #333;
            font-weight: 500;
        }

        .candidate-advantages {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 10px;
        }

        .advantages-title {
            font-size: 0.9rem;
            font-weight: 600;
            color: #166534;
            margin-bottom: 5px;
        }

        .advantages-content {
            font-size: 0.85rem;
            color: #166534;
        }

        .candidate-risks {
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 15px;
        }

        .risks-title {
            font-size: 0.9rem;
            font-weight: 600;
            color: #991b1b;
            margin-bottom: 5px;
        }

        .risks-content {
            font-size: 0.85rem;
            color: #991b1b;
        }

        .candidate-actions {
            display: flex;
            gap: 10px;
        }

        .action-btn {
            flex: 1;
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            font-size: 0.85rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-detail {
            background: #2563eb;
            color: white;
        }

        .btn-contact {
            background: #10b981;
            color: white;
        }

        .btn-detail:hover {
            background: #1d4ed8;
        }

        .btn-contact:hover {
            background: #059669;
        }

        /* 候选人详情模态框 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 20px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        /* 通知样式 */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            padding: 15px 20px;
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            border-left: 4px solid #2563eb;
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        .notification-success {
            border-left-color: #10b981;
        }
        
        .notification-error {
            border-left-color: #ef4444;
        }
        
        .notification-info {
            border-left-color: #2563eb;
        }
        
        .notification-content {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .notification-content i {
            font-size: 1.2rem;
        }
        
        .notification-success i {
            color: #10b981;
        }
        
        .notification-error i {
            color: #ef4444;
        }
        
        .notification-info i {
            color: #2563eb;
        }
        
        /* 导出按钮状态样式 */
        .search-btn.exporting {
            background: #f59e0b !important;
            cursor: not-allowed;
        }
        
        .search-btn.export-complete {
            background: #10b981 !important;
            animation: pulse 0.5s ease-in-out;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
        }

        .close-btn:hover {
            color: #333;
        }

        /* 创建新职位页面样式 */
        .create-jd-section {
            display: none;
        }

        .jd-form {
            max-width: 800px;
            margin: 0 auto;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-row.full-width {
            grid-template-columns: 1fr;
        }

        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            resize: vertical;
            min-height: 120px;
        }

        .form-group textarea:focus {
            outline: none;
            border-color: #2563eb;
        }

        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            background: white;
        }

        .form-group select:focus {
            outline: none;
            border-color: #2563eb;
        }

        .auto-fill-btn {
            background: #f59e0b;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
        }

        .auto-fill-btn:hover {
            background: #d97706;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .required-field::after {
            content: ' *';
            color: #ef4444;
        }



        /* 平台管理图标 */
        .platform-manager {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .platform-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #2563eb;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
            transition: all 0.3s ease;
        }

        .platform-icon:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
        }

        .platform-icon.bound {
            background: #10b981;
        }

        .platform-icon.unbound {
            background: #ef4444;
        }

        /* 平台管理弹窗 */
        .platform-modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .platform-modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 20px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .platform-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .platform-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
        }

        .platform-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .platform-card {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .platform-card:hover {
            border-color: #2563eb;
            transform: translateY(-2px);
        }

        .platform-card.bound {
            border-color: #10b981;
            background: #f0fdf4;
        }

        .platform-card.unbound {
            border-color: #ef4444;
            background: #fef2f2;
        }

        .platform-logo {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .platform-name {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .platform-status {
            font-size: 0.8rem;
            padding: 4px 8px;
            border-radius: 12px;
            margin-top: 5px;
        }

        .status-bound {
            background: #10b981;
            color: white;
        }

        .status-unbound {
            background: #ef4444;
            color: white;
        }

        /* 筛选历史 */
        .history-section {
            display: none;
        }

        .history-title-section {
            border-bottom: 2px solid #e2e8f0;
            margin-bottom: 20px;
            padding-bottom: 15px;
        }

        .history-title-section h3 {
            margin: 0;
            color: #1f2937;
            font-size: 1.25rem;
            font-weight: 600;
        }

        .history-title-section i {
            color: #2563eb;
            margin-right: 8px;
        }

        .history-content {
            display: block;
        }

        .history-list {
            max-height: 500px;
            overflow-y: auto;
        }

        .history-item {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .history-item:hover {
            background: #f1f5f9;
            border-color: #2563eb;
        }

        .history-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .history-title {
            font-weight: 600;
            color: #333;
        }

        .history-date {
            font-size: 0.8rem;
            color: #666;
        }

        .history-stats {
            display: flex;
            gap: 20px;
            font-size: 0.9rem;
            color: #666;
        }

        /* 人才库 */
        .talent-section {
            display: none;
        }

        .talent-title-section {
            border-bottom: 2px solid #e2e8f0;
            margin-bottom: 20px;
            padding-bottom: 15px;
            text-align: center;
        }

        .talent-title-section h3 {
            margin: 0 0 10px 0;
            color: #1f2937;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .talent-title-section p {
            margin: 0;
            color: #6b7280;
            font-size: 1rem;
        }

        .talent-title-section i {
            color: #2563eb;
            margin-right: 8px;
        }

        .talent-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .talent-filters {
            display: flex;
            gap: 15px;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8fafc;
            border-radius: 12px;
            flex-wrap: wrap;
        }

        .talent-filters select,
        .talent-filters input {
            padding: 10px 15px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            background: white;
            min-width: 150px;
        }

        .talent-filters input {
            flex: 1;
            min-width: 200px;
        }

        .talent-positions {
            margin-bottom: 30px;
        }

        .position-card {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 16px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .position-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .position-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .position-title h3 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1e293b;
            margin: 0 0 5px 0;
        }

        .position-stats {
            display: flex;
            gap: 20px;
            color: #64748b;
            font-size: 0.9rem;
        }

        .candidates-container {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
        }

        .candidate-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin: 10px 0;
            background: #f8fafc;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
            transition: all 0.3s ease;
        }

        .candidate-row:hover {
            background: #f1f5f9;
            transform: translateX(5px);
        }

        .candidate-info {
            flex: 1;
        }

        .candidate-name {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 5px;
        }

        .candidate-details {
            color: #64748b;
            font-size: 0.9rem;
        }

        .candidate-score {
            text-align: center;
            margin: 0 20px;
        }

        .candidate-score-number {
            font-size: 1.2rem;
            font-weight: 600;
            color: #1e293b;
        }

        .candidate-score-label {
            color: #64748b;
            font-size: 0.8rem;
        }

        .candidate-status {
            margin: 0 20px;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-contacted {
            background: #d1fae5;
            color: #065f46;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        .candidate-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.8rem;
            border-radius: 6px;
        }

        .talent-empty {
            text-align: center;
            padding: 60px 20px;
            color: #64748b;
        }

        .empty-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .empty-desc {
            font-size: 1rem;
            opacity: 0.8;
        }

        .talent-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }

        /* JD生成弹窗 */
        .jd-generator-modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .jd-generator-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 20px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .jd-generator-form {
            margin-top: 20px;
        }

        .jd-generator-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1rem;
            margin-bottom: 20px;
            resize: vertical;
            min-height: 120px;
        }

        .jd-generator-input:focus {
            outline: none;
            border-color: #2563eb;
        }

        /* 步骤指示器样式 */
        .step-indicator {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            background: #f1f5f9;
            color: #64748b;
            transition: all 0.3s ease;
        }

        .step-indicator.active {
            background: #2563eb;
            color: white;
        }

        .step-indicator.completed {
            background: #10b981;
            color: white;
        }

        /* 步骤内容样式 */
        .step-content {
            display: none;
        }

        .step-content.active {
            display: block;
        }

        /* 对话区域样式 */
        .conversation-message {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 8px;
            max-width: 80%;
        }

        .conversation-message.assistant {
            background: #eff6ff;
            border-left: 4px solid #2563eb;
            margin-right: auto;
        }

        .conversation-message.user {
            background: #f0fdf4;
            border-left: 4px solid #10b981;
            margin-left: auto;
        }

        .conversation-message.system {
            background: #fef3c7;
            border-left: 4px solid #f59e0b;
            margin: 10px auto;
            text-align: center;
            font-style: italic;
        }

        /* 选项按钮样式 */
        .option-btn {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: left;
            font-size: 0.9rem;
        }

        .option-btn:hover {
            background: #eff6ff;
            border-color: #2563eb;
            transform: translateY(-1px);
        }

        .resume-detail {
            line-height: 1.6;
            color: #333;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .app-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .content-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }



            .candidates-grid {
                grid-template-columns: 1fr;
            }

            .candidate-actions {
                flex-direction: column;
            }

            .form-row {
                grid-template-columns: 1fr;
            }
        }

        /* 岗位建议区域样式 */
        .position-advice-area {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }

        .advice-content {
            margin-bottom: 20px;
        }

        .advice-title {
            color: #007bff;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .advice-text {
            color: #333;
            line-height: 1.6;
            font-size: 14px;
        }

        .advice-text h3 {
            color: #007bff;
            font-size: 16px;
            font-weight: 600;
            margin: 15px 0 8px 0;
        }

        .advice-text h4 {
            color: #495057;
            font-size: 14px;
            font-weight: 600;
            margin: 12px 0 6px 0;
        }

        .advice-text p {
            margin: 8px 0;
        }

        .advice-text ul {
            margin: 8px 0;
            padding-left: 20px;
        }

        .advice-text li {
            margin: 4px 0;
        }

        .advice-text strong {
            color: #007bff;
            font-weight: 600;
        }

        .advice-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        /* 对话消息样式 */
        .conversation-message {
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 8px;
            max-width: 80%;
            word-wrap: break-word;
            font-size: 14px;
            line-height: 1.5;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .conversation-message.system {
            background: #f8f9fa;
            color: #495057;
            border-left: 4px solid #6c757d;
            margin-right: auto;
            border: 1px solid #e9ecef;
        }

        .conversation-message.user {
            background: #e3f2fd;
            color: #1976d2;
            border-left: 4px solid #2196f3;
            margin-left: auto;
            text-align: right;
            border: 1px solid #bbdefb;
        }

        .conversation-message.assistant {
            background: #f3e5f5;
            color: #7b1fa2;
            border-left: 4px solid #9c27b0;
            margin-right: auto;
            border: 1px solid #e1bee7;
        }

        /* 选项按钮样式 */
        .option-btn {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: left;
            font-size: 14px;
        }

        .option-btn:hover {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        /* 选项项目样式 */
        .option-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 12px 16px;
            color: #495057;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .option-item:hover {
            background: #e3f2fd;
            border-color: #2196f3;
            color: #1976d2;
            transform: translateY(-1px);
            box-shadow: 0 2px 6px rgba(33, 150, 243, 0.2);
        }

        .option-item:active {
            transform: translateY(0);
        }

        /* 步骤指示器样式 */
        .step-indicator {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }

        .step-indicator.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .step-indicator.completed {
            background: #28a745;
            color: white;
            border-color: #28a745;
        }

        /* 用户消息分点显示样式 */
        .user-point {
            margin: 5px 0;
            position: relative;
            padding-left: 5px;
        }

        .user-point:not(:last-child) {
            margin-bottom: 8px;
        }

        /* 系统消息总结样式 */
        .summary-message {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 12px;
            margin-top: 10px;
        }

        .summary-item {
            margin: 10px 0;
        }

        .summary-item strong {
            color: #007bff;
            font-weight: 600;
        }

        .summary-message ul {
            margin: 8px 0 12px 0;
            padding-left: 20px;
        }

        .summary-message li {
            margin: 5px 0;
            line-height: 1.4;
        }

        /* 编辑按钮样式 */
        .edit-btn {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .edit-btn:hover {
            background: #e9ecef;
            border-color: #adb5bd;
        }

        .edit-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        /* 修改对话区域样式 */
        #modificationConversationArea .conversation-message {
            margin-bottom: 10px;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        #modificationConversationArea .conversation-message.user {
            background: #e3f2fd;
            color: #1976d2;
            border-left: 3px solid #2196f3;
            margin-left: 20px;
            border: 1px solid #bbdefb;
        }

        #modificationConversationArea .conversation-message.system {
            background: #f8f9fa;
            color: #495057;
            border-left: 3px solid #6c757d;
            margin-right: 20px;
            border: 1px solid #e9ecef;
        }

        /* 拖拽列表样式 */
        .draggable-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        /* 拖拽项样式 */
        .draggable-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 12px 16px;
            margin-bottom: 8px;
            cursor: grab;
            transition: all 0.2s ease;
            position: relative;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .draggable-item:hover {
            background: #e9ecef;
            transform: translateY(-1px);
        }

        .draggable-item.dragging {
            opacity: 0.5;
            cursor: grabbing;
        }

        .draggable-item .drag-handle {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 10px;
            background: #dee2e6;
            border-radius: 8px 0 0 8px;
            cursor: grab;
        }

        .draggable-item .drag-handle:hover {
            background: #adb5bd;
        }

        .draggable-item .item-content {
            margin-left: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;
        }

        .draggable-item .item-number {
            display: inline-block;
            width: 24px;
            height: 24px;
            line-height: 24px;
            text-align: center;
            background: #007bff;
            color: white;
            border-radius: 50%;
            margin-right: 8px;
            font-size: 12px;
            font-weight: 600;
            flex-shrink: 0;
        }

        .draggable-item .item-text {
            flex: 1;
            word-break: break-word;
        }

        .draggable-item .edit-item-btn,
        .draggable-item .delete-item-btn {
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
            font-size: 14px;
            flex-shrink: 0;
        }

        .draggable-item .edit-item-btn:hover {
            background: #007bff;
            color: white;
        }

        .draggable-item .delete-item-btn:hover {
            background: #dc3545;
            color: white;
        }

        .draggable-item .edit-input {
            background: #f8f9fa;
            border: 1px solid #007bff;
            color: #333;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 14px;
            width: 100%;
            outline: none;
        }

        .draggable-item .edit-input:focus {
            background: #fff;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        /* 表单样式 */
        .form-label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        /* 步骤指示器样式 */
        .step-indicator {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }

        .step-indicator.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .step-indicator.completed {
            background: #28a745;
            color: white;
            border-color: #28a745;
        }

        .step-indicator:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* Boss直聘二维码登录弹窗样式 */
        .qr-code-content {
            padding: 20px 0;
        }

        .qr-code-container {
            margin-bottom: 25px;
            position: relative;
        }

        .qr-code-image {
            display: none;
            text-align: center;
        }

        .qr-code-image img {
            max-width: 200px;
            height: auto;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .qr-code-loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
            padding: 40px 20px;
            color: #666;
        }

        .qr-code-loading i {
            font-size: 2rem;
            color: #2563eb;
        }

        .qr-code-loading p {
            margin: 0;
            font-size: 1rem;
        }

        .qr-code-instructions {
            margin-bottom: 25px;
            text-align: left;
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }

        .qr-code-instructions h4 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.1rem;
            text-align: center;
        }

        .qr-code-instructions ol {
            margin: 0;
            padding-left: 20px;
            color: #666;
            line-height: 1.6;
        }

        .qr-code-instructions li {
            margin-bottom: 8px;
        }

        .qr-code-status {
            margin-bottom: 25px;
            padding: 15px;
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            color: #1e40af;
        }

        .qr-code-status p {
            margin: 0;
            font-weight: 500;
        }

        .qr-code-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .qr-code-actions .btn-secondary {
            padding: 10px 20px;
            font-size: 0.9rem;
        }

        /* 登录成功状态样式 */
        .qr-code-status.success {
            background: #f0fdf4;
            border-color: #bbf7d0;
            color: #166534;
        }

        .qr-code-status.error {
            background: #fef2f2;
            border-color: #fecaca;
            color: #991b1b;
        }

        .qr-code-status.processing {
            background: #fef3c7;
            border-color: #fbbf24;
            color: #92400e;
        }
    </style>
</head>
<body>
    <!-- 登录页面 -->
    <div id="loginSection" class="login-section">
        <div class="login-card">
            <div class="login-header">
                <h1><i class="fas fa-user-tie"></i> 智能简历筛选工具</h1>
                <p>登录您的账号，开始智能筛选简历</p>
            </div>
            
            <div class="login-tabs">
                                    <div class="login-tab active" onclick="switchLoginTab('normal', event)">普通登录</div>
                    <div class="login-tab" onclick="switchLoginTab('boss', event)">Boss直聘登录</div>
            </div>
            
            <!-- 普通登录 -->
            <div id="normalLogin" class="login-content active">
                <form class="login-form" onsubmit="normalLogin(event)">
                    <div class="form-group">
                        <label for="loginUsername">用户名</label>
                        <input type="text" id="loginUsername" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="password">密码</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    <button type="submit" class="login-btn">
                        <i class="fas fa-sign-in-alt"></i> 登录
                    </button>
                </form>
            </div>
            
            <!-- Boss直聘登录 -->
            <div id="bossLogin" class="login-content">
                <div class="boss-login-info">
                    <h4><i class="fas fa-info-circle"></i> Boss直聘登录说明</h4>
                    <p>• 使用您的Boss直聘企业账号登录</p>
                    <p>• 系统将自动获取您平台上的招聘岗位信息</p>
                    <p>• 支持一键同步岗位数据，提高筛选效率</p>
                    <p>• 登录后可直接联系候选人，无缝对接招聘流程</p>
                </div>
                <button class="boss-login-btn" onclick="loginWithBoss()">
                    <i class="fab fa-boss"></i> 使用Boss直聘账号登录
                </button>
            </div>
        </div>
    </div>

    <!-- 主应用页面 -->
    <div id="appSection" class="app-section">
        <div class="container">
            <!-- 应用头部 -->
            <div class="app-header">
                <div class="user-info">
                                    <div class="user-avatar" id="userAvatar">
                    <div class="avatar-text" id="avatarText">
                        <i class="fas fa-user"></i>
                    </div>
                </div>
                    <div class="user-details">
                        <h3 id="displayUserName">用户名称</h3>
                        <p id="userCompany">公司名称</p>
                    </div>
                </div>
                <div class="header-actions">
                    <button class="logout-btn" onclick="logout()">
                        <i class="fas fa-sign-out-alt"></i> 退出登录
                    </button>
                </div>
            </div>

            <!-- 功能标签 -->
            <div class="function-tabs">
                <div class="function-tab active" onclick="switchFunctionTab('screening')">
                    <i class="fas fa-search"></i> 智能筛选
                </div>
                <div class="function-tab" onclick="switchFunctionTab('history')">
                    <i class="fas fa-history"></i> 筛选历史
                </div>
                <div class="function-tab" onclick="switchFunctionTab('talent')">
                    <i class="fas fa-users"></i> 人才库
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content" id="mainContent">


                <!-- JD选择页面 -->
                <div id="jdSelectionSection">
                    <div class="content-grid">
                        <!-- 左侧：选择职位 -->
                        <div class="content-column">
                            <div class="column-title">
                                <i class="fas fa-briefcase"></i> 选择职位
                            </div>
                            <div class="column-description">
                                从已发布的职位中选择，或创建新的职位需求
                            </div>
                            <div class="jd-selection">
                                <select class="jd-dropdown" id="jdDropdown" onchange="selectJD()">
                                    <option value="">选择已有职位</option>
                                </select>
                                <button class="file-select-btn" onclick="showCreateJDOptions()">
                                    <i class="fas fa-plus"></i> 创建新职位需求
                                </button>
                            </div>
                        </div>

                        <!-- 右侧：上传简历 -->
                        <div class="content-column">
                            <div class="column-title">
                                <i class="fas fa-upload"></i> 上传简历 (可选)
                            </div>
                            <div class="column-description">
                                您可以上传本地简历文件，或者直接开始筛选让系统自动抓取相关候选人
                            </div>
                            <div class="upload-area" id="uploadArea" onclick="triggerFileUpload()">
                                <div class="upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <div class="upload-text">拖拽简历文件到此处或点击上传</div>
                                <div class="upload-hint">支持PDF、Word、TXT、图片格式，最大10MB，如不上传，系统将自动从招聘平台抓取候选人</div>
                                <button type="button" class="file-select-btn" onclick="event.stopPropagation(); triggerFileUpload()">
                                    选择文件
                                </button>
                            </div>
                            <input type="file" id="fileInput" multiple accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.bmp,.gif" style="display: none;">
                            <div id="fileList"></div>
                        </div>
                    </div>

                    
                    <!-- 爬取数量选择 -->
                    <div style="margin-bottom: 15px; display: flex; align-items: center; gap: 10px;">
                        <label for="crawlCount" style="font-weight: 600; color: #333; min-width: 80px;">爬取数量:</label>
                        <select id="crawlCount" style="padding: 8px 12px; border: 1px solid #e2e8f0; border-radius: 6px; background: white; font-size: 14px;">
                            <option value="5">5份</option>
                            <option value="10">10份</option>
                            <option value="20" selected>20份</option>
                            <option value="30">30份</option>
                            <option value="50">50份</option>
                            <option value="100">100份</option>
                        </select>
                    </div>



                    <!-- 开始筛选按钮 -->
                    <button class="search-btn" id="startCrawlBtn" onclick="startCrawlResumes()" style="background: #f59e0b; margin-bottom: 15px;">
                        <i class="fas fa-spider"></i> 开始爬取简历
                    </button>
                    <div class="search-btn-disabled-text" id="crawlDisabledText">请先选择职位</div>
                    
                    <button class="search-btn" id="startSearchBtn" onclick="startSearch()" disabled>
                        <i class="fas fa-search"></i> 开始智能筛选
                    </button>
                    <div class="search-btn-disabled-text" id="disabledText">请先选择职位</div>
                </div>

                <!-- 筛选历史页面 -->
                <div id="historySection" class="history-section">
                    <div class="history-title-section">
                        <h3><i class="fas fa-history"></i> 筛选历史</h3>
                    </div>
                    
                    <div class="history-content">
                        <div class="history-list" id="allHistoryList">
                            <!-- 筛选历史将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>

                <!-- 创建新职位页面 -->
                <div id="createJDSection" class="create-jd-section">
                    <h2 class="section-title">
                        <i class="fas fa-plus-circle"></i> 创建新职位需求
                    </h2>
                    <form class="jd-form" id="jdForm" onsubmit="submitJD(event)">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="jobTitle" class="required-field">岗位名称</label>
                                <input type="text" id="jobTitle" name="jobTitle" required>
                            </div>
                            <div class="form-group">
                                <label for="jobEducation">学历要求</label>
                                <select id="jobEducation" name="education">
                                    <option value="">不限</option>
                                    <option value="高中">高中</option>
                                    <option value="大专">大专</option>
                                    <option value="本科">本科</option>
                                    <option value="硕士">硕士</option>
                                    <option value="博士">博士</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="workDuration">工作时长要求</label>
                                <select id="workDuration" name="workDuration">
                                    <option value="">不限</option>
                                    <option value="全职">全职</option>
                                    <option value="兼职">兼职</option>
                                    <option value="实习">实习</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="experience">工作经验要求</label>
                                <select id="experience" name="experience">
                                    <option value="">不限</option>
                                    <option value="应届生">应届生</option>
                                    <option value="1-3年">1-3年</option>
                                    <option value="3-5年">3-5年</option>
                                    <option value="5-10年">5-10年</option>
                                    <option value="10年以上">10年以上</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-row full-width">
                            <div class="form-group">
                                <label for="jdContent" class="required-field">JD信息</label>
                                <textarea id="jdContent" name="jdContent" placeholder="请详细描述岗位职责、任职要求等信息..." required></textarea>
                                <button type="button" class="auto-fill-btn" onclick="autoFillJD()">
                                    <i class="fas fa-magic"></i> 一键智能识别填入
                                </button>
                            </div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="button" class="btn-secondary" onclick="backToJDSelection()">
                                <i class="fas fa-arrow-left"></i> 返回
                            </button>
                            <button type="submit" class="search-btn">
                                <i class="fas fa-save"></i> 创建职位
                            </button>
                        </div>
                    </form>
                </div>

                <!-- 人才库页面 -->
                <div id="talentSection" class="talent-section">
                    <div class="talent-title-section">
                        <h3><i class="fas fa-users"></i> 人才库</h3>
                        <p>管理通过筛选的候选人信息，按职位分类存储</p>
                    </div>
                    
                    <!-- 统计信息 -->
                    <div class="talent-stats">
                        <div class="stat-card">
                            <div class="stat-number" id="totalCandidates">0</div>
                            <div class="stat-label">总候选人</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="contactedCandidates">0</div>
                            <div class="stat-label">已联系</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="pendingCandidates">0</div>
                            <div class="stat-label">待联系</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="totalPositions">0</div>
                            <div class="stat-label">职位数量</div>
                        </div>
                    </div>

                    <!-- 筛选条件 -->
                    <div class="talent-filters">
                        <select id="positionFilter" onchange="filterTalentPool()">
                            <option value="">全部职位</option>
                        </select>
                        <select id="contactFilter" onchange="filterTalentPool()">
                            <option value="">全部状态</option>
                            <option value="contacted">已联系</option>
                            <option value="pending">待联系</option>
                        </select>
                        <input id="talentSearchInput" oninput="filterTalentPool()" placeholder="🔍 搜索候选人姓名..." type="text">
                    </div>

                    <!-- 职位列表 -->
                    <div class="talent-positions" id="talentPositionsContainer">
                        <!-- 职位卡片将在这里动态生成 -->
                    </div>

                    <!-- 空状态 -->
                    <div class="talent-empty" id="talentEmptyState" style="display: none;">
                        <div class="empty-icon">👥</div>
                        <div class="empty-title">暂无通过评估的候选人</div>
                        <div class="empty-desc">当前没有符合要求的候选人，请继续筛选简历或调整评估标准</div>
                    </div>

                    <!-- 管理按钮 -->
                    <div class="talent-actions">
                        <button class="btn-primary" onclick="loadTalentPoolData()">
                            <i class="fas fa-sync-alt"></i> 刷新数据
                        </button>
                        <button class="btn-secondary" onclick="exportTalentPool()">
                            <i class="fas fa-download"></i> 导出数据
                        </button>
                        <button class="btn-secondary" onclick="importTalentPool()">
                            <i class="fas fa-upload"></i> 导入数据
                        </button>
                        <button class="btn-secondary" onclick="clearTalentPool()">
                            <i class="fas fa-trash"></i> 清空数据
                        </button>
                    </div>
                </div>


            </div>

            <!-- 查找等待页面 -->
            <div id="searchingSection" class="searching-section">
                <div class="searching-animation"></div>
                <div class="searching-text">正在智能筛选简历...</div>
                <div class="searching-subtext">AI正在分析简历内容，请稍候</div>
            </div>

            <!-- 结果页面 -->
            <div id="resultsSection" class="results-section">
                <div class="results-header">
                    <h2 class="section-title">
                        <i class="fas fa-chart-bar"></i> 筛选结果
                    </h2>
                    <button class="search-btn" onclick="exportResults()" style="width: auto;">
                        <i class="fas fa-download"></i> 导出结果
                    </button>
                </div>

                <div class="results-summary">
                    <div class="summary-stats">
                        <div class="stat-item">
                            <div class="stat-number" id="totalCandidates">0</div>
                            <div class="stat-label">总候选人</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="qualifiedCandidates">0</div>
                            <div class="stat-label">符合要求</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="otherCandidates">0</div>
                            <div class="stat-label">其他候选人</div>
                        </div>
                    </div>
                </div>

                <div class="candidates-grid" id="candidatesGrid">
                    <!-- 候选人卡片将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 平台管理图标 -->
    <div class="platform-manager">
        <div class="platform-icon unbound" id="platformIcon" onclick="showPlatformModal()">
            <i class="fas fa-link"></i>
        </div>
    </div>

    <!-- 平台管理弹窗 -->
    <div id="platformModal" class="platform-modal">
        <div class="platform-modal-content">
            <div class="platform-header">
                <h3 class="platform-title">招聘平台管理</h3>
                <button class="close-btn" onclick="closePlatformModal()">&times;</button>
            </div>
            <div class="platform-grid" id="platformGrid">
                <!-- 平台卡片将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <!-- JD生成弹窗 -->
    <div id="jdGeneratorModal" class="jd-generator-modal">
        <div class="jd-generator-content" style="max-width: 1200px; width: 95%;">
            <div class="modal-header">
                <h3 class="modal-title">智能JD生成</h3>
                <button class="close-btn" onclick="closeJDGeneratorModal()">&times;</button>
            </div>
            
            <!-- 步骤指示器 -->
            <div style="display: flex; justify-content: center; margin-bottom: 20px;">
                <div style="display: flex; gap: 8px; align-items: center; flex-wrap: wrap;">
                    <div id="step1" class="step-indicator active">1. 岗位建议</div>
                    <div style="width: 20px; height: 2px; background: #e2e8f0;"></div>
                    <div id="step2" class="step-indicator">2. 基本信息</div>
                    <div style="width: 20px; height: 2px; background: #e2e8f0;"></div>
                    <div id="step3" class="step-indicator">3. 对话收集</div>
                    <div style="width: 20px; height: 2px; background: #e2e8f0;"></div>
                    <div id="step4" class="step-indicator">4. 信息总结</div>
                    <div style="width: 20px; height: 2px; background: #e2e8f0;"></div>
                    <div id="step5" class="step-indicator">5. 生成JD</div>
                </div>
            </div>
            
            <!-- 步骤1：岗位建议 -->
            <div id="step1Content" class="step-content active">
                <div style="margin-bottom: 20px;">
                    <h4 style="margin-bottom: 10px;">请描述您需要招聘的岗位：</h4>
                    <textarea class="jd-generator-input" id="positionDescription" placeholder="例如：我们需要招聘一名AI算法工程师，负责大模型应用落地，月薪15-20k，要求3年以上经验..."></textarea>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn-secondary" onclick="closeJDGeneratorModal()">取消</button>
                    <button type="button" class="search-btn" onclick="getPositionAdvice()">
                        <i class="fas fa-lightbulb"></i> 获取岗位建议
                    </button>
                    <button type="button" class="btn-secondary" onclick="testPositionAdvice()" style="background: #f59e0b;">
                        <i class="fas fa-bug"></i> 测试
                    </button>
                </div>
            </div>
            
            <!-- 步骤2：基本信息配置 -->
            <div id="step2Content" class="step-content">
                <div style="margin-bottom: 20px;">
                    <h4 style="margin-bottom: 15px;">配置岗位基本信息</h4>
                    <p style="color: #666; margin-bottom: 20px;">请填写岗位的基本信息，这些信息将用于生成JD</p>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div class="form-group">
                            <label class="form-label">薪资范围 *</label>
                            <input type="text" id="salaryRange" class="jd-generator-input" placeholder="例如：15-25K">
                        </div>
                        <div class="form-group">
                            <label class="form-label">岗位性质 *</label>
                            <select id="jobType" class="jd-generator-input">
                                <option value="">请选择岗位性质</option>
                                <option value="全职">全职</option>
                                <option value="兼职">兼职</option>
                                <option value="实习">实习</option>
                                <option value="外包">外包</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">学历要求 *</label>
                            <select id="educationLevel" class="jd-generator-input">
                                <option value="">请选择学历要求</option>
                                <option value="不限">不限</option>
                                <option value="专科">专科</option>
                                <option value="本科">本科</option>
                                <option value="硕士">硕士</option>
                                <option value="博士">博士</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">年龄要求</label>
                            <input type="text" id="ageRange" class="jd-generator-input" placeholder="例如：25-35岁">
                        </div>
                        <div class="form-group">
                            <label class="form-label">工作经验 *</label>
                            <select id="workExperience" class="jd-generator-input">
                                <option value="">请选择工作经验</option>
                                <option value="应届毕业生">应届毕业生</option>
                                <option value="1年以下">1年以下</option>
                                <option value="1-3年">1-3年</option>
                                <option value="3-5年">3-5年</option>
                                <option value="5-8年">5-8年</option>
                                <option value="8年以上">8年以上</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">工作地点 *</label>
                            <input type="text" id="workLocation" class="jd-generator-input" placeholder="例如：北京市朝阳区">
                        </div>
                        <div class="form-group">
                            <label class="form-label">工作方式 *</label>
                            <select id="workMode" class="jd-generator-input">
                                <option value="">请选择工作方式</option>
                                <option value="现场办公">现场办公</option>
                                <option value="远程办公">远程办公</option>
                                <option value="混合办公">混合办公</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">专业要求</label>
                            <input type="text" id="majorRequirement" class="jd-generator-input" placeholder="例如：计算机相关专业优先">
                        </div>
                    </div>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn-secondary" onclick="showStep(1)">上一步</button>
                    <button type="button" class="search-btn" onclick="saveBasicInfoAndProceed()">
                        <i class="fas fa-arrow-right"></i> 保存并开始对话
                    </button>
                    <button type="button" class="btn-secondary" onclick="skipBasicInfoAndProceed()">
                        <i class="fas fa-forward"></i> 跳过配置
                    </button>
                </div>
            </div>
            
            <!-- 步骤3：对话收集 -->
            <div id="step3Content" class="step-content">
                <div style="margin-bottom: 20px;">
                    <h4 style="margin-bottom: 10px;">AI助手将帮您收集岗位详细信息：</h4>
                    
                    <!-- 对话区域 -->
                    <div id="conversationArea" style="height: 400px; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; overflow-y: auto; background: #f8fafc; margin-bottom: 15px;"></div>
                    
                    <!-- 选项区域 -->
                    <div id="optionsArea" style="margin-bottom: 15px;"></div>
                    
                    <!-- 输入区域 -->
                    <div style="display: flex; gap: 10px; align-items: flex-end;">
                        <textarea id="userInput" placeholder="请输入您的回答..." style="flex: 1; padding: 12px; border: 1px solid #e2e8f0; border-radius: 6px; min-height: 60px; resize: vertical; font-size: 14px; line-height: 1.4;"></textarea>
                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <button type="button" class="search-btn" onclick="sendUserInput()" style="width: auto; padding: 12px 24px; font-size: 14px; border-radius: 6px;">
                                <i class="fas fa-paper-plane"></i> 发送
                            </button>
                            <button type="button" class="btn-secondary" onclick="generateJD()" style="width: auto; padding: 12px 24px; font-size: 14px; border-radius: 6px;">
                                <i class="fas fa-magic"></i> 生成JD
                            </button>
                        </div>
                    </div>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn-secondary" onclick="showStep(2)">上一步</button>
                    <button type="button" class="btn-secondary" onclick="resetConversation()">重新开始</button>
                </div>
            </div>
            
            <!-- 步骤4：信息总结 -->
            <div id="step4Content" class="step-content">
                <div style="margin-bottom: 20px;">
                    <h4 style="margin-bottom: 10px;">信息总结与确认</h4>
                    <p style="color: #666; margin-bottom: 20px;">请核对以下信息，确认无误后生成JD</p>
                    
                    <!-- 基本信息显示 -->
                    <div id="basicInfoDisplay" style="margin-bottom: 20px;">
                        <!-- 基本信息将动态显示 -->
                    </div>
                    
                    <!-- 核心职责排序区域 -->
                    <div id="coreResponsibilitiesSection" style="margin-bottom: 20px;">
                        <h5 style="margin-bottom: 10px; color: #333; font-size: 16px; font-weight: 600;">核心职责（按重要性排序，可拖拽调整顺序）</h5>
                        <div id="responsibilitiesList" class="draggable-list">
                            <!-- 职责项将动态生成 -->
                        </div>
                    </div>

                    <!-- 必备技能排序区域 -->
                    <div id="coreSkillsSection" style="margin-bottom: 20px;">
                        <h5 style="margin-bottom: 10px; color: #333; font-size: 16px; font-weight: 600;">核心技能（按重要性排序，可拖拽调整顺序）</h5>
                        <div id="skillsList" class="draggable-list">
                            <!-- 技能项将动态生成 -->
                        </div>
                    </div>

                    <!-- 优先技能排序区域 -->
                    <div id="prioritySkillsSection" style="margin-bottom: 20px;">
                        <h5 style="margin-bottom: 10px; color: #333; font-size: 16px; font-weight: 600;">拓展技能（按重要性排序，可拖拽调整顺序）</h5>
                        <div id="prioritySkillsList" class="draggable-list">
                            <!-- 优先技能项将动态生成 -->
                        </div>
                    </div>

                    <!-- 加分项排序区域 -->
                    <div id="bonusSkillsSection" style="margin-bottom: 20px;">
                        <h5 style="margin-bottom: 10px; color: #333; font-size: 16px; font-weight: 600;">加分项（按重要性排序，可拖拽调整顺序）</h5>
                        <div id="bonusSkillsList" class="draggable-list">
                            <!-- 加分项将动态生成 -->
                        </div>
                    </div>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn-secondary" onclick="showStep(3)">返回修改</button>
                    <button type="button" class="search-btn" onclick="confirmAndGenerateJD()">
                        <i class="fas fa-check"></i> 确认生成JD
                    </button>
                </div>
            </div>
            
            <!-- 步骤5：JD预览与编辑 -->
            <div id="step5Content" class="step-content">
                <div style="margin-bottom: 20px;">
                    <h4 style="margin-bottom: 10px;">JD预览与编辑：</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; height: 500px;">
                        <!-- 左侧：对话历史 -->
                        <div>
                            <h5 style="margin-bottom: 10px; color: #333; font-size: 16px; font-weight: 600;">对话历史</h5>
                            <div id="modificationConversationArea" style="height: 400px; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; overflow-y: auto; background: #f8fafc; margin-bottom: 10px;"></div>
                            <div style="display: flex; gap: 10px; align-items: flex-end;">
                                <textarea id="modificationInput" placeholder="请输入修改要求，例如：把技能要求中的React改为Vue..." style="flex: 1; padding: 12px; border: 1px solid #e2e8f0; border-radius: 6px; min-height: 60px; resize: vertical; font-size: 14px; line-height: 1.4;"></textarea>
                                <button type="button" class="search-btn" onclick="modifyJD()" style="width: auto; padding: 12px 24px; font-size: 14px; border-radius: 6px;">
                                    <i class="fas fa-edit"></i> 修改
                                </button>
                            </div>
                        </div>
                        
                        <!-- 右侧：JD内容 -->
                        <div>
                            <h5 style="margin-bottom: 10px; color: #333; font-size: 16px; font-weight: 600;">JD内容</h5>
                            <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                                <button type="button" class="edit-btn active" id="viewMarkdownBtn" onclick="toggleJDPreviewMode('markdown')">Markdown预览</button>
                                <button type="button" class="edit-btn" id="viewRawBtn" onclick="toggleJDPreviewMode('raw')">原始编辑</button>
                            </div>
                            <div id="jdPreviewWrapper" style="height: 400px;">
                                <textarea class="jd-generator-input" id="generatedJDContent" style="height: 100%; display: none; padding: 15px; border: 1px solid #e2e8f0; border-radius: 8px; font-size: 14px; line-height: 1.6; resize: none;" readonly></textarea>
                                <div id="markdownPreview" class="jd-generator-input" style="height: 100%; overflow-y: auto; background: white; color: #333; font-family: 'Inter', sans-serif; line-height: 1.6; padding: 15px; border: 1px solid #e2e8f0; border-radius: 8px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-actions">
                    <button type="button" class="search-btn" onclick="confirmJD()">
                        <i class="fas fa-check"></i> 确认使用
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Boss直聘二维码登录弹窗 -->
    <div id="bossQRCodeModal" class="modal">
        <div class="modal-content" style="max-width: 500px; text-align: center;">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fab fa-boss"></i> Boss直聘登录
                </h3>
                <button class="close-btn" onclick="closeBossQRCodeModal()">&times;</button>
            </div>
            <div class="qr-code-content">
                <div class="qr-code-container">
                    <div id="qrCodeImage" class="qr-code-image">
                        <!-- 二维码图片将在这里显示 -->
                    </div>
                    <div class="qr-code-loading" id="qrCodeLoading">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>正在生成二维码...</p>
                    </div>
                </div>
                <div class="qr-code-instructions">
                    <h4>登录步骤</h4>
                    <ol>
                        <li>打开Boss直聘APP</li>
                        <li>点击"扫一扫"功能</li>
                        <li>扫描上方二维码</li>
                        <li>在APP中确认登录</li>
                    </ol>
                </div>
                <div class="qr-code-status" id="qrCodeStatus">
                    <p>等待扫码登录...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 候选人详情模态框 -->
    <div id="candidateModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">候选人详情</h3>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>
            <div class="resume-detail" id="modalContent">
                <!-- 简历详情内容 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentUser = null;
        let selectedJD = null;
        let uploadedFiles = [];
        let platformStatus = {
            boss: false,
            zhaopin: false,
            qiancheng: false,
            liepin: false
        };

        // JD生成流程状态
        let currentStep = 1;
        let conversationData = {
            company_description: '',
            current_round: 0,
            conversationHistory: [],
            isSupplementQuestion: false
        };
        let basicInfo = null;
        let summaryData = null;
        let responsibilities = [];
        let skills = [];
        let prioritySkills = [];
        let bonusSkills = [];

        // API配置
        const JD_GENERATOR_API_BASE = 'http://localhost:8012'; // JD生成器API地址
        const RESUME_EVAL_API_BASE = 'http://localhost:8008';  // 简历评估系统API地址

        // 筛选历史数据
        let screeningHistory = [];

        // 模拟用户数据（登录后从后端获取）
        const mockUser = {
            name: '张经理',
            company: '科技有限公司',
            avatar: '张'
        };

        // JD数据（从后端获取）
        let jdList = [];

        // 搜索结果
        let searchResults = null;

        // 切换登录标签
        function switchLoginTab(type, event) {
            // 更新标签状态
            document.querySelectorAll('.login-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            if (event && event.target) {
                event.target.classList.add('active');
            }
            
            // 更新内容显示
            document.querySelectorAll('.login-content').forEach(content => {
                content.classList.remove('active');
            });
            
            if (type === 'normal') {
                document.getElementById('normalLogin').classList.add('active');
            } else {
                document.getElementById('bossLogin').classList.add('active');
            }
        }

        // 普通登录功能
        async function normalLogin(event) {
            event.preventDefault();
            const username = document.getElementById('loginUsername').value;
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                alert('请输入用户名和密码');
                return;
            }
            
            try {
                // 显示加载状态
                const loginBtn = document.querySelector('.login-btn');
                const originalText = loginBtn.innerHTML;
                loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 登录中...';
                loginBtn.disabled = true;
                
                // 调用后端登录API
                const response = await fetch(`${RESUME_EVAL_API_BASE}/normal_login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        // 登录成功
                        currentUser = {
                            name: username,
                            company: result.user_info?.公司 || '用户公司',
                            avatar: username.charAt(0).toUpperCase()
                        };
                        showApp();
                    } else {
                        alert(`登录失败: ${result.message}`);
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
            } catch (error) {
                console.error('普通登录失败:', error);
                alert(`登录失败: ${error.message}`);
            } finally {
                // 恢复按钮状态
                const loginBtn = document.querySelector('.login-btn');
                loginBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> 登录';
                loginBtn.disabled = false;
            }
        }

        // Boss直聘登录功能
        async function loginWithBoss() {
            try {
                // 显示加载状态
                const bossBtn = document.querySelector('.boss-login-btn');
                const originalText = bossBtn.innerHTML;
                bossBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 正在获取登录二维码...';
                bossBtn.disabled = true;
                
                // 向resume_score.py发送login_boss请求
                const response = await fetch(`${RESUME_EVAL_API_BASE}/login_boss`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'login_boss'
                    })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        // 显示二维码弹窗
                        showBossQRCodeModal(result.qr_code_url || result.qr_code_data, result.message || '请使用Boss直聘APP扫描二维码登录');
                    } else {
                        throw new Error(result.message || '获取登录二维码失败');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
            } catch (error) {
                console.error('Boss直聘登录失败:', error);
                alert(`Boss直聘登录失败: ${error.message}`);
            } finally {
                // 恢复按钮状态
                const bossBtn = document.querySelector('.boss-login-btn');
                bossBtn.innerHTML = '<i class="fab fa-boss"></i> 使用Boss直聘账号登录';
                bossBtn.disabled = false;
            }
        }

        // 显示主应用
        async function showApp() {
            document.getElementById('loginSection').style.display = 'none';
            document.getElementById('appSection').style.display = 'block';
            
            // 更新用户信息
            document.getElementById('displayUserName').textContent = currentUser.name;
            document.getElementById('userCompany').textContent = currentUser.company;
            updateUserAvatar(currentUser.avatar);
            
            // 异步加载数据
            try {
                await Promise.all([
                    loadJDOptions(),
                    loadHistory()
                ]);
            } catch (error) {
                console.error('加载应用数据失败:', error);
            }
            
            // 加载平台状态
            loadPlatformStatus();
        }

        // 退出登录
        function logout() {
            currentUser = null;
            selectedJD = null;
            uploadedFiles = [];
            searchResults = null;
            
            document.getElementById('appSection').style.display = 'none';
            document.getElementById('loginSection').style.display = 'flex';
            
            // 重置所有状态
            resetApp();
        }

        // 重置应用状态
        function resetApp() {
            document.getElementById('jdSelectionSection').style.display = 'block';
            document.getElementById('historySection').style.display = 'none';
            document.getElementById('createJDSection').style.display = 'none';
            document.getElementById('searchingSection').style.display = 'none';
            document.getElementById('resultsSection').style.display = 'none';
            
            uploadedFiles = [];
            document.getElementById('fileList').innerHTML = '';
            document.getElementById('startSearchBtn').disabled = true;
            updateSearchButton();
            
            // 重置功能标签
            document.querySelectorAll('.function-tab').forEach(t => {
                t.classList.remove('active');
            });
            document.querySelector('.function-tab').classList.add('active');
        }

        // 加载JD选项到下拉框
        async function loadJDOptions() {
            try {
                const jdDropdown = document.getElementById('jdDropdown');
                jdDropdown.innerHTML = '<option value="">选择已有职位</option>';

                const userName = currentUser ? currentUser.name : '默认用户';

                // 从JD库目录获取JD文件列表
                const response = await fetch(`${RESUME_EVAL_API_BASE}/api/jd_list?user_name=${encodeURIComponent(userName)}`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        jdList = data.jd_list || [];
                        
                        jdList.forEach(jd => {
                            const option = document.createElement('option');
                            option.value = jd.id;
                            option.textContent = jd.title;
                            jdDropdown.appendChild(option);
                        });
                    }
                } else {
                    // 如果API不可用，使用默认JD
                    console.warn('无法获取JD列表，使用默认JD');
                    const defaultJD = {
                        id: 'default',
                        title: '默认招聘岗位',
                        description: '使用系统默认JD进行筛选'
                    };
                    jdList = [defaultJD];
                    
                    const option = document.createElement('option');
                    option.value = defaultJD.id;
                    option.textContent = defaultJD.title;
                    jdDropdown.appendChild(option);
                }
            } catch (error) {
                console.error('加载JD选项失败:', error);
                // 使用默认JD
                const defaultJD = {
                    id: 'default',
                    title: '默认招聘岗位',
                    description: '使用系统默认JD进行筛选'
                };
                jdList = [defaultJD];
                
                const jdDropdown = document.getElementById('jdDropdown');
                jdDropdown.innerHTML = '<option value="">选择已有职位</option>';
                const option = document.createElement('option');
                option.value = defaultJD.id;
                option.textContent = defaultJD.title;
                jdDropdown.appendChild(option);
            }
        }

        // 选择JD
        function selectJD() {
            const jdDropdown = document.getElementById('jdDropdown');
            const selectedId = jdDropdown.value;
            
            if (selectedId) {
                selectedJD = jdList.find(jd => jd.id == selectedId);
                updateSearchButton();
            } else {
                selectedJD = null;
                updateSearchButton();
            }
        }

        // 显示创建新职位页面
        function showCreateJD() {
            document.getElementById('jdSelectionSection').style.display = 'none';
            document.getElementById('createJDSection').style.display = 'block';
        }

        // 返回JD选择页面
        function backToJDSelection() {
            document.getElementById('createJDSection').style.display = 'none';
            document.getElementById('jdSelectionSection').style.display = 'block';
        }

        // 一键智能识别填入JD
        function autoFillJD() {
            // 模拟智能识别过程
            setTimeout(() => {
                document.getElementById('jobTitle').value = '前端开发工程师';
                document.getElementById('education').value = '本科';
                document.getElementById('workDuration').value = '全职';
                document.getElementById('experience').value = '3-5年';
                document.getElementById('jdContent').value = `岗位职责：
1. 负责公司产品的前端开发工作，包括PC端和移动端
2. 与产品、设计、后端团队协作，完成产品功能开发
3. 优化前端性能，提升用户体验
4. 参与技术方案设计和技术选型

任职要求：
1. 本科及以上学历，计算机相关专业
2. 3年以上前端开发经验
3. 熟练掌握HTML、CSS、JavaScript
4. 熟悉React、Vue等主流前端框架
5. 了解前端工程化和模块化开发
6. 有良好的代码风格和团队协作能力`;
                
                alert('智能识别完成！已自动填入相关信息，请根据实际情况调整。');
            }, 2000);
        }

        // 提交JD表单
        async function submitJD(event) {
            event.preventDefault();
            
            const jobTitle = document.getElementById('jobTitle').value;
            const jdContent = document.getElementById('jdContent').value;
            
            if (!jobTitle || !jdContent) {
                alert('请填写岗位名称和JD信息');
                return;
            }
            
            try {
                // 保存JD到后端
                const response = await fetch(`${JD_GENERATOR_API_BASE}/save_jd`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        content: jdContent,
                        filename: `${jobTitle}_JD.md`,
                        user_name: currentUser ? currentUser.name : null // 传递用户名
                    })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        alert('职位创建成功！');
                        
                        // 创建新的JD对象
                        const newJD = {
                            id: Date.now(),
                            title: jobTitle,
                            description: jdContent.substring(0, 100) + '...',
                            requirements: '新创建职位'
                        };
                        
                        // 添加到JD列表
                        jdList.push(newJD);
                        
                        // 返回选择页面并刷新
                        backToJDSelection();
                        await loadJDOptions();
                    } else {
                        throw new Error(result.message || '保存JD失败');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                console.error('提交JD失败:', error);
                alert(`职位创建失败: ${error.message}`);
            }
        }

        // 触发文件上传
        function triggerFileUpload() {
            document.getElementById('fileInput').click();
        }

        // 文件上传处理
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const files = Array.from(e.target.files);
            handleFiles(files);
        });

        // 拖拽上传
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            handleFiles(files);
        });

        // 提取候选人姓名
        function extractCandidateName(filename) {
            const nameWithoutExt = filename.replace(/\.[^/.]+$/, '');
            if (nameWithoutExt.includes('_')) {
                return nameWithoutExt.split('_')[0];
            }
            return nameWithoutExt;
        }

        // 处理文件
        function handleFiles(files) {
            files.forEach(file => {
                if (file.size > 10 * 1024 * 1024) {
                    alert(`文件 ${file.name} 超过10MB限制`);
                    return;
                }
                
                if (!uploadedFiles.find(f => f.name === file.name)) {
                    uploadedFiles.push(file);
                }
            });
            
            updateFileList();
            updateSearchButton();
        }

        // 更新文件列表
        function updateFileList() {
            const fileList = document.getElementById('fileList');
            fileList.innerHTML = '';
            
            uploadedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.style.cssText = `
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 10px;
                    background: #f8fafc;
                    border-radius: 8px;
                    margin-bottom: 10px;
                `;
                const candidateName = extractCandidateName(file.name);
                fileItem.innerHTML = `
                    <div style="display: flex; flex-direction: column; align-items: flex-start;">
                        <span style="font-weight: 600; color: #2563eb;">候选人: ${candidateName}</span>
                        <span style="font-size: 0.9rem; color: #666;">${file.name} (${(file.size / 1024 / 1024).toFixed(2)}MB)</span>
                    </div>
                    <button onclick="removeFile(${index})" style="background: #ef4444; color: white; border: none; border-radius: 4px; padding: 5px 10px; cursor: pointer;">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                fileList.appendChild(fileItem);
            });
        }

        // 移除文件
        function removeFile(index) {
            uploadedFiles.splice(index, 1);
            updateFileList();
            updateSearchButton();
        }

        // 更新搜索按钮状态
        function updateSearchButton() {
            const startSearchBtn = document.getElementById('startSearchBtn');
            const startCrawlBtn = document.getElementById('startCrawlBtn');

            const disabledText = document.getElementById('disabledText');
            const crawlDisabledText = document.getElementById('crawlDisabledText');
      
            if (selectedJD) {
                startSearchBtn.disabled = false;
                startCrawlBtn.disabled = false;
                disabledText.textContent = '';
                crawlDisabledText.textContent = '';
            } else {
                startSearchBtn.disabled = true;
                startCrawlBtn.disabled = true;

                disabledText.textContent = '请先选择职位';
                crawlDisabledText.textContent = '请先选择职位';
            }
        }

        // 开始爬取简历
        async function startCrawlResumes() {
            if (!selectedJD) {
                alert('请先选择职位');
                return;
            }
            
            // 显示爬取等待页面
            document.getElementById('jdSelectionSection').style.display = 'none';
            document.getElementById('searchingSection').style.display = 'block';
            document.querySelector('.searching-text').textContent = '正在通知爬虫系统开始爬取简历...';
            document.querySelector('.searching-subtext').textContent = '请等待爬虫系统响应';
            
            try {
                // 调用爬取API
                const response = await fetch(`${RESUME_EVAL_API_BASE}/trigger_crawl`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        job_title: selectedJD.title,
                        job_description: selectedJD.description || '',
                        crawl_count: parseInt(document.getElementById('crawlCount').value), // 从选择框获取爬取数量
                        platforms: ['boss', 'zhaopin', 'qiancheng'] // 支持的平台
                    })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        console.log('爬取任务已启动:', result);
                        
                        // 开始轮询爬取状态
                        pollCrawlStatus(result.task_id);
                    } else {
                        throw new Error(result.message || '启动爬取任务失败');
                    }
                } else {
                    throw new Error(`爬取API调用失败: HTTP ${response.status}`);
                }
                
            } catch (error) {
                console.error('启动爬取失败:', error);
                alert(`启动爬取失败: ${error.message}`);
                
                // 返回选择页面
                document.getElementById('searchingSection').style.display = 'none';
                document.getElementById('jdSelectionSection').style.display = 'block';
            }
        }

        // 轮询爬取状态
        async function pollCrawlStatus(taskId) {
            try {
                const response = await fetch(`${RESUME_EVAL_API_BASE}/crawl_status/${taskId}`);
                if (response.ok) {
                    const result = await response.json();
                    console.log('爬取状态检查结果:', result);
                    
                    if (result.success) {
                        const status = result.status;
                        
                        // 更新搜索等待页面的进度信息
                        updateCrawlProgress(status);
                        
                        if (status.status === 'completed') {
                            // 爬取完成，需要调用评估API
                            console.log('爬取完成，准备调用评估API:', status);
                            const progressText = document.querySelector('.searching-text');
                            if (progressText) {
                                progressText.textContent = '爬取完成，准备开始AI评估...';
                            }
                            
                            // 检查是否有简历数据
                            if (status.resumes && status.resumes.length > 0) {
                                console.log('找到简历数据，开始评估:', status.resumes);
                                // 调用评估API
                                startEvaluationAfterCrawl(status.resumes);
                            } else {
                                console.log('没有找到简历数据，使用模拟数据');
                                // 如果没有简历数据，使用模拟数据
                                const mockResumes = [
                                    {
                                        candidate_name: "候选人1",
                                        source_url: "模拟来源",
                                        crawl_time: new Date().toISOString(),
                                        platform: "模拟平台"
                                    },
                                    {
                                        candidate_name: "候选人2", 
                                        source_url: "模拟来源",
                                        crawl_time: new Date().toISOString(),
                                        platform: "模拟平台"
                                    }
                                ];
                                startEvaluationAfterCrawl(mockResumes);
                            }
                        } else if (status.status === 'failed') {
                            // 爬取失败
                            alert(`爬取失败: ${status.error || '未知错误'}`);
                            document.getElementById('searchingSection').style.display = 'none';
                            document.getElementById('jdSelectionSection').style.display = 'block';
                        } else {
                            // 继续轮询
                            setTimeout(() => pollCrawlStatus(taskId), 3000);
                        }
                    } else {
                        throw new Error(result.message || '获取爬取状态失败');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                console.error('轮询爬取状态失败:', error);
                alert(`获取爬取状态失败: ${error.message}`);
                document.getElementById('searchingSection').style.display = 'none';
                document.getElementById('jdSelectionSection').style.display = 'block';
            }
        }

        // 更新爬取进度
        function updateCrawlProgress(status) {
            console.log('更新爬取进度:', status);
            
            const progressText = document.querySelector('.searching-text');
            const progressSubtext = document.querySelector('.searching-subtext');
            
            if (progressText && progressSubtext) {
                // 确保从正确的字段获取进度信息
                const completed = status.completed || 0;
                const total = status.total || 0;
                const progress = total > 0 ? Math.round((completed / total) * 100) : 0;
                
                progressText.textContent = `正在爬取简历... ${progress}%`;
                progressSubtext.textContent = `已爬取 ${completed}/${total} 份简历`;
                console.log(`进度更新: ${progress}% (${completed}/${total})`);
                
                // 如果爬取完成，更新状态
                if (status.status === 'completed') {
                    progressText.textContent = '爬取完成，准备开始AI评估...';
                    progressSubtext.textContent = `共爬取 ${total} 份简历`;
                }
            } else {
                console.error('找不到进度显示元素');
            }
        }

        // 爬取完成后开始评估
        async function startEvaluationAfterCrawl(resumes) {
            try {
                console.log('开始评估流程，简历数据:', resumes);
                
                // 准备评估数据
                const formData = new FormData();
                
                // 添加JD文件
                if (selectedJD.id === 'default') {
                    const defaultJdContent = `# ${selectedJD.title}

## 岗位职责
负责组织招聘、培训、绩效、员工关系等HR工作。

## 任职要求
- 沟通协调能力强
- 本科及以上学历
- 人力资源相关经验3年以上
- 熟悉劳动法规和HR流程`;
                    formData.append('jd_file', new Blob([defaultJdContent], { type: 'text/markdown' }), 'default_jd.md');
                } else {
                    const jdResponse = await fetch(`${RESUME_EVAL_API_BASE}/api/jd_content/${selectedJD.id}`);
                    if (jdResponse.ok) {
                        const jdData = await jdResponse.json();
                        if (jdData.success) {
                            formData.append('jd_file', new Blob([jdData.content], { type: 'text/markdown' }), `${selectedJD.title}.md`);
                        }
                    }
                }
                
                // 处理爬取到的简历数据
                if (resumes && resumes.length > 0) {
                    console.log('处理爬取到的简历数据:', resumes);
                    
                    // 从爬虫程序获取的简历数据中提取文件路径
                    for (const resume of resumes) {
                        try {
                            // 如果简历数据包含文件路径，尝试获取文件内容
                            if (resume.file_path) {
                                console.log('尝试获取简历文件:', resume.file_path);
                                
                                // 修复文件路径格式问题
                                let filePath = resume.file_path;
                                // 处理路径分隔符问题
                                filePath = filePath.replace(/\\/g, '/').replace(/\|\|/g, '/');
                                
                                console.log('修复后的文件路径:', filePath);
                                
                                // 调用爬虫程序的API获取文件内容
                                const fileResponse = await fetch(`http://localhost:8000/api/view-file?path=${encodeURIComponent(filePath)}`);
                                if (fileResponse.ok) {
                                    const fileBlob = await fileResponse.blob();
                                    formData.append('files', fileBlob, `${resume.candidate_name}.png`);
                                    formData.append('candidate_names', resume.candidate_name);
                                    console.log('成功添加简历文件:', resume.candidate_name);
                                } else {
                                    console.error('无法获取简历文件:', filePath);
                                    throw new Error(`无法获取简历文件: ${filePath}`);
                                }
                            } else {
                                console.error('简历数据没有文件路径');
                                throw new Error('简历数据没有文件路径');
                            }
                        } catch (error) {
                            console.error('处理简历数据失败:', error, resume);
                            throw new Error(`处理简历数据失败: ${error.message}`);
                        }
                    }
                } else {
                    console.error('没有爬取到简历数据');
                    throw new Error('没有爬取到简历数据，请检查爬虫系统是否正常工作');
                }
                
                // 第一阶段：上传文件并提取基本信息
                console.log('开始第一阶段：上传文件并提取基本信息');
                const extractResponse = await fetch(`${RESUME_EVAL_API_BASE}/upload_and_extract`, {
                    method: 'POST',
                    body: formData
                });
                
                if (extractResponse.ok) {
                    const extractResult = await extractResponse.json();
                    if (extractResult.success) {
                        console.log('第一阶段完成：基本信息提取成功', extractResult);
                        
                        // 显示第一阶段结果（基本信息）
                        showInitialResults(extractResult.candidates);
                        
                        // 第二阶段：开始评估
                        console.log('开始第二阶段：AI评估');
                        document.querySelector('.searching-text').textContent = '正在进行AI评估...';
                        
                        // 添加轮次选择
                        formData.append('round_select', '1');
                        formData.append('round_select', '2');
                        formData.append('round_select', '2.5');
                        formData.append('round_select', '3');
                        
                        const evalResponse = await fetch(`${RESUME_EVAL_API_BASE}/start_task`, {
                            method: 'POST',
                            body: formData
                        });
                        
                        if (evalResponse.ok) {
                            const evalResult = await evalResponse.json();
                            if (evalResult.success) {
                                console.log('评估任务启动成功:', evalResult);
                                const progressText = document.querySelector('.searching-text');
                                if (progressText) {
                                    progressText.textContent = '正在AI评估中...';
                                }
                                // 开始轮询任务状态
                                pollTaskStatus(evalResult.task_id);
                            } else {
                                throw new Error(evalResult.message || '启动评估任务失败');
                            }
                        } else {
                            throw new Error(`评估API调用失败: HTTP ${evalResponse.status}`);
                        }
                    } else {
                        throw new Error(extractResult.message || '基本信息提取失败');
                    }
                } else {
                    throw new Error(`上传API调用失败: HTTP ${extractResponse.status}`);
                }
                
            } catch (error) {
                console.error('开始评估失败:', error);
                alert(`开始评估失败: ${error.message}`);
                document.getElementById('searchingSection').style.display = 'none';
                document.getElementById('jdSelectionSection').style.display = 'block';
            }
        }

        // 开始搜索（分阶段：先提取基本信息，再评估）
        async function startSearch() {
            if (!selectedJD) {
                alert('请先选择职位');
                return;
            }
            
            // 显示搜索等待页面
            document.getElementById('jdSelectionSection').style.display = 'none';
            document.getElementById('searchingSection').style.display = 'block';
            document.querySelector('.searching-text').textContent = '正在上传文件并提取基本信息...';
            
            try {
                // 准备简历文件数据
                const formData = new FormData();
                
                // 添加JD文件
                if (selectedJD.id === 'default') {
                    // 使用默认JD
                    formData.append('jd_file', new Blob(['默认JD内容'], { type: 'text/plain' }), 'default_jd.md');
                } else {
                    // 使用选中的JD文件
                    const userName = currentUser ? currentUser.name : '默认用户';
                    const jdResponse = await fetch(`${RESUME_EVAL_API_BASE}/api/jd_content/${selectedJD.id}?user=${encodeURIComponent(userName)}`);
                    if (jdResponse.ok) {
                        const jdData = await jdResponse.json();
                        if (jdData.success && jdData.content) {
                            formData.append('jd_file', new Blob([jdData.content], { type: 'text/markdown' }), `${selectedJD.title}.md`);
                        } else {
                            throw new Error(`获取JD内容失败: ${jdData.message || '未知错误'}`);
                        }
                    } else {
                        throw new Error(`JD内容API调用失败: HTTP ${jdResponse.status}`);
                    }
                }
                
                // 添加简历文件
                if (uploadedFiles.length > 0) {
                    uploadedFiles.forEach((file, index) => {
                        formData.append('files', file);
                        formData.append('candidate_names', extractCandidateName(file.name));
                    });
                } else {
                    // 如果没有上传文件，使用默认候选人
                    formData.append('files', new Blob(['默认简历内容'], { type: 'text/plain' }), 'default_resume.txt');
                    formData.append('candidate_names', '默认候选人');
                }
                
                // 第一阶段：上传文件并提取基本信息
                console.log('开始第一阶段：上传文件并提取基本信息');
                const extractResponse = await fetch(`${RESUME_EVAL_API_BASE}/upload_and_extract`, {
                    method: 'POST',
                    body: formData
                });
                
                if (extractResponse.ok) {
                    const extractResult = await extractResponse.json();
                    if (extractResult.success) {
                        console.log('第一阶段完成：基本信息提取成功', extractResult);
                        
                        // 显示第一阶段结果（基本信息）
                        showInitialResults(extractResult.candidates);
                        
                        // 第二阶段：开始评估
                        console.log('开始第二阶段：AI评估');
                        document.querySelector('.searching-text').textContent = '正在进行AI评估...';
                        
                        // 添加轮次选择
                        formData.append('round_select', '1');
                        formData.append('round_select', '2');
                        formData.append('round_select', '2.5');
                        formData.append('round_select', '3');
                        
                        const evalResponse = await fetch(`${RESUME_EVAL_API_BASE}/start_task`, {
                            method: 'POST',
                            body: formData
                        });
                        
                        if (evalResponse.ok) {
                            const evalResult = await evalResponse.json();
                            if (evalResult.success) {
                                console.log('第二阶段启动成功：开始评估', evalResult);
                                // 开始轮询任务状态
                                pollTaskStatus(evalResult.task_id);
                            } else {
                                throw new Error(evalResult.message || '启动评估任务失败');
                            }
                        } else {
                            throw new Error(`评估API调用失败: HTTP ${evalResponse.status}`);
                        }
                    } else {
                        throw new Error(extractResult.message || '基本信息提取失败');
                    }
                } else {
                    throw new Error(`上传API调用失败: HTTP ${extractResponse.status}`);
                }
                
            } catch (error) {
                console.error('开始搜索失败:', error);
                alert(`搜索失败: ${error.message}`);
                
                // 返回选择页面
                document.getElementById('searchingSection').style.display = 'none';
                document.getElementById('jdSelectionSection').style.display = 'block';
            }
        }

        // 实时更新候选人信息
        async function updateCandidateInfo(task) {
            console.log('更新候选人信息:', task);
            
            // 检查 searchResults 是否已初始化
            if (!searchResults || !searchResults.candidates) {
                console.warn('searchResults 未初始化，跳过候选人信息更新');
                return;
            }
            
            // 更新每个候选人的信息
            for (const detail of task.details) {
                const candidateIndex = searchResults.candidates.findIndex(c => c.id === detail.candidate_name);
                if (candidateIndex !== -1) {
                    const candidate = searchResults.candidates[candidateIndex];
                    
                    // 更新基本信息
                    if (detail.basic_info) {
                        const basicInfo = detail.basic_info;
                        console.log(`候选人 ${detail.candidate_name} 的后端返回基本信息:`, basicInfo);
                        
                        candidate.name = (basicInfo.name && basicInfo.name !== '未知' && basicInfo.name !== '未提及') 
                            ? basicInfo.name 
                            : detail.candidate_name;
                        candidate.age = basicInfo.age || '未提及';
                        candidate.experience = basicInfo.experience || '未提及';
                        candidate.education = basicInfo.education || '未提及';
                        
                        console.log(`候选人 ${detail.candidate_name} 更新后的基本信息:`, {
                            name: candidate.name,
                            age: candidate.age,
                            experience: candidate.experience,
                            education: candidate.education
                        });
                    } else {
                        console.log(`候选人 ${detail.candidate_name} 没有后端返回的基本信息`);
                    }
                    
                    // 更新评估结果
                    if (detail.success) {
                        // 根据评估轮次决定得分显示
                        if (detail.final_round === 3) {
                            // 进入第三轮，显示最终得分
                            candidate.aiScore = detail.final_score || 0;
                            console.log(`候选人 ${detail.candidate_name} 进入第三轮，显示最终得分: ${candidate.aiScore}`);
                        } else {
                            // 没有进入第三轮，显示0分
                            candidate.aiScore = '0分';
                            console.log(`候选人 ${detail.candidate_name} 在第${detail.final_round}轮被淘汰，显示0分`);
                        }
                        candidate.qualified = detail.qualified || false;
                        candidate.taskId = task.task_id;
                        candidate.reportPaths = detail.report_paths;
                        candidate.status = 'evaluated';
                        
                        // 优先使用任务详情中的优势和风险信息
                        if (detail.advantages && detail.advantages !== '查看详细报告了解优势') {
                            candidate.advantages = detail.advantages;
                        }
                        if (detail.risks && detail.risks !== '查看详细报告了解风险') {
                            candidate.risks = detail.risks;
                        }
                        
                        // 如果任务详情中没有优势和风险信息，尝试从报告中获取
                        if ((!detail.advantages || detail.advantages === '查看详细报告了解优势') && 
                            detail.report_paths && detail.report_paths.length > 0 && task.task_id && candidate.advantages === '未评估') {
                            try {
                                const response = await fetch(`${RESUME_EVAL_API_BASE}/get_evaluation_reports/${task.task_id}/${encodeURIComponent(detail.candidate_name)}`);
                                if (response.ok) {
                                    const result = await response.json();
                                    if (result.success && result.reports.length > 0) {
                                        const reports = result.reports.sort((a, b) => a.round - b.round);
                                        
                                        // 获取第一轮报告的优势和风险
                                        const firstRoundReport = reports.find(r => r.round === 1);
                                        if (firstRoundReport) {
                                            const firstRoundInfo = parseFirstRoundAdvantagesAndRisks(firstRoundReport.content);
                                            candidate.advantages = firstRoundInfo.advantages;
                                            candidate.risks = firstRoundInfo.risks;
                                        }
                                    }
                                }
                            } catch (error) {
                                console.error(`获取候选人 ${detail.candidate_name} 的优势和风险失败:`, error);
                            }
                        }
                    }
                }
            }
            
            // 更新统计信息
            searchResults.qualified = searchResults.candidates.filter(c => c.qualified).length;
            searchResults.others = searchResults.candidates.filter(c => !c.qualified).length;
            
            // 重新生成候选人卡片
            generateCandidateCards();
            
            // 更新统计数据显示
            document.getElementById('totalCandidates').textContent = searchResults.total;
            document.getElementById('qualifiedCandidates').textContent = searchResults.qualified;
            document.getElementById('otherCandidates').textContent = searchResults.others;
        }

        // 显示第一阶段结果（基本信息）
        function showInitialResults(candidates) {
            console.log('显示第一阶段结果：', candidates);
            
            searchResults = {
                total: candidates.length,
                qualified: 0,
                others: candidates.length,
                candidates: candidates.map(candidate => {
                    const basicInfo = candidate.basic_info || {};
                    return {
                        id: candidate.candidate_name,
                        name: (basicInfo.name && basicInfo.name !== '未知' && basicInfo.name !== '未提及') 
                            ? basicInfo.name 
                            : candidate.candidate_name,
                        age: basicInfo.age || '未提及',
                        experience: basicInfo.experience || '未提及',
                        education: basicInfo.education || '未提及',
                        aiScore: '待评估',
                        advantages: '未评估',
                        risks: '未评估',
                        qualified: false,
                        resumeDetail: '简历内容请查看详细报告',
                        taskId: null,
                        reportPaths: null,
                        status: 'extracted' // 标记为已提取基本信息
                    };
                })
            };
            
            showResults();
        }

        // 轮询任务状态
        async function pollTaskStatus(taskId) {
            try {
                const response = await fetch(`${RESUME_EVAL_API_BASE}/task_status/${taskId}`);
                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        const task = result.task;
                        
                        // 更新搜索等待页面的进度信息
                        updateSearchingProgress(task);
                        
                        // 实时更新候选人信息
                        if (task.details && task.details.length > 0) {
                            updateCandidateInfo(task);
                        }
                        
                        if (task.status === 'completed') {
                            // 任务完成，显示最终结果
                            processTaskResults(task);
                        } else if (task.status === 'failed') {
                            // 任务失败
                            alert(`任务失败: ${task.error || '未知错误'}`);
                            document.getElementById('searchingSection').style.display = 'none';
                            document.getElementById('jdSelectionSection').style.display = 'block';
                        } else {
                            // 继续轮询
                            setTimeout(() => pollTaskStatus(taskId), 2000);
                        }
                    } else {
                        throw new Error(result.message || '获取任务状态失败');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                console.error('轮询任务状态失败:', error);
                alert(`获取任务状态失败: ${error.message}`);
                document.getElementById('searchingSection').style.display = 'none';
                document.getElementById('jdSelectionSection').style.display = 'block';
            }
        }

        // 更新搜索进度
        function updateSearchingProgress(task) {
            const progressText = document.querySelector('.searching-text');
            const progressSubtext = document.querySelector('.searching-subtext');
            
            if (progressText && progressSubtext) {
                const progress = task.total > 0 ? Math.round((task.completed / task.total) * 100) : 0;
                progressText.textContent = `正在智能筛选简历... ${progress}%`;
                progressSubtext.textContent = `已处理 ${task.completed}/${task.total} 份简历`;
            }
        }

        // 解析第三轮评估报告中的基本信息
        function parseThirdRoundBasicInfo(reportContent) {
            const basicInfo = {
                name: '未提及',
                age: '未提及',
                education: '未提及',
                experience: '未提及',
                score: 0
            };
            
            try {
                // 查找基本信息部分
                const basicInfoMatch = reportContent.match(/## 基本信息[\s\S]*?(?=##|$)/);
                if (basicInfoMatch) {
                    const basicInfoText = basicInfoMatch[0];
                    console.log('找到基本信息文本:', basicInfoText);
                    
                    // 提取候选人姓名 - 支持多种格式
                    const namePatterns = [
                        /\*\*候选人\*\*[：:]\s*([^|]+?)(?=\s*\||\s*$)/,
                        /候选人[：:]\s*([^|]+?)(?=\s*\||\s*$)/
                    ];
                    
                    for (const pattern of namePatterns) {
                        const nameMatch = basicInfoText.match(pattern);
                        if (nameMatch && nameMatch[1].trim() && nameMatch[1].trim() !== '未知') {
                            basicInfo.name = nameMatch[1].trim();
                            console.log('提取到姓名:', basicInfo.name);
                            break;
                        }
                    }
                    
                    // 提取年龄 - 支持多种格式
                    const agePatterns = [
                        /\*\*年龄\*\*[：:]\s*([^|]+?)(?=\s*\||\s*$)/,
                        /年龄[：:]\s*([^|]+?)(?=\s*\||\s*$)/
                    ];
                    
                    for (const pattern of agePatterns) {
                        const ageMatch = basicInfoText.match(pattern);
                        if (ageMatch && ageMatch[1].trim() && ageMatch[1].trim() !== '未知') {
                            basicInfo.age = ageMatch[1].trim();
                            console.log('提取到年龄:', basicInfo.age);
                            break;
                        }
                    }
                    
                    // 提取学历 - 支持多种格式
                    const educationPatterns = [
                        /\*\*学历\*\*[：:]\s*([^|]+?)(?=\s*\||\s*$)/,
                        /学历[：:]\s*([^|]+?)(?=\s*\||\s*$)/
                    ];
                    
                    for (const pattern of educationPatterns) {
                        const educationMatch = basicInfoText.match(pattern);
                        if (educationMatch && educationMatch[1].trim() && educationMatch[1].trim() !== '未知') {
                            basicInfo.education = educationMatch[1].trim();
                            console.log('提取到学历:', basicInfo.education);
                            break;
                        }
                    }
                    
                    // 提取工作经验 - 支持多种格式
                    const experiencePatterns = [
                        /\*\*工作经验\*\*[：:]\s*([^|]+?)(?=\s*\||\s*$)/,
                        /工作经验[：:]\s*([^|]+?)(?=\s*\||\s*$)/
                    ];
                    
                    for (const pattern of experiencePatterns) {
                        const experienceMatch = basicInfoText.match(pattern);
                        if (experienceMatch && experienceMatch[1].trim() && experienceMatch[1].trim() !== '未知') {
                            basicInfo.experience = experienceMatch[1].trim();
                            console.log('提取到工作经验:', basicInfo.experience);
                            break;
                        }
                    }
                    
                    // 提取总分
                    const scorePatterns = [
                        /\*\*总分\*\*[：:]\s*(\d+)/,
                        /总分[：:]\s*(\d+)/
                    ];
                    
                    for (const pattern of scorePatterns) {
                        const scoreMatch = basicInfoText.match(pattern);
                        if (scoreMatch) {
                            basicInfo.score = parseInt(scoreMatch[1]);
                            break;
                        }
                    }
                    
                    console.log('解析结果:', basicInfo);
                }
            } catch (error) {
                console.error('解析第三轮基本信息失败:', error);
            }
            
            return basicInfo;
        }

        // 解析初筛报告中的基本信息
        function parseFirstRoundBasicInfo(reportContent) {
            const basicInfo = {
                name: '未提及',
                age: '未提及',
                experience: '未提及',
                education: '未提及',
                currentPosition: '未提及',
                expectedSalary: '未提及'
            };
            
            try {
                console.log('开始解析初筛报告的基本信息');
                
                // 提取姓名
                const namePatterns = [
                    /\*\*姓名\*\*[：:]\s*([^\n\r]+)/,
                    /姓名[：:]\s*([^\n\r]+)/
                ];
                
                for (const pattern of namePatterns) {
                    const nameMatch = reportContent.match(pattern);
                    if (nameMatch) {
                        basicInfo.name = nameMatch[1].trim();
                        break;
                    }
                }
                
                // 提取年龄
                const agePatterns = [
                    /\*\*年龄\*\*[：:]\s*([^\n\r]+)/,
                    /年龄[：:]\s*([^\n\r]+)/
                ];
                
                for (const pattern of agePatterns) {
                    const ageMatch = reportContent.match(pattern);
                    if (ageMatch) {
                        basicInfo.age = ageMatch[1].trim();
                        break;
                    }
                }
                
                // 提取工作经验
                const experiencePatterns = [
                    /\*\*工作经验\*\*[：:]\s*([^\n\r]+)/,
                    /工作经验[：:]\s*([^\n\r]+)/
                ];
                
                for (const pattern of experiencePatterns) {
                    const experienceMatch = reportContent.match(pattern);
                    if (experienceMatch) {
                        basicInfo.experience = experienceMatch[1].trim();
                        break;
                    }
                }
                
                // 提取学历
                const educationPatterns = [
                    /\*\*学历\*\*[：:]\s*([^\n\r]+)/,
                    /学历[：:]\s*([^\n\r]+)/
                ];
                
                for (const pattern of educationPatterns) {
                    const educationMatch = reportContent.match(pattern);
                    if (educationMatch) {
                        basicInfo.education = educationMatch[1].trim();
                        break;
                    }
                }
                
                // 提取当前职位
                const positionPatterns = [
                    /\*\*当前职位\*\*[：:]\s*([^\n\r]+)/,
                    /当前职位[：:]\s*([^\n\r]+)/
                ];
                
                for (const pattern of positionPatterns) {
                    const positionMatch = reportContent.match(pattern);
                    if (positionMatch) {
                        basicInfo.currentPosition = positionMatch[1].trim();
                        break;
                    }
                }
                
                // 提取期望薪资
                const salaryPatterns = [
                    /\*\*期望薪资\*\*[：:]\s*([^\n\r]+)/,
                    /期望薪资[：:]\s*([^\n\r]+)/
                ];
                
                for (const pattern of salaryPatterns) {
                    const salaryMatch = reportContent.match(pattern);
                    if (salaryMatch) {
                        basicInfo.expectedSalary = salaryMatch[1].trim();
                        break;
                    }
                }
                
                console.log('初筛报告基本信息解析结果:', basicInfo);
            } catch (error) {
                console.error('解析初筛基本信息失败:', error);
            }
            
            return basicInfo;
        }

        // 解析初筛结果中的优势和风险
        function parseFirstRoundAdvantagesAndRisks(reportContent) {
            const result = {
                advantages: '查看详细报告了解优势',
                risks: '查看详细报告了解风险'
            };
            
            try {
                console.log('开始解析初筛报告的优势和风险');
                
                // 查找优势部分 - 支持多种格式
                const advantagesPatterns = [
                    /- \*\*优势:\*\*[\s\S]*?(?=- \*\*风险|$)/,
                    /优势[：:]\s*[\s\S]*?(?=风险|$)/,
                    /- 优势[\s\S]*?(?=- 风险|$)/
                ];
                
                for (const pattern of advantagesPatterns) {
                    const advantagesMatch = reportContent.match(pattern);
                    if (advantagesMatch) {
                        const advantagesText = advantagesMatch[0];
                        console.log('找到优势文本:', advantagesText);
                        
                        // 提取优势列表
                        const advantagesList = advantagesText.match(/- ([^\n]+)/g);
                        if (advantagesList && advantagesList.length > 0) {
                            const cleanAdvantages = advantagesList
                                .slice(0, 3)
                                .map(item => item.replace('- ', '').trim())
                                .filter(item => item && item !== '查看详细报告了解优势');
                            
                            if (cleanAdvantages.length > 0) {
                                result.advantages = cleanAdvantages.join('；');
                                break;
                            }
                        }
                    }
                }
                
                // 查找风险部分 - 支持多种格式
                const risksPatterns = [
                    /- \*\*风险与不足:\*\*[\s\S]*?(?=##|$)/,
                    /风险[：:]\s*[\s\S]*?(?=##|$)/,
                    /- 风险[\s\S]*?(?=##|$)/
                ];
                
                for (const pattern of risksPatterns) {
                    const risksMatch = reportContent.match(pattern);
                    if (risksMatch) {
                        const risksText = risksMatch[0];
                        console.log('找到风险文本:', risksText);
                        
                        // 提取风险列表
                        const risksList = risksText.match(/- ([^\n]+)/g);
                        if (risksList && risksList.length > 0) {
                            const cleanRisks = risksList
                                .slice(0, 2)
                                .map(item => item.replace('- ', '').trim())
                                .filter(item => item && item !== '查看详细报告了解风险');
                            
                            if (cleanRisks.length > 0) {
                                result.risks = cleanRisks.join('；');
                                break;
                            }
                        }
                    }
                }
                
                console.log('解析结果:', result);
            } catch (error) {
                console.error('解析初筛优势和风险失败:', error);
            }
            
            return result;
        }

        // 处理任务结果
        async function processTaskResults(task) {
            console.log('=== 开始处理任务结果 ===');
            console.log('任务详情:', task);
            console.log('当前时间:', new Date().toLocaleString());
            
            // 检查是否已经有更新的数据
            if (searchResults && searchResults.candidates && searchResults.candidates.length > 0) {
                console.log('检测到已有更新的候选人数据，跳过重新构建');
                return;
            }
            
            // 转换任务结果为前端需要的格式
            const candidates = [];
            
                    for (const detail of task.details) {
            console.log(`处理候选人: ${detail.candidate_name}`);
            console.log(`候选人详情:`, detail);
            
            // 优先使用任务详情中的基本信息
            let finalBasicInfo = {
                name: '未提及',
                age: '未提及',
                education: '未提及',
                experience: '未提及'
            };
            
            // 首先检查任务详情中是否有基本信息
            if (detail.basic_info) {
                console.log(`候选人 ${detail.candidate_name} 的任务详情基本信息:`, detail.basic_info);
                finalBasicInfo = {
                    // 优先使用candidate_name，如果简历中提取的姓名为"未知"或"未提及"，则使用candidate_name
                    name: (detail.basic_info.name && detail.basic_info.name !== '未知' && detail.basic_info.name !== '未提及') 
                        ? detail.basic_info.name 
                        : detail.candidate_name,
                    age: detail.basic_info.age || detail.basic_info['age'] || '未提及',
                    education: detail.basic_info.education || detail.basic_info['education'] || '未提及',
                    experience: detail.basic_info.experience || detail.basic_info['experience'] || '未提及'
                };
                console.log(`使用任务详情中的基本信息:`, finalBasicInfo);
            } else {
                console.log(`候选人 ${detail.candidate_name} 的任务详情中没有基本信息，尝试从初筛报告中提取`);
            }
            
            // 从任务详情中获取优势和风险信息
            let advantages = detail.advantages || '查看详细报告了解优势';
            let risks = detail.risks || '查看详细报告了解风险';
            
            // 如果有报告路径，尝试解析第三轮评估报告和初筛报告
            if (detail.report_paths && detail.report_paths.length > 0 && task.task_id) {
                console.log(`候选人 ${detail.candidate_name} 有报告路径:`, detail.report_paths);
                try {
                    // 获取评估报告
                    const response = await fetch(`${RESUME_EVAL_API_BASE}/get_evaluation_reports/${task.task_id}/${encodeURIComponent(detail.candidate_name)}`);
                    if (response.ok) {
                        const result = await response.json();
                        console.log(`候选人 ${detail.candidate_name} 的评估报告结果:`, result);
                        if (result.success && result.reports.length > 0) {
                            // 按轮次排序
                            const reports = result.reports.sort((a, b) => a.round - b.round);
                            console.log(`候选人 ${detail.candidate_name} 的报告轮次:`, reports.map(r => r.round));
                            
                            // 优先从初筛报告中获取基本信息
                            const firstRoundReport = reports.find(r => r.round === 1);
                            if (firstRoundReport) {
                                console.log(`找到候选人 ${detail.candidate_name} 的初筛报告，尝试提取基本信息`);
                                const firstRoundBasicInfo = parseFirstRoundBasicInfo(firstRoundReport.content);
                                
                                // 更新基本信息（优先使用初筛报告中的信息）
                                if (firstRoundBasicInfo.name && firstRoundBasicInfo.name !== '未提及') {
                                    finalBasicInfo.name = firstRoundBasicInfo.name;
                                }
                                if (firstRoundBasicInfo.age && firstRoundBasicInfo.age !== '未提及') {
                                    finalBasicInfo.age = firstRoundBasicInfo.age;
                                }
                                if (firstRoundBasicInfo.experience && firstRoundBasicInfo.experience !== '未提及') {
                                    finalBasicInfo.experience = firstRoundBasicInfo.experience;
                                }
                                if (firstRoundBasicInfo.education && firstRoundBasicInfo.education !== '未提及') {
                                    finalBasicInfo.education = firstRoundBasicInfo.education;
                                }
                                console.log(`从初筛报告提取的基本信息:`, finalBasicInfo);
                                
                                // 解析初筛报告的优势和风险
                                const firstRoundInfo = parseFirstRoundAdvantagesAndRisks(firstRoundReport.content);
                                advantages = firstRoundInfo.advantages;
                                risks = firstRoundInfo.risks;
                                console.log(`候选人 ${detail.candidate_name} 的优势和风险:`, { advantages, risks });
                            } else {
                                console.log(`未找到候选人 ${detail.candidate_name} 的初筛报告`);
                            }
                            
                            // 如果初筛报告中没有基本信息，尝试从第三轮评估报告中提取
                            if (!detail.basic_info && (!finalBasicInfo.name || finalBasicInfo.name === '未提及')) {
                                const thirdRoundReport = reports.find(r => r.round === 3);
                                if (thirdRoundReport) {
                                    console.log(`找到候选人 ${detail.candidate_name} 的第三轮报告，尝试提取基本信息`);
                                    const thirdRoundInfo = parseThirdRoundBasicInfo(thirdRoundReport.content);
                                    if (thirdRoundInfo.name && thirdRoundInfo.name !== '未提及') {
                                        finalBasicInfo.name = thirdRoundInfo.name;
                                    }
                                    if (thirdRoundInfo.age && thirdRoundInfo.age !== '未提及') {
                                        finalBasicInfo.age = thirdRoundInfo.age;
                                    }
                                    if (thirdRoundInfo.experience && thirdRoundInfo.experience !== '未提及') {
                                        finalBasicInfo.experience = thirdRoundInfo.experience;
                                    }
                                    if (thirdRoundInfo.education && thirdRoundInfo.education !== '未提及') {
                                        finalBasicInfo.education = thirdRoundInfo.education;
                                    }
                                    console.log(`从第三轮报告提取的基本信息:`, finalBasicInfo);
                                } else {
                                    console.log(`未找到候选人 ${detail.candidate_name} 的第三轮报告`);
                                }
                            }
                        } else {
                            console.log(`候选人 ${detail.candidate_name} 没有有效的评估报告`);
                        }
                    } else {
                        console.log(`获取候选人 ${detail.candidate_name} 的评估报告失败:`, response.status);
                    }
                } catch (error) {
                    console.error(`获取候选人 ${detail.candidate_name} 的评估报告失败:`, error);
                }
            } else {
                console.log(`候选人 ${detail.candidate_name} 没有报告路径`);
            }
            
            // 确保使用正确的默认值
            const finalCandidate = {
                id: detail.candidate_name,
                // 姓名逻辑：优先使用finalBasicInfo.name，如果为空或"未提及"，则使用candidate_name
                name: (finalBasicInfo.name && finalBasicInfo.name !== '未提及' && finalBasicInfo.name !== '未知') 
                    ? finalBasicInfo.name 
                    : detail.candidate_name,
                age: finalBasicInfo.age !== '未提及' ? finalBasicInfo.age : '未提及',
                experience: finalBasicInfo.experience !== '未提及' ? finalBasicInfo.experience : '未提及',
                education: finalBasicInfo.education !== '未提及' ? finalBasicInfo.education : '未提及',
                aiScore: detail.final_round === 3 ? (detail.final_score || 0) : '0分',
                advantages: advantages,
                risks: risks,
                qualified: detail.qualified || false,
                resumeDetail: '简历内容请查看详细报告',
                taskId: task.task_id,
                reportPaths: detail.report_paths
            };
                
                console.log(`候选人 ${detail.candidate_name} 的最终信息:`, finalCandidate);
                candidates.push(finalCandidate);
            }
            
            searchResults = {
                total: task.total || 0,
                qualified: candidates.filter(c => c.qualified).length,
                others: candidates.filter(c => !c.qualified).length,
                candidates: candidates
            };
            
            showResults();
        }

        // 显示结果
        function showResults() {
            document.getElementById('searchingSection').style.display = 'none';
            document.getElementById('resultsSection').style.display = 'block';
            
            // 更新统计数据
            document.getElementById('totalCandidates').textContent = searchResults.total;
            document.getElementById('qualifiedCandidates').textContent = searchResults.qualified;
            document.getElementById('otherCandidates').textContent = searchResults.others;
            
            // 生成候选人卡片
            generateCandidateCards();
        }

        // 生成候选人卡片
        function generateCandidateCards() {
            const candidatesGrid = document.getElementById('candidatesGrid');
            candidatesGrid.innerHTML = '';
            
            searchResults.candidates.forEach(candidate => {
                const candidateCard = document.createElement('div');
                candidateCard.className = 'candidate-card';
                candidateCard.onclick = () => showCandidateDetail(candidate);
                
                candidateCard.innerHTML = `
                    <div class="candidate-header">
                        <div class="candidate-name">${candidate.name}</div>
                        <div class="ai-score">${candidate.aiScore === '待评估' ? '待评估' : (candidate.aiScore === '0分' ? '0分' : candidate.aiScore + '分')}</div>
                    </div>
                    <div class="candidate-info">
                        <div class="info-row">
                            <span class="info-label">年龄</span>
                            <span class="info-value">${candidate.age === '未提及' ? '未提及' : (candidate.age.includes('岁') ? candidate.age : candidate.age + '岁')}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">工作经验</span>
                            <span class="info-value">${candidate.experience}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">学历</span>
                            <span class="info-value">${candidate.education}</span>
                        </div>
                    </div>
                    <div class="candidate-advantages">
                        <div class="advantages-title">优势</div>
                        <div class="advantages-content">${candidate.advantages}</div>
                    </div>
                    <div class="candidate-risks">
                        <div class="risks-title">风险</div>
                        <div class="risks-content">${candidate.risks}</div>
                    </div>
                    <div class="candidate-actions">
                        <button class="action-btn btn-detail" onclick="event.stopPropagation(); showCandidateDetail(${JSON.stringify(candidate).replace(/"/g, '&quot;')})">
                            <i class="fas fa-eye"></i> 查看详情
                        </button>
                        <button class="action-btn btn-contact" onclick="event.stopPropagation(); contactCandidate('${candidate.name}')">
                            <i class="fas fa-comments"></i> 联系候选人
                        </button>
                    </div>
                `;
                
                candidatesGrid.appendChild(candidateCard);
            });
        }

        // 显示候选人详情
        async function showCandidateDetail(candidate) {
            if (typeof candidate === 'string') {
                candidate = JSON.parse(candidate);
            }
            
            document.getElementById('modalTitle').textContent = `${candidate.name} - 简历详情`;
            document.getElementById('modalContent').innerHTML = '<div style="text-align: center; padding: 40px;"><i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #2563eb;"></i><br>加载中...</div>';
            
            document.getElementById('candidateModal').style.display = 'block';
            
            try {
                // 获取候选人的评估报告
                if (candidate.taskId) {
                    const response = await fetch(`${RESUME_EVAL_API_BASE}/get_evaluation_reports/${candidate.taskId}/${encodeURIComponent(candidate.name)}`);
                    if (response.ok) {
                        const result = await response.json();
                        if (result.success && result.reports.length > 0) {
                            // 显示评估报告
                            displayEvaluationReports(candidate, result.reports);
                        } else {
                            // 如果没有评估报告，显示基本信息
                            displayBasicInfo(candidate);
                        }
                    } else {
                        // 显示基本信息
                        displayBasicInfo(candidate);
                    }
                } else {
                    // 如果没有taskId，直接显示基本信息
                    displayBasicInfo(candidate);
                }
            } catch (error) {
                console.error('获取候选人详情失败:', error);
                displayBasicInfo(candidate);
            }
        }

        // 显示评估报告
        function displayEvaluationReports(candidate, reports) {
            let content = `
                <h4>基本信息</h4>
                <p><strong>姓名：</strong>${candidate.name}</p>
                <p><strong>AI评分：</strong>${candidate.aiScore}分</p>
                <p><strong>是否通过：</strong>${candidate.qualified ? '✅ 通过' : '❌ 未通过'}</p>
                
                <h4 style="margin-top: 20px;">评估报告</h4>
            `;
            
            // 按轮次排序显示报告
            reports.sort((a, b) => a.round - b.round);
            
            reports.forEach((report, index) => {
                content += `
                    <div style="margin-bottom: 20px; border: 1px solid #e2e8f0; border-radius: 8px; overflow: hidden;">
                        <div style="background: #f8fafc; padding: 10px 15px; border-bottom: 1px solid #e2e8f0;">
                            <strong>第${report.round}轮评估报告</strong>
                        </div>
                        <div style="padding: 15px; max-height: 300px; overflow-y: auto;">
                            <pre style="white-space: pre-wrap; font-family: inherit; margin: 0;">${report.content}</pre>
                        </div>
                    </div>
                `;
            });
            
            document.getElementById('modalContent').innerHTML = content;
        }

        // 显示基本信息
        function displayBasicInfo(candidate) {
            document.getElementById('modalContent').innerHTML = `
                <h4>基本信息</h4>
                <p><strong>姓名：</strong>${candidate.name}</p>
                <p><strong>AI评分：</strong>${candidate.aiScore}分</p>
                <p><strong>是否通过：</strong>${candidate.qualified ? '✅ 通过' : '❌ 未通过'}</p>
                
                <h4 style="margin-top: 20px;">优势分析</h4>
                <p>${candidate.advantages}</p>
                
                <h4 style="margin-top: 20px;">风险提示</h4>
                <p>${candidate.risks}</p>
                
                <h4 style="margin-top: 20px;">详细简历</h4>
                <p>${candidate.resumeDetail}</p>
            `;
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('candidateModal').style.display = 'none';
        }

        // 联系候选人
        function contactCandidate(candidateName) {
            // 模拟跳转到Boss直聘
            alert(`正在跳转到Boss直聘与 ${candidateName} 联系...`);
        }

        // 切换功能标签
        function switchFunctionTab(tab) {
            // 更新标签状态
            document.querySelectorAll('.function-tab').forEach(t => {
                t.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 更新内容显示
            if (tab === 'screening') {
                document.getElementById('jdSelectionSection').style.display = 'block';
                document.getElementById('historySection').style.display = 'none';
                document.getElementById('talentSection').style.display = 'none';
            } else if (tab === 'history') {
                document.getElementById('jdSelectionSection').style.display = 'none';
                document.getElementById('historySection').style.display = 'block';
                document.getElementById('talentSection').style.display = 'none';
                loadHistory();
            } else if (tab === 'talent') {
                document.getElementById('jdSelectionSection').style.display = 'none';
                document.getElementById('historySection').style.display = 'none';
                document.getElementById('talentSection').style.display = 'block';
                loadTalentPoolData();
            }
        }



        // 加载筛选历史
        async function loadHistory() {
            try {
                const allList = document.getElementById('allHistoryList');
                
                allList.innerHTML = '';
                
                // 显示加载状态
                const loadingMsg = document.createElement('div');
                loadingMsg.style.cssText = 'text-align: center; padding: 20px; color: #666;';
                loadingMsg.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 正在加载历史记录...';
                allList.appendChild(loadingMsg);
                
                // 从简历评估系统获取历史记录
                const response = await fetch(`${RESUME_EVAL_API_BASE}/api/history`);
                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        screeningHistory = result.data || [];
                        
                        // 清空加载状态
                        allList.innerHTML = '';
                        
                        if (screeningHistory.length === 0) {
                            showNoHistoryMessage();
                            return;
                        }
                        
                        // 显示所有历史记录
                        screeningHistory.forEach(item => {
                            const historyItem = createHistoryItem(item);
                            allList.appendChild(historyItem);
                        });
                        
                        console.log(`成功加载 ${screeningHistory.length} 条历史记录`);
                    } else {
                        console.warn('获取历史记录失败:', result.message);
                        showNoHistoryMessage();
                    }
                } else {
                    console.warn('获取历史记录API不可用，状态码:', response.status);
                    showNoHistoryMessage();
                }
            } catch (error) {
                console.error('加载历史记录失败:', error);
                showNoHistoryMessage();
            }
        }

        // 显示无历史记录消息
        function showNoHistoryMessage() {
            const allList = document.getElementById('allHistoryList');
            
            const noHistoryMsg = document.createElement('div');
            noHistoryMsg.style.cssText = 'text-align: center; padding: 40px; color: #666;';
            noHistoryMsg.innerHTML = '<i class="fas fa-history" style="font-size: 3rem; margin-bottom: 20px; opacity: 0.5;"></i><br>暂无筛选历史记录';
            
            allList.appendChild(noHistoryMsg);
        }

        // 创建历史项目
        function createHistoryItem(item) {
            const div = document.createElement('div');
            div.className = 'history-item';
            div.onclick = () => viewHistoryDetail(item);
            
            // 格式化日期和时间
            const date = item.date || '未知日期';
            const time = item.time || '';
            const dateTime = time ? `${date} ${time}` : date;
            
            // 格式化岗位信息
            const positionInfo = item.position_info || '未知岗位';
            
            // 计算统计信息
            const totalCandidates = item.total_candidates || 0;
            const stats = item.stats || {};
            const qualifiedCount = stats['符合要求'] || 0;
            const otherCount = totalCandidates - qualifiedCount;
            
            div.innerHTML = `
                <div class="history-header">
                    <div class="history-title">${positionInfo}</div>
                    <div class="history-date">${dateTime}</div>
                </div>
                <div class="history-stats">
                    <span>总候选人: ${totalCandidates}</span>
                    <span>符合要求: ${qualifiedCount}</span>
                    <span>其他: ${otherCount}</span>
                </div>
            `;
            return div;
        }

        // 查看历史详情
        async function viewHistoryDetail(item) {
            try {
                // 从简历评估系统获取历史详情
                const response = await fetch(`${RESUME_EVAL_API_BASE}/api/history/${encodeURIComponent(item.folder_name)}`);
                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        // 显示历史详情弹窗
                        showHistoryDetailModal(result.data);
                    } else {
                        alert(`获取历史详情失败: ${result.message}`);
                    }
                } else {
                    alert(`获取历史详情失败: HTTP ${response.status}`);
                }
            } catch (error) {
                console.error('获取历史详情失败:', error);
                alert(`获取历史详情失败: ${error.message}`);
            }
        }

        // 显示历史详情弹窗
        function showHistoryDetailModal(historyData) {
            // 创建历史详情弹窗
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            
            const modalContent = document.createElement('div');
            modalContent.className = 'modal-content';
            modalContent.style.maxWidth = '1000px';
            
            let content = `
                <div class="modal-header">
                    <h3 class="modal-title">历史筛选详情 - ${historyData.folder_name}</h3>
                    <button class="close-btn" onclick="this.closest('.modal').remove()">&times;</button>
                </div>
                <div style="max-height: 600px; overflow-y: auto;">
            `;
            
            // 检查是否有分类数据
            if (!historyData.categories || Object.keys(historyData.categories).length === 0) {
                content += `
                    <div style="text-align: center; padding: 40px; color: #666;">
                        <i class="fas fa-info-circle" style="font-size: 2rem; margin-bottom: 15px; opacity: 0.5;"></i>
                        <p>暂无详细数据</p>
                    </div>
                `;
            } else {
                // 显示各个分类的结果
                for (const [category, files] of Object.entries(historyData.categories)) {
                    content += `
                        <div style="margin-bottom: 30px;">
                            <h4 style="color: #2563eb; margin-bottom: 15px; border-bottom: 2px solid #e2e8f0; padding-bottom: 8px;">
                                ${category} (${files.length}人)
                            </h4>
                            <div style="display: grid; gap: 10px;">
                    `;
                    
                    files.forEach(file => {
                        // 格式化分数显示
                        let scoreDisplay = file.score;
                        let scoreColor = '#2563eb';
                        
                        if (file.score === '画像分析') {
                            scoreDisplay = '画像分析';
                            scoreColor = '#10b981';
                        } else if (file.score === '未知') {
                            scoreDisplay = '未知';
                            scoreColor = '#6b7280';
                        } else if (!isNaN(parseFloat(file.score))) {
                            scoreDisplay = `${file.score}分`;
                            scoreColor = parseFloat(file.score) >= 80 ? '#10b981' : 
                                       parseFloat(file.score) >= 60 ? '#f59e0b' : '#ef4444';
                        }
                        
                        // 格式化文件大小
                        const fileSize = file.file_size ? (file.file_size / 1024).toFixed(1) : '0';
                        
                        content += `
                            <div style="background: #f8fafc; padding: 15px; border-radius: 8px; border: 1px solid #e2e8f0; transition: all 0.2s ease;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                    <strong style="color: #1f2937;">${file.candidate_name || '未知候选人'}</strong>
                                    <span style="color: ${scoreColor}; font-weight: 600; font-size: 1.1rem;">${scoreDisplay}</span>
                                </div>
                                <div style="color: #666; font-size: 0.9rem; display: flex; gap: 15px; flex-wrap: wrap;">
                                    <span><i class="fas fa-layer-group"></i> 轮次: ${file.round || '未知'}</span>
                                    <span><i class="fas fa-file-alt"></i> 文件大小: ${fileSize}KB</span>
                                    <span><i class="fas fa-clock"></i> 修改时间: ${new Date(file.modified_time * 1000).toLocaleString()}</span>
                                </div>
                            </div>
                        `;
                    });
                    
                    content += `
                            </div>
                        </div>
                    `;
                }
            }
            
            content += '</div>';
            modalContent.innerHTML = content;
            modal.appendChild(modalContent);
            document.body.appendChild(modal);
            
            // 点击外部关闭
            modal.onclick = function(event) {
                if (event.target === modal) {
                    modal.remove();
                }
            };
        }

        // 显示Boss直聘二维码登录弹窗
        function showBossQRCodeModal(qrCodeData, message) {
            const modal = document.getElementById('bossQRCodeModal');
            const qrCodeImage = document.getElementById('qrCodeImage');
            const qrCodeLoading = document.getElementById('qrCodeLoading');
            const qrCodeStatus = document.getElementById('qrCodeStatus');
            
            // 显示弹窗
            modal.style.display = 'block';
            
            // 更新状态信息
            qrCodeStatus.innerHTML = `<p>${message}</p>`;
            qrCodeStatus.className = 'qr-code-status';
            
            // 处理二维码数据
            if (qrCodeData) {
                if (qrCodeData.startsWith('data:image/') || qrCodeData.startsWith('http')) {
                    // 如果是图片URL或base64数据
                    qrCodeImage.innerHTML = `<img src="${qrCodeData}" alt="Boss直聘登录二维码" />`;
                    qrCodeImage.style.display = 'block';
                    qrCodeLoading.style.display = 'none';
                } else {
                    // 如果是文本数据，尝试生成二维码
                    generateQRCode(qrCodeData, qrCodeImage);
                    qrCodeImage.style.display = 'block';
                    qrCodeLoading.style.display = 'none';
                }
                
                // 二维码显示成功后，开始轮询登录状态
                startLoginStatusPolling();

            } else {
                // 没有二维码数据，显示加载状态
                qrCodeImage.style.display = 'none';
                qrCodeLoading.style.display = 'flex';
                qrCodeStatus.innerHTML = '<p>正在生成二维码...</p>';
            }
        }

        // 关闭Boss直聘二维码登录弹窗
        function closeBossQRCodeModal() {
            const modal = document.getElementById('bossQRCodeModal');
            modal.style.display = 'none';
            
            // 停止轮询
            stopLoginStatusPolling();
            
            // 重置状态
            resetQRCodeModal();
        }

        // 重置二维码弹窗状态
        function resetQRCodeModal() {
            const qrCodeImage = document.getElementById('qrCodeImage');
            const qrCodeLoading = document.getElementById('qrCodeLoading');
            const qrCodeStatus = document.getElementById('qrCodeStatus');
            
            qrCodeImage.style.display = 'none';
            qrCodeLoading.style.display = 'flex';
            qrCodeStatus.innerHTML = '<p>等待扫码登录...</p>';
            qrCodeStatus.className = 'qr-code-status';
        }

        // 生成二维码（使用简单的文本显示，实际项目中可以使用qrcode.js等库）
        function generateQRCode(text, container) {
            // 这里使用简单的文本显示，实际项目中建议使用专业的二维码生成库
            container.innerHTML = `
                <div style="
                    width: 200px; 
                    height: 200px; 
                    background: #f8fafc; 
                    border: 2px solid #e2e8f0; 
                    border-radius: 8px; 
                    display: flex; 
                    align-items: center; 
                    justify-content: center; 
                    margin: 0 auto;
                    font-family: monospace;
                    font-size: 12px;
                    text-align: center;
                    word-break: break-all;
                    padding: 10px;
                ">
                    <div>
                        <div style="margin-bottom: 10px; color: #666;">二维码内容:</div>
                        <div style="color: #333;">${text}</div>
                    </div>
                </div>
            `;
        }

        // 登录状态轮询相关变量
        let loginStatusPolling = null;
        let loginCheckCount = 0;
        const MAX_LOGIN_CHECKS = 60; // 最多检查60次（5分钟）

        // 开始登录状态轮询
        function startLoginStatusPolling() {
            if (loginStatusPolling) {
                clearInterval(loginStatusPolling);
            }
            
            loginCheckCount = 0;
            loginStatusPolling = setInterval(async () => {
                try {
                    loginCheckCount++;
                    console.log(`第 ${loginCheckCount} 次检查登录状态...`);
                    
                    // 检查是否超过最大检查次数
                    if (loginCheckCount > MAX_LOGIN_CHECKS) {
                        stopLoginStatusPolling();
                        updateQRCodeStatus('登录超时，请重新获取二维码', 'error');
                        return;
                    }
                    
                    // 发送请求检查登录状态
                    const response = await fetch(`${RESUME_EVAL_API_BASE}/boss_login_status`);
                    if (response.ok) {
                        const result = await response.json();
                        
                        if (result.success && result.complete_info) {
                            // 登录成功并且信息提取完成
                            stopLoginStatusPolling();
                            updateQRCodeStatus('登录成功，信息提取完成！', 'success');
                            
                            // 延迟1秒后处理登录成功
                            setTimeout(() => {
                                handleBossLoginSuccess(result.user_info);
                            }, 1000);
                            
                        } else if (result.success && !result.complete_info) {
                            // 登录成功但信息提取还在进行中
                            updateQRCodeStatus('登录成功，正在提取用户信息...', 'processing');
                            
                        } else {
                            // 还在等待登录
                            updateQRCodeStatus(`等待扫码登录... (${loginCheckCount}/${MAX_LOGIN_CHECKS})`, '');
                        }
                    } else {
                        console.warn('检查登录状态请求失败:', response.status);
                    }
                    
                } catch (error) {
                    console.error('检查登录状态时发生错误:', error);
                }
            }, 5000); // 每5秒检查一次
        }

        // 停止登录状态轮询
        function stopLoginStatusPolling() {
            if (loginStatusPolling) {
                clearInterval(loginStatusPolling);
                loginStatusPolling = null;
            }
            loginCheckCount = 0;
        }

        // 更新用户头像显示
        function updateUserAvatar(avatar) {
            const userAvatar = document.getElementById('userAvatar');
            const avatarText = document.getElementById('avatarText');
            
            // 清空现有内容
            userAvatar.innerHTML = '';
            
            if (avatar && (avatar.startsWith('http') || avatar.startsWith('data:image/'))) {
                // 如果是图片URL或base64数据，显示图片
                const img = document.createElement('img');
                img.src = avatar;
                img.alt = '用户头像';
                img.onerror = function() {
                    // 图片加载失败时，回退到文字头像
                    fallbackToTextAvatar();
                };
                userAvatar.appendChild(img);
            } else {
                // 如果是文字或没有头像，显示文字头像
                fallbackToTextAvatar();
            }
        }

        // 回退到文字头像
        function fallbackToTextAvatar() {
            const userAvatar = document.getElementById('userAvatar');
            const avatarText = document.createElement('div');
            avatarText.className = 'avatar-text';
            
            if (currentUser && currentUser.name) {
                avatarText.textContent = currentUser.name.charAt(0).toUpperCase();
            } else {
                avatarText.innerHTML = '<i class="fas fa-user"></i>';
            }
            
            userAvatar.appendChild(avatarText);
        }

        // 更新二维码状态显示
        function updateQRCodeStatus(message, type = '') {
            const qrCodeStatus = document.getElementById('qrCodeStatus');
            qrCodeStatus.innerHTML = `<p>${message}</p>`;
            qrCodeStatus.className = `qr-code-status ${type}`;
        }


        // 处理Boss直聘登录成功
        function handleBossLoginSuccess(userInfo) {
            // 关闭二维码弹窗
            closeBossQRCodeModal();
            
            // 设置当前用户，处理后端返回的中文字段名
            const name = userInfo?.['姓名'] || userInfo?.name || userInfo?.username || 'Boss用户';
            const company = userInfo?.['公司名称'] || userInfo?.company || '';
            const avatar = userInfo?.['头像'] || userInfo?.avatar || name.charAt(0).toUpperCase();
            
            currentUser = {
                name: name,
                company: company,
                avatar: avatar
            };
            
            console.log('Boss登录成功，用户信息:', currentUser);
            
            // 显示主应用
            showApp();
            
            // 更新平台状态
            platformStatus.boss = true;
            updatePlatformStatus();
        }

        // 显示平台管理弹窗
        function showPlatformModal() {
            document.getElementById('platformModal').style.display = 'block';
            loadPlatformCards();
        }

        // 关闭平台管理弹窗
        function closePlatformModal() {
            document.getElementById('platformModal').style.display = 'none';
        }

        // 加载平台卡片
        function loadPlatformCards() {
            const platformGrid = document.getElementById('platformGrid');
            platformGrid.innerHTML = '';
            
            const platforms = [
                { id: 'boss', name: 'Boss直聘', icon: 'fas fa-briefcase', status: platformStatus.boss },
                { id: 'zhaopin', name: '智联招聘', icon: 'fas fa-search', status: platformStatus.zhaopin },
                { id: 'qiancheng', name: '前程无忧', icon: 'fas fa-star', status: platformStatus.qiancheng },
                { id: 'liepin', name: '猎聘网', icon: 'fas fa-user-tie', status: platformStatus.liepin }
            ];
            
            platforms.forEach(platform => {
                const card = document.createElement('div');
                card.className = `platform-card ${platform.status ? 'bound' : 'unbound'}`;
                card.onclick = () => bindPlatformAccount(platform.id);
                card.innerHTML = `
                    <div class="platform-logo">
                        <i class="${platform.icon}"></i>
                    </div>
                    <div class="platform-name">${platform.name}</div>
                    <div class="platform-status ${platform.status ? 'status-bound' : 'status-unbound'}">
                        ${platform.status ? '已绑定' : '未绑定'}
                    </div>
                `;
                platformGrid.appendChild(card);
            });
        }

        // 绑定平台账号
        function bindPlatformAccount(platformId) {
            if (platformStatus[platformId]) {
                // 已绑定，显示解绑选项
                if (confirm('是否要解绑该平台账号？')) {
                    platformStatus[platformId] = false;
                    updatePlatformStatus();
                    loadPlatformCards();
                }
            } else {
                // 未绑定，跳转到平台登录
                const platformNames = {
                    boss: 'Boss直聘',
                    zhaopin: '智联招聘',
                    qiancheng: '前程无忧',
                    liepin: '猎聘网'
                };
                
                alert(`正在跳转到${platformNames[platformId]}进行账号绑定...`);
                
                // 模拟绑定成功
                setTimeout(() => {
                    platformStatus[platformId] = true;
                    updatePlatformStatus();
                    loadPlatformCards();
                    closePlatformModal();
                }, 2000);
            }
        }

        // 更新平台状态
        function updatePlatformStatus() {
            const hasBoundPlatform = Object.values(platformStatus).some(status => status);
            const platformIcon = document.getElementById('platformIcon');
            
            if (hasBoundPlatform) {
                platformIcon.className = 'platform-icon bound';
                platformIcon.innerHTML = '<i class="fas fa-check"></i>';
            } else {
                platformIcon.className = 'platform-icon unbound';
                platformIcon.innerHTML = '<i class="fas fa-link"></i>';
            }
        }

        // 加载平台状态
        function loadPlatformStatus() {
            // 由于已移除警告横幅，只更新平台图标状态
            updatePlatformStatus();
        }

        // 显示创建JD选项
        function showCreateJDOptions() {
            const hasJD = confirm('您是否有现成的JD？\n\n点击"确定"：使用现有JD创建职位\n点击"取消"：使用AI生成JD');
            
            if (hasJD) {
                showCreateJD();
            } else {
                showJDGenerator();
            }
        }

        // 显示JD生成器
        function showJDGenerator() {
            document.getElementById('jdGeneratorModal').style.display = 'block';
        }

        // 关闭JD生成器
        function closeJDGeneratorModal() {
            document.getElementById('jdGeneratorModal').style.display = 'none';
            // 重置步骤
            currentStep = 1;
            updateStepIndicators();
        }

        // 显示指定步骤
        function showStep(step) {
            // 隐藏所有步骤内容
            for (let i = 1; i <= 5; i++) {
                const stepContent = document.getElementById(`step${i}Content`);
                if (stepContent) {
                    stepContent.classList.remove('active');
                }
            }
            
            // 显示指定步骤
            const targetStepContent = document.getElementById(`step${step}Content`);
            if (targetStepContent) {
                targetStepContent.classList.add('active');
            }
            
            // 更新当前步骤
            currentStep = step;
            updateStepIndicators();
        }

        // 更新步骤指示器
        function updateStepIndicators() {
            for (let i = 1; i <= 5; i++) {
                const stepIndicator = document.getElementById(`step${i}`);
                if (stepIndicator) {
                    if (i < currentStep) {
                        stepIndicator.classList.add('completed');
                        stepIndicator.classList.remove('active');
                    } else if (i === currentStep) {
                        stepIndicator.classList.add('active');
                        stepIndicator.classList.remove('completed');
                    } else {
                        stepIndicator.classList.remove('active', 'completed');
                    }
                }
            }
        }

        // JD生成相关变量（已在全局变量中定义）

        // 获取岗位建议
        async function getPositionAdvice() {
            const positionDescription = document.getElementById('positionDescription').value;
            
            if (!positionDescription.trim()) {
                alert('请描述您需要招聘的岗位');
                return;
            }
            
            console.log('🚀 开始获取岗位建议...');
            console.log('📝 岗位描述:', positionDescription);
            
            try {
                // 显示加载状态
                const adviceBtn = document.querySelector('#step1Content .search-btn');
                const originalText = adviceBtn.innerHTML;
                adviceBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 获取建议中...';
                adviceBtn.disabled = true;
                
                // 调用岗位建议API - 使用与yuy_jd_generator.html相同的请求格式
                const requestBody = {
                    position_name: positionDescription.trim(),
                    company_info: null,
                    request_content: `请根据以下岗位描述，提供专业的招聘建议：

岗位描述：${positionDescription.trim()}

请从市场需求、薪资水平、核心技能要求、招聘难点等方面提供详细的分析和建议，帮助HR更好地理解和招聘该岗位。`,
                user_name: currentUser ? currentUser.name : null // 传递用户名
            };
                
                console.log('📡 API请求:', `${JD_GENERATOR_API_BASE}/api/v1/get_position_advice`);
                console.log('📦 请求数据:', requestBody);
                
                const response = await fetch(`${JD_GENERATOR_API_BASE}/api/v1/get_position_advice`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });
                
                console.log('📊 API响应状态:', response.status, response.statusText);
                
                if (response.ok) {
                    const result = await response.json();
                    console.log('📦 API返回数据:', result);
                    
                    if (result.success) {
                        console.log('✅ 岗位建议获取成功:', result.advice);
                        
                        // 保存公司描述
                        conversationData.company_description = positionDescription;
                        
                        // 显示岗位建议内容
                        showPositionAdvice(result.advice);
                    } else {
                        console.error('❌ API返回失败:', result.message);
                        throw new Error(result.message || '获取岗位建议失败');
                    }
                } else {
                    const errorText = await response.text();
                    console.error('❌ API错误响应:', errorText);
                    throw new Error(`HTTP ${response.status}: ${response.statusText}\n${errorText}`);
                }
                
            } catch (error) {
                console.error('💥 获取岗位建议失败:', error);
                alert(`获取岗位建议失败: ${error.message}`);
            } finally {
                // 恢复按钮状态
                const adviceBtn = document.querySelector('#step1Content .search-btn');
                adviceBtn.innerHTML = '<i class="fas fa-lightbulb"></i> 获取岗位建议';
                adviceBtn.disabled = false;
            }
        }

        // 显示对话步骤
        function showConversationStep(advice) {
            console.log('🎯 显示对话步骤，岗位建议:', advice);
            
            // 显示步骤3（对话收集）
            showStep(3);
            
            // 清空对话区域
            const conversationArea = document.getElementById('conversationArea');
            if (conversationArea) {
                conversationArea.innerHTML = '';
                console.log('✅ 清空对话区域完成');
            }
            
            // 添加系统消息
            addConversationMessage('system', 'AI助手已准备好帮您收集岗位详细信息，让我们开始吧！');
            
            // 添加岗位建议或对话内容
            if (advice && advice.trim()) {
                // 使用格式化函数来显示岗位建议
                addFormattedConversationMessage('assistant', advice);
            } else {
                addConversationMessage('assistant', '已收到您的岗位描述，现在开始收集详细信息...');
            }
            
            // 开始第一轮对话
            setTimeout(() => {
                startConversation();
            }, 1000);
        }

        // 获取下一个问题
        async function fetchNextQuestion(userInput = null) {
            try {
                console.log(`发送请求，当前轮次: ${conversationData.current_round}, 用户输入: ${userInput}`);
                const requestBody = {
                    company_description: conversationData.company_description,
                    current_round: conversationData.current_round,
                    selected_options: {},
                    custom_inputs: {},
                    user_input: userInput,
                    user_name: currentUser ? currentUser.name : null // 传递用户名
                };

                const response = await fetch(`${JD_GENERATOR_API_BASE}/api/v1/conversation`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });

                const data = await response.json();

                if (data.success) {
                    if (data.is_summary_round) {
                        console.log('进入总结阶段');
                        showSummaryStep(data.summary_data);
                        return;
                    }

                    // 更新轮次
                    console.log(`收到响应，next_round: ${data.next_round}`);
                    conversationData.current_round = data.next_round;
                    console.log(`更新后当前轮次: ${conversationData.current_round}`);

                    // 提取纯问题内容（移除选项部分）
                    const cleanMessage = extractQuestionFromMessage(data.message);
                    addConversationMessage('assistant', cleanMessage);

                    // 单独显示选项
                    displayOptions(data.options);

                    // 标记这是否为补充询问
                    conversationData.isSupplementQuestion = data.is_supplement || false;

                    document.getElementById('userInput').focus();
                } else {
                    console.error('获取问题失败:', data.message);
                    alert('获取问题失败: ' + data.message);
                }
            } catch (error) {
                console.error('Error:', error);
                alert('网络错误，请重试');
            }
        }

        // 开始对话
        async function startConversation() {
            // 直接开始第一轮对话
            await fetchNextQuestion();
        }

        // 发送用户输入
        async function sendUserInput() {
            const userInput = document.getElementById('userInput').value.trim();
            
            // 如果是补充询问且输入为空或"跳过"，跳过补充询问
            if (conversationData.isSupplementQuestion && (!userInput || userInput === '跳过')) {
                conversationData.isSupplementQuestion = false;
                document.getElementById('userInput').value = '';
                document.getElementById('userInput').placeholder = "请输入您的回答...（输入'生成JD'或'结束'可完成对话）";
                document.getElementById('optionsArea').innerHTML = '';
                // 跳过补充询问，直接进入下一轮
                await fetchNextQuestion();
                return;
            }
            
            if (!userInput) {
                alert('请输入您的回答');
                return;
            }
            
            // 添加用户消息
            addConversationMessage('user', userInput);
            
            // 清空输入框
            document.getElementById('userInput').value = '';
            document.getElementById('userInput').placeholder = "请输入您的回答...（输入'生成JD'或'结束'可完成对话）";
            document.getElementById('optionsArea').innerHTML = '';
            
            // 检查用户是否要求生成JD
            if (userInput === "生成JD" || userInput === "结束") {
                alert('正在生成JD，请稍候...');
                await fetchNextQuestion(userInput);
                return;
            }
            
            // 如果是补充询问的回答，直接进入下一轮
            if (conversationData.isSupplementQuestion) {
                conversationData.isSupplementQuestion = false;
                // 补充询问回答完毕，现在进入下一轮
                await fetchNextQuestion();
            } else {
                // 正常回答后，直接进入下一轮
                console.log(`用户回答完毕，准备进入下一轮，当前轮次: ${conversationData.current_round}`);
                await fetchNextQuestion(userInput);
            }
        }

        // 显示选项
        function displayOptions(options) {
            const optionsContainer = document.getElementById('optionsArea');
            optionsContainer.innerHTML = '';
            
            if (!options || options.length === 0) return;
            
            const title = document.createElement('div');
            title.className = 'text-sm text-gray-600 mb-3';
            title.textContent = '参考选项（仅供参考，选择点击可快速填入）：';
            optionsContainer.appendChild(title);
            
            // 过滤第一轮薪资询问时的无关选项
            const filteredOptions = filterIrrelevantOptions(options);
            
            filteredOptions.forEach((option, index) => {
                const optionDiv = document.createElement('div');
                optionDiv.className = 'option-item';
                optionDiv.textContent = `${index + 1}. ${option}`;
                
                optionDiv.addEventListener('click', () => {
                    // 如果选项中包含分号，按分号分割并添加换行
                    if (option.includes('；') || option.includes(';')) {
                        const items = option.replace(/；/g, ';').split(';').map(item => item.trim()).filter(Boolean);
                        const formattedOption = items.join('\n');
                        
                        const currentValue = document.getElementById('userInput').value.trim();
                        if (currentValue) {
                            document.getElementById('userInput').value = currentValue + '\n' + formattedOption;
                        } else {
                            document.getElementById('userInput').value = formattedOption;
                        }
                    } else {
                        const currentValue = document.getElementById('userInput').value.trim();
                        if (currentValue) {
                            document.getElementById('userInput').value = currentValue + '\n' + option;
                        } else {
                            document.getElementById('userInput').value = option;
                        }
                    }
                    document.getElementById('userInput').focus();
                });
                
                optionsContainer.appendChild(optionDiv);
            });
        }

        // 过滤第一轮薪资询问时的无关选项
        function filterIrrelevantOptions(options) {
            // 检查当前是否是第一轮且询问薪资
            if (conversationData.current_round === 1) {
                const lastSystemMessage = conversationData.conversationHistory.filter(msg => msg.type === 'assistant').pop();
                if (lastSystemMessage && (lastSystemMessage.content.includes('薪资') || lastSystemMessage.content.includes('薪酬'))) {
                    // 只保留包含薪资相关信息的选项
                    return options.filter(option =>
                        option.includes('K') ||
                        option.includes('k') ||
                        option.includes('薪') ||
                        option.includes('元') ||
                        option.includes('万') ||
                        /\d+/.test(option) // 包含数字
                    );
                }
            }
            return options;
        }

        // 选择选项
        function selectOption(option) {
            const userInput = document.getElementById('userInput');
            const currentValue = userInput.value.trim();
            
            // 如果选项中包含分号，按分号分割并添加换行
            if (option.includes('；') || option.includes(';')) {
                const items = option.replace(/；/g, ';').split(';').map(item => item.trim()).filter(Boolean);
                const formattedOption = items.join('\n');

                if (currentValue) {
                    userInput.value = currentValue + '\n' + formattedOption;
                } else {
                    userInput.value = formattedOption;
                }
            } else {
                if (currentValue) {
                    userInput.value = currentValue + '\n' + option;
                } else {
                    userInput.value = option;
                }
            }
            
            userInput.focus();
        }

        // 从AI回复中提取纯问题内容（移除选项部分）
        function extractQuestionFromMessage(message) {
            // 查找选项开始的位置的各种模式
            const optionPatterns = [
                /参考选项[\s\S]*?：/i,
                /以下是一些参考选项/i,
                /您可以从以下选项中选择/i,
                /参考答案/i,
                /可选项/i,
                /\n\s*[①②③④⑤⑥⑦⑧⑨⑩]/,  // 中文圆圈数字
                /\n\s*[1-9]\.\s+/,  // 数字列表
                /\n\s*[A-Z][\.\)]\s+/,  // 字母选项
                /\n\s*【[^】]+】/,  // 中文括号选项
                /（[多可]选[择项]*）/,  // 多选或可选标记
                /\n\s*\w+：[^：\n]+/  // 冒号分隔的选项
            ];

            let cleanMessage = message.trim();

            // 移除选项部分
            for (const pattern of optionPatterns) {
                const match = cleanMessage.search(pattern);
                if (match !== -1) {
                    cleanMessage = cleanMessage.substring(0, match).trim();
                    break;
                }
            }

            // 进一步清理格式
            cleanMessage = cleanMessage
                .replace(/：\s*$/, '')  // 移除末尾冒号
                .replace(/\s*$/, '')    // 移除末尾空白
                .replace(/\n\n+/g, '\n') // 合并多余换行
                .trim();

            // 如果清理后内容太短，返回原消息的前200字符
            if (cleanMessage.length < 20) {
                return message.substring(0, 200).trim() + (message.length > 200 ? '...' : '');
            }

            return cleanMessage;
        }

        // 显示总结步骤
        function showSummaryStep(summaryData) {
            console.log('📋 显示信息总结步骤');
            
            // 保存总结数据到全局变量
            window.summaryData = summaryData;
            
            // 显示步骤4（信息总结）
            showStep(4);
            
            // 显示总结内容
            displaySummaryContent(summaryData);
            
            // 解析并显示结构化数据
            if (typeof summaryData === 'object') {
                parseAndDisplayStructuredData(summaryData);
            }
        }
        
        // 复制对话历史到修改区域
        function copyConversationHistory() {
            const modificationArea = document.getElementById('modificationConversationArea');
            if (modificationArea && conversationData.conversationHistory) {
                modificationArea.innerHTML = '';
                conversationData.conversationHistory.forEach(msg => {
                    const messageDiv = document.createElement('div');
                    messageDiv.className = `conversation-message ${msg.type}`;
                    messageDiv.textContent = msg.content;
                    modificationArea.appendChild(messageDiv);
                });
                modificationArea.scrollTop = modificationArea.scrollHeight;
            }
        }
        
        // 显示总结内容
        function displaySummaryContent(summaryData) {
            const basicInfoDisplay = document.getElementById('basicInfoDisplay');
            if (!basicInfoDisplay) return;
            
            let basicInfoHtml = '<h5 style="margin-bottom: 10px; color: #333; font-size: 16px; font-weight: 600;">基本信息</h5>';
            basicInfoHtml += '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">';
            
            // 显示基本信息
            if (basicInfo) {
                basicInfoHtml += `
                    <div style="background: white; padding: 12px; border-radius: 6px; border: 1px solid #e9ecef;">
                        <div style="font-weight: 600; color: #495057; margin-bottom: 5px;">薪资范围</div>
                        <div style="color: #6c757d; font-size: 14px;">${basicInfo.salaryRange}</div>
                    </div>
                    <div style="background: white; padding: 12px; border-radius: 6px; border: 1px solid #e9ecef;">
                        <div style="font-weight: 600; color: #495057; margin-bottom: 5px;">工作地点</div>
                        <div style="color: #6c757d; font-size: 14px;">${basicInfo.workLocation}</div>
                    </div>
                    <div style="background: white; padding: 12px; border-radius: 6px; border: 1px solid #e9ecef;">
                        <div style="font-weight: 600; color: #495057; margin-bottom: 5px;">学历要求</div>
                        <div style="color: #6c757d; font-size: 14px;">${basicInfo.educationLevel}</div>
                    </div>
                    <div style="background: white; padding: 12px; border-radius: 6px; border: 1px solid #e9ecef;">
                        <div style="font-weight: 600; color: #495057; margin-bottom: 5px;">工作经验</div>
                        <div style="color: #6c757d; font-size: 14px;">${basicInfo.workExperience}</div>
                    </div>
                `;
            }
            
            // 显示总结数据中的基本信息
            if (typeof summaryData === 'object') {
                if (summaryData['职位']) {
                    basicInfoHtml += `
                        <div style="background: white; padding: 12px; border-radius: 6px; border: 1px solid #e9ecef;">
                            <div style="font-weight: 600; color: #495057; margin-bottom: 5px;">职位名称</div>
                            <div style="color: #6c757d; font-size: 14px;">${summaryData['职位']}</div>
                        </div>
                    `;
                }
                if (summaryData['薪资']) {
                    basicInfoHtml += `
                        <div style="background: white; padding: 12px; border-radius: 6px; border: 1px solid #e9ecef;">
                            <div style="font-weight: 600; color: #495057; margin-bottom: 5px;">薪资待遇</div>
                            <div style="color: #6c757d; font-size: 14px;">${summaryData['薪资']}</div>
                        </div>
                    `;
                }
                if (summaryData['地区']) {
                    basicInfoHtml += `
                        <div style="background: white; padding: 12px; border-radius: 6px; border: 1px solid #e9ecef;">
                            <div style="font-weight: 600; color: #495057; margin-bottom: 5px;">工作地区</div>
                            <div style="color: #6c757d; font-size: 14px;">${summaryData['地区']}</div>
                        </div>
                    `;
                }
                if (summaryData['行业']) {
                    basicInfoHtml += `
                        <div style="background: white; padding: 12px; border-radius: 6px; border: 1px solid #e9ecef;">
                            <div style="font-weight: 600; color: #495057; margin-bottom: 5px;">所属行业</div>
                            <div style="color: #6c757d; font-size: 14px;">${summaryData['行业']}</div>
                        </div>
                    `;
                }
            }
            
            basicInfoHtml += '</div>';
            basicInfoDisplay.innerHTML = basicInfoHtml;
        }

        // 解析并显示结构化数据
        function parseAndDisplayStructuredData(summaryData) {
            console.log('🔍 解析结构化数据:', summaryData);
            
            // 解析核心职责
            if (summaryData['核心职责']) {
                responsibilities = parseList(summaryData['核心职责'], 'responsibilities');
                displayResponsibilitiesList();
            }
            
            // 解析核心技能
            if (summaryData['核心技能']) {
                skills = parseList(summaryData['核心技能'], 'skills');
                displaySkillsList();
            }
            
            // 解析拓展技能
            if (summaryData['拓展技能']) {
                prioritySkills = parseList(summaryData['拓展技能'], 'skills');
                displayPrioritySkillsList();
            }
            
            // 解析加分项
            if (summaryData['加分项']) {
                bonusSkills = parseList(summaryData['加分项'], 'skills');
                displayBonusSkillsList();
            }
            
            // 初始化拖拽功能
            setTimeout(() => {
                const containers = [
                    document.getElementById('responsibilitiesList'),
                    document.getElementById('skillsList'),
                    document.getElementById('prioritySkillsList'),
                    document.getElementById('bonusSkillsList')
                ];
                
                containers.forEach(container => {
                    if (container) {
                        initDraggableContainer(container);
                    }
                });
            }, 100);
        }

        // 解析列表数据
        function parseList(text, type) {
            if (!text) return [];

            // 如果text是数组，直接返回
            if (Array.isArray(text)) {
                return text.map(item => String(item));
            }

            // 确保text是字符串
            if (typeof text !== 'string') {
                text = String(text);
            }

            // 清理文本
            text = text.trim();

            // 匹配列表项的模式
            const patterns = [
                /(\d+)[\.、]\s*(.*?)(?=\n\d+[\.、]|\n$|$)/g,  // 数字列表
                /[•●]\s*(.*?)(?=\n[•●]|\n$|$)/g,  // 圆点列表
                /[-–—]\s*(.*?)(?=\n[-–—]|\n$|$)/g,  // 短横线列表
                /[①②③④⑤⑥⑦⑧⑨⑩]\s*(.*?)(?=\n[①②③④⑤⑥⑦⑧⑨⑩]|\n$|$)/g  // 中文圆圈数字
            ];

            let items = [];
            let matches;

            // 尝试使用各种模式提取列表项
            for (const pattern of patterns) {
                matches = [...text.matchAll(pattern)];
                if (matches.length > 0) {
                    items = matches.map(match => match[2] || match[1]).filter(item => item.trim().length > 0);
                    break;
                }
            }

            // 如果没有找到列表项，尝试按分号或逗号分割
            if (items.length === 0) {
                if (text.includes('；') || text.includes(';')) {
                    items = text.split(/[；;]/).filter(item => item.trim().length > 0);
                } else if (text.includes('，') || text.includes(',')) {
                    items = text.split(/[，,]/).filter(item => item.trim().length > 0);
                } else {
                    // 最后尝试按行分割
                    items = text.split(/\n+/).filter(line => line.trim().length > 0);
                }
            }

            // 清理每个项目
            items = items.map(item => {
                item = item.trim();
                // 移除开头的数字和标点
                item = item.replace(/^[\d\.、①②③④⑤⑥⑦⑧⑨⑩\s]+/, '');
                // 移除末尾的标点
                item = item.replace(/[，。；,.;\s]+$/, '');
                return item.trim();
            }).filter(item => item.length > 0);

            return items;
        }

        // 显示职责列表
        function displayResponsibilitiesList() {
            const container = document.getElementById('responsibilitiesList');
            if (!container) return;
            
            container.innerHTML = '';
            responsibilities.forEach((responsibility, index) => {
                createDraggableItem(responsibility, index, container);
            });
        }

        // 显示技能列表
        function displaySkillsList() {
            const container = document.getElementById('skillsList');
            if (!container) return;
            
            container.innerHTML = '';
            skills.forEach((skill, index) => {
                createDraggableItem(skill, index, container);
            });
        }

        // 显示优先技能列表
        function displayPrioritySkillsList() {
            const container = document.getElementById('prioritySkillsList');
            if (!container) return;
            
            container.innerHTML = '';
            prioritySkills.forEach((skill, index) => {
                createDraggableItem(skill, index, container);
            });
        }

        // 显示加分项列表
        function displayBonusSkillsList() {
            const container = document.getElementById('bonusSkillsList');
            if (!container) return;
            
            container.innerHTML = '';
            bonusSkills.forEach((skill, index) => {
                createDraggableItem(skill, index, container);
            });
        }

        // 创建可拖拽项目
        function createDraggableItem(content, index, container) {
            const item = document.createElement('div');
            item.className = 'draggable-item';
            item.draggable = true;
            item.dataset.index = index;

            const handle = document.createElement('div');
            handle.className = 'drag-handle';
            item.appendChild(handle);

            const contentDiv = document.createElement('div');
            contentDiv.className = 'item-content';

            const numberSpan = document.createElement('span');
            numberSpan.className = 'item-number';
            numberSpan.textContent = index + 1;
            contentDiv.appendChild(numberSpan);

            const textSpan = document.createElement('span');
            textSpan.textContent = content;
            textSpan.className = 'item-text';
            contentDiv.appendChild(textSpan);

            // 添加编辑按钮
            const editBtn = document.createElement('button');
            editBtn.className = 'edit-item-btn';
            editBtn.innerHTML = '✏️';
            editBtn.title = '编辑';
            editBtn.onclick = () => editItem(item, content, container);
            contentDiv.appendChild(editBtn);

            // 添加删除按钮
            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'delete-item-btn';
            deleteBtn.innerHTML = '🗑️';
            deleteBtn.title = '删除';
            deleteBtn.onclick = () => deleteItem(item, container);
            contentDiv.appendChild(deleteBtn);

            item.appendChild(contentDiv);
            container.appendChild(item);

            // 添加拖拽事件
            item.addEventListener('dragstart', () => {
                item.classList.add('dragging');
            });

            item.addEventListener('dragend', () => {
                item.classList.remove('dragging');
                updateItemsOrder(container);
            });

            return item;
        }

        // 编辑项目
        function editItem(item, originalContent, container) {
            const textSpan = item.querySelector('.item-text');
            const originalText = textSpan.textContent;

            // 创建输入框
            const input = document.createElement('input');
            input.type = 'text';
            input.value = originalText;
            input.className = 'edit-input';

            // 替换文本为输入框
            textSpan.style.display = 'none';
            textSpan.parentNode.insertBefore(input, textSpan);
            input.focus();

            // 处理保存和取消
            function saveEdit() {
                const newContent = input.value.trim();
                if (newContent && newContent !== originalText) {
                    textSpan.textContent = newContent;
                    updateItemContent(container, item.dataset.index, newContent);
                }
                textSpan.style.display = 'inline';
                input.remove();
            }

            function cancelEdit() {
                textSpan.style.display = 'inline';
                input.remove();
            }

            input.addEventListener('blur', saveEdit);
            input.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    saveEdit();
                } else if (e.key === 'Escape') {
                    cancelEdit();
                }
            });
        }

        // 删除项目
        function deleteItem(item, container) {
            if (confirm('确定要删除这个项目吗？')) {
                const index = parseInt(item.dataset.index);
                item.remove();
                removeItemContent(container, index);
                updateItemsOrder(container);
            }
        }

        // 更新项目内容
        function updateItemContent(container, index, newContent) {
            if (container.id === 'responsibilitiesList') {
                responsibilities[index] = newContent;
            } else if (container.id === 'skillsList') {
                skills[index] = newContent;
            } else if (container.id === 'prioritySkillsList') {
                prioritySkills[index] = newContent;
            } else if (container.id === 'bonusSkillsList') {
                bonusSkills[index] = newContent;
            }
        }

        // 删除项目内容
        function removeItemContent(container, index) {
            if (container.id === 'responsibilitiesList') {
                responsibilities.splice(index, 1);
            } else if (container.id === 'skillsList') {
                skills.splice(index, 1);
            } else if (container.id === 'prioritySkillsList') {
                prioritySkills.splice(index, 1);
            } else if (container.id === 'bonusSkillsList') {
                bonusSkills.splice(index, 1);
            }
        }

        // 更新项目顺序
        function updateItemsOrder(container) {
            const items = container.querySelectorAll('.draggable-item');
            items.forEach((item, index) => {
                item.dataset.index = index;
                const numberSpan = item.querySelector('.item-number');
                if (numberSpan) {
                    numberSpan.textContent = index + 1;
                }
            });

            // 更新状态中的顺序
            if (container.id === 'responsibilitiesList') {
                responsibilities = Array.from(items).map(item =>
                    item.querySelector('.item-text').textContent
                );
            } else if (container.id === 'skillsList') {
                skills = Array.from(items).map(item =>
                    item.querySelector('.item-text').textContent
                );
            } else if (container.id === 'prioritySkillsList') {
                prioritySkills = Array.from(items).map(item =>
                    item.querySelector('.item-text').textContent
                );
            } else if (container.id === 'bonusSkillsList') {
                bonusSkills = Array.from(items).map(item =>
                    item.querySelector('.item-text').textContent
                );
            }
        }

        // 确认并生成JD
        async function confirmAndGenerateJD() {
            console.log('🚀 确认并生成JD');
            
            // 构建排序后的数据
            const sortedData = {
                '职位': window.summaryData['职位'] || '',
                '薪资': basicInfo ? basicInfo.salaryRange : (window.summaryData['薪资'] || ''),
                '地区': basicInfo ? basicInfo.workLocation : (window.summaryData['地区'] || ''),
                '行业': window.summaryData['行业'] || ''
            };

            // 添加排序后的核心职责
            responsibilities.forEach((resp, index) => {
                sortedData[`核心职责${index + 1}`] = resp;
            });

            // 添加排序后的核心技能
            if (skills.length > 0) {
                sortedData['核心技能'] = skills.map((skill, index) => `${index + 1}. ${skill}`).join('\n');
            }

            // 添加排序后的拓展技能
            if (prioritySkills.length > 0) {
                sortedData['拓展技能'] = prioritySkills.map((skill, index) => `${index + 1}. ${skill}`).join('\n');
            }

            // 添加排序后的加分项
            if (bonusSkills.length > 0) {
                sortedData['加分项'] = bonusSkills.map((skill, index) => `${index + 1}. ${skill}`).join('\n');
            }

            console.log('📦 发送的排序数据:', sortedData);

            try {
                const response = await fetch(`${JD_GENERATOR_API_BASE}/api/v1/generate_jd`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        company_description: conversationData.company_description,
                        summary_data: window.summaryData,
                        sorted_data: sortedData,
                        basic_info: basicInfo,
                        user_name: currentUser ? currentUser.name : null // 传递用户名
                    })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        // 显示生成的JD
                        document.getElementById('generatedJDContent').value = result.jd_content;
                        // 更新Markdown预览
                        updateMarkdownPreview();
                        
                        // 复制对话历史到修改区域
                        copyConversationHistory();
                        
                        // 进入步骤5（JD预览与编辑）
                        showStep(5);
                        
                        console.log('✅ JD生成成功');
                    } else {
                        throw new Error(result.message || 'JD生成失败');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                console.error('生成JD失败:', error);
                alert(`生成JD失败: ${error.message}`);
            }
        }

        // 修改JD
        async function modifyJD() {
            const modificationRequest = document.getElementById('modificationInput').value.trim();
            if (!modificationRequest) {
                alert('请输入修改要求');
                return;
            }
            
            try {
                const originalJD = document.getElementById('generatedJDContent').value;
                
                // 添加用户修改请求到对话历史
                const modificationArea = document.getElementById('modificationConversationArea');
                const userMsg = document.createElement('div');
                userMsg.className = 'conversation-message user';
                userMsg.textContent = `修改要求: ${modificationRequest}`;
                modificationArea.appendChild(userMsg);
                
                const response = await fetch(`${JD_GENERATOR_API_BASE}/api/v1/modify_jd`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        original_jd: originalJD,
                        modification_request: modificationRequest,
                        conversation_history: conversationData.conversation_history,
                        user_name: currentUser ? currentUser.name : null // 传递用户名
                    })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        document.getElementById('generatedJDContent').value = result.modified_jd;
                        // 更新Markdown预览
                        updateMarkdownPreview();
                        
                        // 添加系统回复到对话历史
                        const systemMsg = document.createElement('div');
                        systemMsg.className = 'conversation-message system';
                        systemMsg.textContent = 'JD已根据您的要求进行修改。';
                        modificationArea.appendChild(systemMsg);
                        
                        // 清空输入框
                        document.getElementById('modificationInput').value = '';
                        
                        // 滚动到底部
                        modificationArea.scrollTop = modificationArea.scrollHeight;
                        
                        alert('JD修改成功！');
                    } else {
                        throw new Error(result.message || 'JD修改失败');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
            } catch (error) {
                console.error('修改JD失败:', error);
                alert(`JD修改失败: ${error.message}`);
            }
        }

        // 初始化拖拽容器
        function initDraggableContainer(container) {
            container.addEventListener('dragover', e => {
                e.preventDefault();
                const afterElement = getDragAfterElement(container, e.clientY);
                const draggable = document.querySelector('.dragging');
                if (draggable) {
                    if (afterElement) {
                        container.insertBefore(draggable, afterElement);
                    } else {
                        container.appendChild(draggable);
                    }
                }
            });
        }

        // 获取拖拽后的位置
        function getDragAfterElement(container, y) {
            const draggableElements = [...container.querySelectorAll('.draggable-item:not(.dragging)')];

            return draggableElements.reduce((closest, child) => {
                const box = child.getBoundingClientRect();
                const offset = y - box.top - box.height / 2;

                if (offset < 0 && offset > closest.offset) {
                    return {offset: offset, element: child};
                } else {
                    return closest;
                }
            }, {offset: Number.NEGATIVE_INFINITY}).element;
        }

        // 确认使用JD
        function confirmJD() {
            const jdContent = document.getElementById('generatedJDContent').value;
            if (!jdContent.trim()) {
                alert('请先生成JD内容');
                return;
            }
            
            // 将生成的JD填入创建表单
            document.getElementById('jobTitle').value = '新生成职位';
            document.getElementById('jdContent').value = jdContent;
            
            closeJDGeneratorModal();
            showCreateJD();
            
            alert('JD已确认使用！已自动填入创建表单。');
        }

        // 重置对话
        function resetConversation() {
            conversationData = {
                company_description: '',
                current_round: 0,
                conversation_history: [],
                isSupplementQuestion: false
            };
            
            // 清空对话区域
            document.getElementById('conversationArea').innerHTML = '';
            
            // 清空选项区域
            document.getElementById('optionsArea').innerHTML = '';
            
            // 重置输入框
            const userInput = document.getElementById('userInput');
            userInput.value = '';
            userInput.placeholder = "请输入您的回答...（输入'生成JD'或'结束'可完成对话）";
            
            // 回到第一步
            currentStep = 1;
            updateStepIndicators();
            
            document.getElementById('step2Content').classList.remove('active');
            document.getElementById('step1Content').classList.add('active');
        }

        // 更新步骤指示器
        function updateStepIndicators() {
            document.querySelectorAll('.step-indicator').forEach((indicator, index) => {
                indicator.classList.remove('active', 'completed');
                if (index + 1 < currentStep) {
                    indicator.classList.add('completed');
                } else if (index + 1 === currentStep) {
                    indicator.classList.add('active');
                }
            });
        }

        // 添加对话消息
        function addConversationMessage(type, content) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `conversation-message ${type}`;

            // 优化文本显示
            let formattedContent = content;

            // 如果是系统消息且内容较长，尝试格式化显示
            if (type === 'system' && content.length > 200) {
                // 检测是否为信息总结
                if (content.includes('信息总结') || content.includes('收集到以下核心要求')) {
                    // 将长文本分段显示
                    formattedContent = formatSummaryMessage(content);
                }
            }

            // 如果是用户消息，尝试分点显示
            if (type === 'user') {
                formattedContent = formatUserMessage(content);
            }

            messageDiv.innerHTML = formattedContent;
            
            document.getElementById('conversationArea').appendChild(messageDiv);
            scrollToBottom();
            
            // 保存到对话历史
            if (!conversationData.conversationHistory) {
                conversationData.conversationHistory = [];
            }
            conversationData.conversationHistory.push({type, content});
        }

        // 添加格式化的对话消息（用于岗位建议等需要格式化的内容）
        function addFormattedConversationMessage(type, content) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `conversation-message ${type}`;

            // 设置样式
            if (type === 'assistant') {
                messageDiv.style.cssText = `
                    background: #f8f9fa;
                    color: #333;
                    padding: 15px 20px;
                    border-radius: 15px 15px 15px 0;
                    margin: 10px auto 10px 0;
                    max-width: 85%;
                    word-wrap: break-word;
                    border: 1px solid #e9ecef;
                    line-height: 1.6;
                `;
            } else {
                // 使用默认样式
                messageDiv.style.cssText = `
                    background: #007bff;
                    color: white;
                    padding: 10px 15px;
                    border-radius: 15px 15px 0 15px;
                    margin: 10px 0 10px auto;
                    max-width: 80%;
                    word-wrap: break-word;
                    align-self: flex-end;
                `;
            }

            // 使用格式化函数处理内容
            messageDiv.innerHTML = formatAdviceContent(content);
            
            document.getElementById('conversationArea').appendChild(messageDiv);
            scrollToBottom();
            
            // 保存到对话历史
            if (!conversationData.conversationHistory) {
                conversationData.conversationHistory = [];
            }
            conversationData.conversationHistory.push({type, content});
        }

        // 格式化用户消息，分点显示
        function formatUserMessage(content) {
            // 如果内容包含分号，按分号分割并显示为列表
            if (content.includes('；') || content.includes(';')) {
                const items = content.replace(/；/g, ';').split(';').filter(item => item.trim());
                if (items.length > 1) {
                    return items.map((item, index) => `<div class="user-point">${index + 1}. ${item.trim()}</div>`).join('');
                }
            }

            // 如果内容包含换行，按换行分割并显示为列表
            if (content.includes('\n')) {
                const lines = content.split('\n').filter(line => line.trim());
                if (lines.length > 1) {
                    return lines.map((line, index) => `<div class="user-point">${index + 1}. ${line.trim()}</div>`).join('');
                }
            }

            return content;
        }

        // 格式化总结消息
        function formatSummaryMessage(content) {
            // 提取关键信息并格式化显示
            let formatted = '<div class="summary-message">';

            // 尝试提取薪资范围
            const salaryMatch = content.match(/薪资范围[：:]\s*([^，。\n]+)/);
            if (salaryMatch) {
                formatted += `<div class="summary-item"><strong>薪资范围:</strong> ${salaryMatch[1]}</div>`;
            }

            // 尝试提取核心职责
            const respMatch = content.match(/核心职责[：:]\s*([^。]+)/);
            if (respMatch) {
                const responsibilities = respMatch[1].split(/[,，、]/).filter(item => item.trim());
                formatted += '<div class="summary-item"><strong>核心职责:</strong></div><ul>';
                responsibilities.forEach(item => {
                    formatted += `<li>${item.trim()}</li>`;
                });
                formatted += '</ul>';
            }

            // 尝试提取必备能力
            const skillsMatch = content.match(/必备能力[：:]\s*([^。]+)/);
            if (skillsMatch) {
                const skills = skillsMatch[1].split(/[,，、]/).filter(item => item.trim());
                formatted += '<div class="summary-item"><strong>必备能力:</strong></div><ul>';
                skills.forEach(item => {
                    formatted += `<li>${item.trim()}</li>`;
                });
                formatted += '</ul>';
            }

            // 如果没有成功提取结构化信息，则保持原样
            if (formatted === '<div class="summary-message">') {
                return content;
            }

            formatted += '</div>';
            return formatted;
        }

        // 滚动到底部
        function scrollToBottom() {
            const conversationArea = document.getElementById('conversationArea');
            conversationArea.scrollTop = conversationArea.scrollHeight;
        }

        // 显示岗位建议内容
        function showPositionAdvice(advice) {
            console.log('🎯 显示岗位建议:', advice);
            
            // 检查是否是对话内容而不是岗位建议
            if (advice.includes('开始第一轮对话') || advice.includes('核心职责确定类')) {
                console.log('检测到对话内容，直接进入对话模式');
                // 直接进入对话模式
                showConversationStep(advice);
                return;
            }
            
            // 创建建议显示区域
            const adviceArea = document.createElement('div');
            adviceArea.className = 'position-advice-area';
            adviceArea.innerHTML = `
                <div class="advice-content">
                    <h4 class="advice-title">
                        <i class="fas fa-lightbulb"></i> AI岗位分析建议
                    </h4>
                    <div class="advice-text">${formatAdviceContent(advice)}</div>
                </div>
                <div class="advice-actions">
                    <button class="btn-secondary" onclick="resetPositionAdvice()">
                        <i class="fas fa-undo"></i> 重新生成
                    </button>
                    <button class="search-btn" onclick="proceedToConversation()">
                        <i class="fas fa-arrow-right"></i> 开始创建JD
                    </button>
                </div>
            `;
            
            // 插入到表单区域后面
            const formActions = document.querySelector('#step1Content .form-actions');
            if (formActions) {
                formActions.parentNode.insertBefore(adviceArea, formActions.nextSibling);
                
                // 隐藏表单区域
                const formArea = document.querySelector('#step1Content .form-group');
                if (formArea) {
                    formArea.style.display = 'none';
                }
                
                // 隐藏按钮区域
                formActions.style.display = 'none';
            }
            
            console.log('✅ 岗位建议显示完成');
        }
        
        // 格式化岗位建议内容
        function formatAdviceContent(content) {
            // 检查是否包含对话问题（如【核心职责确定类】）
            if (content.includes('【') && content.includes('】')) {
                return formatConversationContent(content);
            }
            
            // 转换markdown格式到HTML
            let formattedContent = content
                // 处理标题
                .replace(/###\s*(.*?)(?=\n|$)/g, '<h4 style="color: #007bff; margin: 15px 0 10px 0; font-size: 16px;">$1</h4>')
                .replace(/##\s*(.*?)(?=\n|$)/g, '<h3 style="color: #007bff; margin: 20px 0 15px 0; font-size: 18px;">$1</h3>')
                // 处理粗体
                .replace(/\*\*(.*?)\*\*/g, '<strong style="color: #007bff;">$1</strong>')
                // 处理列表项
                .replace(/^\d+\.\s*(.*?)(?=\n|$)/gm, '<li style="margin: 8px 0;">$1</li>')
                .replace(/^-\s*(.*?)(?=\n|$)/gm, '<li style="margin: 8px 0;">$1</li>')
                // 处理段落
                .replace(/\n\n/g, '</p><p style="margin: 10px 0; line-height: 1.6;">')
                .replace(/\n/g, '<br/>');

            // 包装在段落中
            formattedContent = '<p style="margin: 10px 0; line-height: 1.6;">' + formattedContent + '</p>';

            // 处理列表
            formattedContent = formattedContent.replace(/(<li.*?<\/li>)/gs, function (match) {
                return '<ul style="margin: 10px 0; padding-left: 20px;">' + match + '</ul>';
            });

            return formattedContent;
        }

        // 格式化对话内容（包含问题部分）
        function formatConversationContent(content) {
            let formattedContent = content;
            
            // 分离岗位建议和对话问题
            const parts = content.split(/【([^】]+)】/);
            let result = '';
            
            for (let i = 0; i < parts.length; i++) {
                if (i % 2 === 0) {
                    // 这是普通内容（岗位建议部分）
                    if (parts[i].trim()) {
                        result += formatAdviceContent(parts[i]);
                    }
                } else {
                    // 这是对话问题部分
                    const questionType = parts[i];
                    const questionContent = parts[i + 1] || '';
                    
                    result += `
                        <div style="background: #e3f2fd; border-left: 4px solid #007bff; padding: 15px; margin: 15px 0; border-radius: 0 8px 8px 0;">
                            <div style="color: #007bff; font-weight: 600; margin-bottom: 10px;">
                                【${questionType}】
                            </div>
                            <div style="color: #333; line-height: 1.6;">
                                ${questionContent.replace(/\n/g, '<br/>')}
                            </div>
                        </div>
                    `;
                    i++; // 跳过下一个部分，因为我们已经处理了
                }
            }
            
            return result;
        }
        
        // 重置岗位建议
        function resetPositionAdvice() {
            console.log('🔄 开始重置岗位建议...');
            
            // 移除建议区域
            const adviceArea = document.querySelector('.position-advice-area');
            if (adviceArea) {
                adviceArea.remove();
                console.log('✅ 移除岗位建议区域');
            }
            
            // 显示表单区域
            const formArea = document.querySelector('#step1Content .form-group');
            if (formArea) {
                formArea.style.display = 'block';
                console.log('✅ 显示表单区域');
            } else {
                console.error('❌ 找不到表单区域元素');
            }
            
            // 显示按钮区域
            const formActions = document.querySelector('#step1Content .form-actions');
            if (formActions) {
                formActions.style.display = 'flex';
                console.log('✅ 显示按钮区域');
            } else {
                console.error('❌ 找不到按钮区域元素');
            }
            
            // 清空输入框
            const positionDescription = document.getElementById('positionDescription');
            if (positionDescription) {
                positionDescription.value = '';
                console.log('✅ 清空输入框');
            } else {
                console.error('❌ 找不到输入框元素');
            }
            
            console.log('✅ 重置岗位建议完成');
        }
        
        // 进入基本信息配置阶段
        function proceedToBasicInfo() {
            console.log('🚀 开始进入基本信息配置阶段');
            
            // 获取岗位建议内容
            const adviceText = document.querySelector('.advice-text');
            const advice = adviceText ? adviceText.textContent : '';
            
            console.log('📝 获取到的岗位建议内容:', advice);
            
            // 保存岗位建议到对话数据
            conversationData.positionAdvice = advice;
            
            // 显示基本信息配置步骤
            showStep(2);
        }

        // 进入对话收集阶段
        function proceedToConversation() {
            console.log('🚀 开始进入对话收集阶段');
            
            // 获取岗位建议内容
            const adviceText = document.querySelector('.advice-text');
            const advice = adviceText ? adviceText.textContent : '';
            
            console.log('📝 获取到的岗位建议内容:', advice);
            
            // 显示对话步骤
            showConversationStep(advice);
        }

        // 保存基本信息并进入对话
        function saveBasicInfoAndProceed() {
            console.log('💾 保存基本信息并进入对话');
            
            // 验证必填字段
            const requiredFields = [
                {id: 'salaryRange', name: '薪资范围'},
                {id: 'jobType', name: '岗位性质'},
                {id: 'educationLevel', name: '学历要求'},
                {id: 'workExperience', name: '工作经验'},
                {id: 'workLocation', name: '工作地点'},
                {id: 'workMode', name: '工作方式'}
            ];

            for (const field of requiredFields) {
                const element = document.getElementById(field.id);
                if (!element || !element.value.trim()) {
                    alert(`请填写${field.name}`);
                    if (element) element.focus();
                    return;
                }
            }

            // 收集基本信息
            basicInfo = {
                salaryRange: document.getElementById('salaryRange').value.trim(),
                jobType: document.getElementById('jobType').value,
                educationLevel: document.getElementById('educationLevel').value,
                ageRange: document.getElementById('ageRange').value.trim(),
                workExperience: document.getElementById('workExperience').value,
                workLocation: document.getElementById('workLocation').value.trim(),
                workMode: document.getElementById('workMode').value,
                majorRequirement: document.getElementById('majorRequirement').value.trim()
            };

            console.log('✅ 基本信息保存成功:', basicInfo);
            
            // 进入对话步骤
            showStep(3);
            
            // 开始对话
            startConversation();
        }

        // 跳过基本信息配置并进入对话
        function skipBasicInfoAndProceed() {
            console.log('⏭️ 跳过基本信息配置');
            basicInfo = null;
            
            // 进入对话步骤
            showStep(3);
            
            // 开始对话
            startConversation();
        }

        // 测试岗位建议功能
        function testPositionAdvice() {
            console.log('🧪 开始测试岗位建议功能...');
            
            // 模拟岗位建议数据
            const mockAdvice = `根据您提供的岗位描述，我为您提供以下建议：

1. **岗位定位**：AI算法工程师，专注于大模型应用落地
2. **技能要求**：
   - 深度学习框架：PyTorch、TensorFlow
   - 大模型技术：Transformer、BERT、GPT等
   - 编程语言：Python、C++
   - 云平台：AWS、阿里云等

3. **薪资建议**：15-20k月薪在广东地区对于3年以上经验的AI工程师来说是合理的

4. **JD生成建议**：建议重点突出大模型应用落地的实际项目经验

现在开始收集详细信息...`;
            
            // 保存公司描述
            const positionDescription = document.getElementById('positionDescription').value || 'AI算法工程师';
            conversationData.company_description = positionDescription;
            
            // 显示岗位建议
            showPositionAdvice(mockAdvice);
            
            console.log('✅ 测试岗位建议功能完成');
        }

        // 切换JD预览模式
        function toggleJDPreviewMode(mode) {
            const textarea = document.getElementById('generatedJDContent');
            const preview = document.getElementById('markdownPreview');
            const markdownBtn = document.getElementById('viewMarkdownBtn');
            const rawBtn = document.getElementById('viewRawBtn');
            
            if (mode === 'markdown') {
                textarea.style.display = 'none';
                preview.style.display = 'block';
                markdownBtn.classList.add('active');
                rawBtn.classList.remove('active');
                updateMarkdownPreview();
            } else {
                textarea.style.display = 'block';
                preview.style.display = 'none';
                markdownBtn.classList.remove('active');
                rawBtn.classList.add('active');
            }
        }
        
        // 更新Markdown预览
        function updateMarkdownPreview() {
            const textarea = document.getElementById('generatedJDContent');
            const preview = document.getElementById('markdownPreview');
            
            if (textarea && preview) {
                const content = textarea.value;
                const htmlContent = parseMarkdown(content);
                preview.innerHTML = htmlContent;
            }
        }
        
        // 简单的Markdown解析器
        function parseMarkdown(text) {
            if (!text) return '';
            
            // 处理标题
            text = text.replace(/^# (.*?)$/gm, '<h1>$1</h1>');
            text = text.replace(/^## (.*?)$/gm, '<h2>$1</h2>');
            text = text.replace(/^### (.*?)$/gm, '<h3>$1</h3>');
            text = text.replace(/^#### (.*?)$/gm, '<h4>$1</h4>');
            
            // 处理粗体和斜体
            text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
            text = text.replace(/\*(.*?)\*/g, '<em>$1</em>');
            
            // 处理列表
            text = text.replace(/^\d+\. (.*?)$/gm, '<li>$1</li>');
            text = text.replace(/^- (.*?)$/gm, '<li>$1</li>');
            
            // 将连续的li元素包装在ul或ol中
            let inList = false;
            let listType = '';
            const lines = text.split('\n');
            const processedLines = [];
            
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i];
                
                if (line.startsWith('<li>')) {
                    if (!inList) {
                        const isOrderedList = i > 0 && /^\d+\. /.test(lines[i - 1]);
                        listType = isOrderedList ? 'ol' : 'ul';
                        processedLines.push(`<${listType}>`);
                        inList = true;
                    }
                    processedLines.push(line);
                } else {
                    if (inList) {
                        processedLines.push(`</${listType}>`);
                        inList = false;
                    }
                    processedLines.push(line);
                }
            }
            
            if (inList) {
                processedLines.push(`</${listType}>`);
            }
            
            text = processedLines.join('\n');
            
            // 处理段落
            text = text.replace(/^(?!<[hou]|<li|<\/[oul])(.*?)$/gm, '<p>$1</p>');
            
            // 处理换行
            text = text.replace(/\n\n+/g, '<br>');
            
            return text;
        }

        // 添加键盘事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            // 监听用户输入框的Enter键
            const userInput = document.getElementById('userInput');
            if (userInput) {
                userInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        sendUserInput();
                    }
                });
            }
            
            // 监听修改输入框的Enter键
            const modificationInput = document.getElementById('modificationInput');
            if (modificationInput) {
                modificationInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        modifyJD();
                    }
                });
            }
        });

        // 生成JD（保留原函数名以兼容）
        async function generateJD() {
            // 如果当前在对话步骤，直接生成JD
            if (currentStep === 2) {
                try {
                    const response = await fetch(`${JD_GENERATOR_API_BASE}/api/v1/conversation`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            company_description: conversationData.company_description,
                            current_round: conversationData.current_round,
                            selected_options: {},
                            custom_inputs: {},
                            user_input: '生成JD',
                            user_name: currentUser ? currentUser.name : null // 传递用户名
                        })
                    });
                    
                    if (response.ok) {
                        const result = await response.json();
                        if (result.success && result.is_summary_round) {
                            showSummaryStep(result.summary_data);
                        }
                    }
                } catch (error) {
                    console.error('生成JD失败:', error);
                    alert(`生成JD失败: ${error.message}`);
                }
            }
        }



        // 导出结果
        async function exportResults() {
            // 获取导出按钮
            const exportBtn = document.querySelector('.search-btn[onclick="exportResults()"]');
            if (!exportBtn) {
                console.error('找不到导出按钮');
                return;
            }
            
            // 保存原始按钮状态
            const originalText = exportBtn.innerHTML;
            const originalDisabled = exportBtn.disabled;
            const originalClasses = exportBtn.className;
            
            try {
                // 显示加载状态
                exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 导出中...';
                exportBtn.disabled = true;
                exportBtn.classList.add('exporting');
                
                // 调用导出API
                const response = await fetch(`${RESUME_EVAL_API_BASE}/export_results_to_excel`);
                
                if (response.ok) {
                    // 获取文件名
                    const contentDisposition = response.headers.get('content-disposition');
                    let filename = '简历评估结果.xlsx';
                    if (contentDisposition) {
                        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
                        if (filenameMatch) {
                            filename = filenameMatch[1];
                        }
                    }
                    
                    // 下载文件
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                    
                    // 显示成功消息
                    showNotification('导出成功！', 'success');
                    
                    // 显示完成状态
                    exportBtn.classList.remove('exporting');
                    exportBtn.classList.add('export-complete');
                    exportBtn.innerHTML = '<i class="fas fa-check"></i> 导出完成';
                    exportBtn.disabled = false;
                    
                    // 3秒后恢复原始状态
                    setTimeout(() => {
                        exportBtn.innerHTML = originalText;
                        exportBtn.className = originalClasses;
                        exportBtn.disabled = originalDisabled;
                    }, 3000);
                    
                } else {
                    const errorData = await response.json();
                    throw new Error(errorData.message || '导出失败');
                }
            } catch (error) {
                console.error('导出失败:', error);
                showNotification(`导出失败: ${error.message}`, 'error');
                
                // 错误时立即恢复按钮状态
                exportBtn.innerHTML = originalText;
                exportBtn.className = originalClasses;
                exportBtn.disabled = originalDisabled;
            }
        }
        
        // 显示通知
        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
            `;
            
            // 添加到页面
            document.body.appendChild(notification);
            
            // 显示动画
            setTimeout(() => notification.classList.add('show'), 100);
            
            // 自动隐藏
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => document.body.removeChild(notification), 300);
            }, 3000);
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const candidateModal = document.getElementById('candidateModal');
            const platformModal = document.getElementById('platformModal');
            const jdGeneratorModal = document.getElementById('jdGeneratorModal');
            const bossQRCodeModal = document.getElementById('bossQRCodeModal');
            
            if (event.target === candidateModal) {
                closeModal();
            }
            if (event.target === platformModal) {
                closePlatformModal();
            }
            if (event.target === jdGeneratorModal) {
                closeJDGeneratorModal();
            }
            if (event.target === bossQRCodeModal) {
                closeBossQRCodeModal();
            }
        }

        // 添加回车键发送功能
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Enter' && event.target.id === 'userInput') {
                event.preventDefault();
                sendUserInput();
            }
        });

        // 页面加载时的调试信息
        console.log('=== 页面加载完成 ===');
        console.log('智能简历筛选工具 v4.0 已加载');
        console.log('当前时间:', new Date().toLocaleString());
        console.log('新增功能：分阶段处理流程');
        console.log('第一阶段：上传文件并立即提取基本信息显示');

        
        // 启动协作结果自动刷新
        startCollaborationAutoRefresh();
        console.log('第二阶段：后台进行AI评估，实时更新候选人信息');
        console.log('新增功能：Boss直聘二维码登录');
        console.log('支持向resume_score.py发送login_boss请求');
        console.log('支持显示后端返回的二维码');
        console.log('支持登录状态轮询检查');
        console.log('支持二维码刷新和过期处理');
        console.log('修复错误：searchingText元素ID不存在的问题');
        
        // 协作功能自动刷新
        function startCollaborationAutoRefresh() {
            console.log('启动协作结果自动刷新...');
            
            // 立即检查一次协作任务
            checkCollaborationTasks();
            
            // 每30秒检查一次是否有新的协作任务完成
            setInterval(checkCollaborationTasks, 30000);
        }
        
        // 检查协作任务
        async function checkCollaborationTasks() {
            try {
                const response = await fetch(`${RESUME_EVAL_API_BASE}/collaboration/list_tasks`);
                if (response.ok) {
                    const result = await response.json();
                    console.log('协作任务检查结果:', result);
                    
                    if (result.success && result.tasks.length > 0) {
                        // 检查是否有新完成的协作任务
                        const completedTasks = result.tasks.filter(task => 
                            task.status === 'completed' && 
                            task.job_title && 
                            !localStorage.getItem(`collaboration_${task.task_id}`)
                        );
                        
                        if (completedTasks.length > 0) {
                            console.log('发现新的协作评估结果，自动刷新页面');
                            // 标记已处理的任务
                            completedTasks.forEach(task => {
                                localStorage.setItem(`collaboration_${task.task_id}`, 'processed');
                            });
                            
                            // 显示通知
                            showCollaborationNotification(completedTasks);
                            
                            // 延迟刷新页面，让用户看到通知
                            setTimeout(() => {
                                location.reload();
                            }, 3000);
                        } else {
                            // 检查是否有正在运行的任务
                            const runningTasks = result.tasks.filter(task => 
                                task.status === 'running' && 
                                task.job_title
                            );
                            
                            if (runningTasks.length > 0) {
                                console.log('发现正在运行的协作评估任务');
                                const progressText = document.querySelector('.searching-text');
                                if (progressText) {
                                    progressText.textContent = '正在AI评估中...';
                                }
                                
                                // 显示评估进度
                                const runningTask = runningTasks[0];
                                const progress = runningTask.total > 0 ? Math.round((runningTask.completed / runningTask.total) * 100) : 0;
                                const progressSubtext = document.querySelector('.searching-subtext');
                                if (progressSubtext) {
                                    progressSubtext.textContent = `评估进度: ${runningTask.completed}/${runningTask.total} (${progress}%)`;
                                }
                            } else {
                                // 检查是否有等待中的任务
                                const pendingTasks = result.tasks.filter(task => 
                                    task.status === 'pending' && 
                                    task.job_title
                                );
                                
                                if (pendingTasks.length > 0) {
                                    console.log('发现等待中的协作评估任务');
                                    const progressText = document.querySelector('.searching-text');
                                    if (progressText) {
                                        progressText.textContent = '正在准备AI评估...';
                                    }
                                }
                            }
                        }
                    } else {
                        console.log('没有找到协作任务或API返回失败');
                        // 如果爬取完成但没有找到协作任务，可能是延迟启动
                        const progressText = document.querySelector('.searching-text');
                        if (progressText && progressText.textContent.includes('爬取完成')) {
                            progressText.textContent = '正在准备AI评估...';
                        }
                    }
                }
            } catch (error) {
                console.error('协作结果自动刷新检查失败:', error);
            }
        }
        
        // 显示协作结果通知
        function showCollaborationNotification(completedTasks) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #10b981;
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                max-width: 300px;
                font-size: 14px;
            `;
            
            const taskInfo = completedTasks.map(task => 
                `${task.job_title} (${task.total}份简历)`
            ).join(', ');
            
            notification.innerHTML = `
                <div style="font-weight: bold; margin-bottom: 5px;">🎉 协作评估完成</div>
                <div>${taskInfo}</div>
                <div style="font-size: 12px; margin-top: 5px;">页面将在3秒后自动刷新...</div>
            `;
            
            document.body.appendChild(notification);
            
            // 5秒后自动移除通知
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        }
        console.log('修复错误：避免重复API调用和undefined task_id的问题');
        console.log('修复错误：优势和风险信息提取和显示问题');
        console.log('解析函数已更新，支持从第三轮评估报告提取基本信息');
        console.log('修复逻辑：优先从简历文件提取基本信息，备选从第三轮报告提取');
        console.log('修复显示：优先使用candidate_name作为姓名，避免显示"未知"');
        console.log('默认值已从"未知"改为"未提及"');
        console.log('正则表达式已修复，支持正确的字段分隔符匹配');
        
        // 强制刷新缓存
        if (window.performance && window.performance.navigation.type === window.performance.navigation.TYPE_BACK_FORWARD) {
            console.log('检测到浏览器后退/前进，强制刷新页面');
            window.location.reload(true);
        }

        // 测试正则表达式的函数（可在浏览器控制台调用）
        function testRegex() {
            console.log('🧪 开始测试正则表达式...');
            
            const testText = `**候选人**：梁朝策 | **目标岗位**：AI产品经理 | **当前职位**：产品经理
**年龄**：29岁 | **学历**：本科(软件工程) | **工作经验**：8年 | **期望薪资**：23-30K`;
            
            console.log('测试文本:', testText);
            
            // 测试姓名提取
            const namePattern = /\*\*候选人\*\*[：:]\s*([^|]+?)(?=\s*\||\s*$)/;
            const nameMatch = testText.match(namePattern);
            console.log('姓名匹配结果:', nameMatch);
            
            // 测试年龄提取
            const agePattern = /\*\*年龄\*\*[：:]\s*([^|]+?)(?=\s*\||\s*$)/;
            const ageMatch = testText.match(agePattern);
            console.log('年龄匹配结果:', ageMatch);
            
            // 测试学历提取
            const educationPattern = /\*\*学历\*\*[：:]\s*([^|]+?)(?=\s*\||\s*$)/;
            const educationMatch = testText.match(educationPattern);
            console.log('学历匹配结果:', educationMatch);
            
            // 测试工作经验提取
            const experiencePattern = /\*\*工作经验\*\*[：:]\s*([^|]+?)(?=\s*\||\s*$)/;
            const experienceMatch = testText.match(experiencePattern);
            console.log('工作经验匹配结果:', experienceMatch);
        }

        // 测试解析函数的函数（可在浏览器控制台调用）
        function testParsingFunctions() {
            console.log('🧪 开始测试解析函数...');
            
            // 测试第三轮基本信息解析 - 使用实际的格式
            const testThirdRoundContent = `## 基本信息
**候选人**：梁朝策 | **目标岗位**：AI产品经理 | **当前职位**：产品经理
**年龄**：29岁 | **学历**：本科(软件工程) | **工作经验**：8年 | **期望薪资**：23-30K

## 评估总结
**总分**：91/100 | **评级**：优秀 | **JD匹配度**：93% | **录用建议**：强烈推荐`;
            
            console.log('测试内容:', testThirdRoundContent);
            const thirdRoundResult = parseThirdRoundBasicInfo(testThirdRoundContent);
            console.log('第三轮解析结果:', thirdRoundResult);
            
            // 测试第一轮优势和风险解析
            const testFirstRoundContent = `- **优势:**
    - 多个从0到1构建AI Agent系统的项目经验
    - 深入掌握LangChain/LangGraph框架
    - 全栈开发能力突出

- **风险与不足:**
    - 年龄42岁，略高于常规AI岗位平均年龄
    - 无明显硬性门槛"潜在风险"项`;
            
            const firstRoundResult = parseFirstRoundAdvantagesAndRisks(testFirstRoundContent);
            console.log('第一轮解析结果:', firstRoundResult);
            
            // 测试简历基本信息提取
            console.log('🧪 测试简历基本信息提取...');
            const testBasicInfo = {
                name: '张三',
                age: '28',
                education: '本科',
                experience: '5年'
            };
            console.log('模拟简历基本信息:', testBasicInfo);
            console.log('姓名:', testBasicInfo.name || testBasicInfo['name'] || '未提及');
            console.log('年龄:', testBasicInfo.age || testBasicInfo['age'] || '未提及');
            console.log('学历:', testBasicInfo.education || testBasicInfo['education'] || '未提及');
            console.log('工作经验:', testBasicInfo.experience || testBasicInfo['experience'] || '未提及');
            
            // 测试逻辑优先级
            console.log('🧪 测试逻辑优先级...');
            console.log('优先级1: 任务详情中的基本信息 (detail.basic_info)');
            console.log('优先级2: 第三轮评估报告中的基本信息');
            console.log('优先级3: 默认值 "未提及"');
            
            // 测试姓名显示逻辑
            console.log('🧪 测试姓名显示逻辑...');
            const testCases = [
                { basicInfoName: '张三', candidateName: '李四', expected: '张三' },
                { basicInfoName: '未知', candidateName: '李四', expected: '李四' },
                { basicInfoName: '未提及', candidateName: '李四', expected: '李四' },
                { basicInfoName: '', candidateName: '李四', expected: '李四' }
            ];
            
            testCases.forEach((testCase, index) => {
                const result = (testCase.basicInfoName && testCase.basicInfoName !== '未知' && testCase.basicInfoName !== '未提及') 
                    ? testCase.basicInfoName 
                    : testCase.candidateName;
                console.log(`测试${index + 1}: basicInfo.name="${testCase.basicInfoName}", candidate_name="${testCase.candidateName}" => 显示="${result}" (期望: "${testCase.expected}")`);
            });
            
            // 测试分阶段处理流程
            console.log('🧪 测试分阶段处理流程...');
            console.log('第一阶段：上传文件并立即提取基本信息显示');
            console.log('第二阶段：后台进行AI评估，实时更新候选人信息');
            console.log('状态标记：extracted(已提取) -> evaluated(已评估)');
        }

        // 测试对话窗口显示的函数（可在浏览器控制台调用）
        function testConversationWindow() {
            console.log('🧪 开始测试对话窗口显示...');
            
            // 检查元素是否存在
            const step1Content = document.getElementById('step1Content');
            const step2Content = document.getElementById('step2Content');
            const conversationArea = document.getElementById('conversationArea');
            
            console.log('检查元素:');
            console.log('step1Content:', step1Content);
            console.log('step2Content:', step2Content);
            console.log('conversationArea:', conversationArea);
            
            if (step1Content && step2Content && conversationArea) {
                console.log('✅ 所有必需元素都存在');
                
                // 检查当前状态
                console.log('当前状态:');
                console.log('step1Content classes:', step1Content.classList.toString());
                console.log('step2Content classes:', step2Content.classList.toString());
                console.log('currentStep:', currentStep);
                
                // 手动触发对话步骤
                console.log('🚀 手动触发对话步骤...');
                showConversationStep('测试对话内容');
                
                // 检查切换后的状态
                setTimeout(() => {
                    console.log('切换后状态:');
                    console.log('step1Content classes:', step1Content.classList.toString());
                    console.log('step2Content classes:', step2Content.classList.toString());
                    console.log('currentStep:', currentStep);
                    
                    const step2Style = window.getComputedStyle(step2Content);
                    console.log('step2Content display:', step2Style.display);
                    console.log('step2Content visibility:', step2Style.visibility);
                }, 100);
            } else {
                console.error('❌ 缺少必需元素');
            }
        }

        // 显示提示信息
        function showAlert(message, type = 'info') {
            // 创建提示元素
            const alertDiv = document.createElement('div');
            alertDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                max-width: 300px;
                word-wrap: break-word;
                animation: slideIn 0.3s ease;
            `;
            
            // 根据类型设置颜色
            switch(type) {
                case 'success':
                    alertDiv.style.background = '#10b981';
                    break;
                case 'error':
                    alertDiv.style.background = '#ef4444';
                    break;
                case 'warning':
                    alertDiv.style.background = '#f59e0b';
                    break;
                default:
                    alertDiv.style.background = '#3b82f6';
            }
            
            alertDiv.textContent = message;
            document.body.appendChild(alertDiv);
            
            // 3秒后自动消失
            setTimeout(() => {
                alertDiv.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.parentNode.removeChild(alertDiv);
                    }
                }, 300);
            }, 3000);
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // ==================== 人才库功能 ====================
        
        // 加载人才库数据
        async function loadTalentPoolData() {
            try {
                // 显示加载状态
                showLoadingState();
                
                // 从简历评估系统获取真实数据
                const response = await fetch(`${RESUME_EVAL_API_BASE}/api/history`);
                if (response.ok) {
                    const result = await response.json();
                    if (result.success && result.data && result.data.length > 0) {
                        // 处理评估结果，筛选通过的候选人
                        const qualifiedCandidates = processEvaluationResults(result.data);
                        
                        if (Object.keys(qualifiedCandidates).length === 0) {
                            showNoQualifiedCandidates();
                            return;
                        }

                        // 更新统计信息
                        updateTalentPoolStats(qualifiedCandidates);
                        
                        // 更新职位筛选器
                        updatePositionFilter(qualifiedCandidates);
                        
                        // 显示职位列表
                        displayPositions(qualifiedCandidates);
                        
                        // 保存到localStorage以便后续使用
                        localStorage.setItem('talentPool', JSON.stringify(qualifiedCandidates));
                        
                    } else {
                        showNoQualifiedCandidates();
                    }
                } else {
                    console.warn('获取评估结果失败，状态码:', response.status);
                    showNoQualifiedCandidates();
                }
            } catch (error) {
                console.error('加载人才库数据失败:', error);
                showNoQualifiedCandidates();
            }
        }

        // 处理评估结果，筛选通过的候选人
        function processEvaluationResults(evaluationData) {
            const qualifiedCandidates = {};
            
            evaluationData.forEach(task => {
                if (task.details && Array.isArray(task.details)) {
                    task.details.forEach(detail => {
                        // 检查候选人是否通过评估
                        if (detail.success && detail.qualified === true) {
                            // 获取职位名称
                            const position = getPositionFromTask(task, detail);
                            
                            // 提取候选人基本信息
                            const candidate = extractCandidateInfo(detail);
                            
                            if (candidate && position) {
                                if (!qualifiedCandidates[position]) {
                                    qualifiedCandidates[position] = [];
                                }
                                
                                // 检查是否已存在该候选人
                                const existingIndex = qualifiedCandidates[position].findIndex(c => c.id === candidate.id);
                                if (existingIndex === -1) {
                                    qualifiedCandidates[position].push(candidate);
                                }
                            }
                        }
                    });
                }
            });
            
            return qualifiedCandidates;
        }

        // 从任务中获取职位名称
        function getPositionFromTask(task, detail) {
            // 优先从任务名称获取
            if (task.task_name) {
                return task.task_name;
            }
            
            // 从JD文件名获取
            if (task.jd_file) {
                const fileName = task.jd_file.split('/').pop() || task.jd_file;
                return fileName.replace(/\.(md|txt|json)$/i, '').trim();
            }
            
            // 从候选人详情中获取
            if (detail.position_title) {
                return detail.position_title;
            }
            
            return '未知职位';
        }

        // 提取候选人信息
        function extractCandidateInfo(detail) {
            try {
                // 基本信息
                const basicInfo = detail.basic_info || {};
                const name = basicInfo.name || detail.candidate_name || '未知姓名';
                const age = basicInfo.age || '未知';
                const experience = basicInfo.experience || '未知';
                const education = basicInfo.education || '未知';
                
                // 评估分数
                const score = detail.final_score || 0;
                
                // 生成唯一ID
                const id = generateCandidateId(name);
                
                return {
                    id: id,
                    name: name,
                    age: age,
                    experience: experience,
                    education: education,
                    score: score,
                    contactStatus: 'pending', // 默认待联系状态
                    taskId: detail.task_id || 'unknown',
                    evaluationDate: detail.evaluation_date || new Date().toISOString()
                };
            } catch (error) {
                console.error('提取候选人信息失败:', error);
                return null;
            }
        }

        // 显示加载状态
        function showLoadingState() {
            const container = document.getElementById('talentPositionsContainer');
            const emptyState = document.getElementById('talentEmptyState');
            
            container.innerHTML = `
                <div style="text-align: center; padding: 40px; color: #666;">
                    <div style="font-size: 3rem; margin-bottom: 20px;">⏳</div>
                    <div>正在加载人才库数据...</div>
                    <div style="margin-top: 10px; font-size: 0.9rem; opacity: 0.7;">从简历评估系统获取最新数据</div>
                </div>
            `;
            
            emptyState.style.display = 'none';
        }

        // 显示没有通过候选人的状态
        function showNoQualifiedCandidates() {
            const container = document.getElementById('talentPositionsContainer');
            const emptyState = document.getElementById('talentEmptyState');
            
            container.innerHTML = '';
            
            emptyState.innerHTML = `
                <div class="empty-icon">👥</div>
                <div class="empty-title">暂无通过评估的候选人</div>
                <div class="empty-desc">当前没有符合要求的候选人，请继续筛选简历或调整评估标准</div>
            `;
            emptyState.style.display = 'block';
            
            // 重置统计信息
            document.getElementById('totalCandidates').textContent = '0';
            document.getElementById('contactedCandidates').textContent = '0';
            document.getElementById('pendingCandidates').textContent = '0';
            document.getElementById('totalPositions').textContent = '0';
        }

        // 更新人才库统计信息
        function updateTalentPoolStats(talentPoolData) {
            let totalCandidates = 0;
            let contactedCandidates = 0;
            let pendingCandidates = 0;
            const positions = Object.keys(talentPoolData);

            positions.forEach(position => {
                const candidates = talentPoolData[position];
                totalCandidates += candidates.length;
                candidates.forEach(candidate => {
                    if (candidate.contactStatus === 'contacted') {
                        contactedCandidates++;
                    } else {
                        pendingCandidates++;
                    }
                });
            });

            document.getElementById('totalCandidates').textContent = totalCandidates;
            document.getElementById('contactedCandidates').textContent = contactedCandidates;
            document.getElementById('pendingCandidates').textContent = pendingCandidates;
            document.getElementById('totalPositions').textContent = positions.length;
        }

        // 更新职位筛选器
        function updatePositionFilter(talentPoolData) {
            const positionFilter = document.getElementById('positionFilter');
            const positions = Object.keys(talentPoolData);
            
            // 清空现有选项
            positionFilter.innerHTML = '<option value="">全部职位</option>';
            
            // 添加职位选项
            positions.forEach(position => {
                const option = document.createElement('option');
                option.value = position;
                option.textContent = position;
                positionFilter.appendChild(option);
            });
        }

        // 显示职位列表
        function displayPositions(talentPoolData) {
            const container = document.getElementById('talentPositionsContainer');
            container.innerHTML = '';

            Object.keys(talentPoolData).forEach(position => {
                const candidates = talentPoolData[position];
                const positionCard = createPositionCard(position, candidates);
                container.appendChild(positionCard);
            });
        }

        // 创建职位卡片
        function createPositionCard(position, candidates) {
            const positionCard = document.createElement('div');
            positionCard.className = 'position-card';

            const contactedCount = candidates.filter(c => c.contactStatus === 'contacted').length;
            const pendingCount = candidates.filter(c => c.contactStatus === 'pending').length;

            positionCard.innerHTML = `
                <div class="position-header">
                    <div class="position-title">
                        <h3>${position}</h3>
                        <div class="position-stats">
                            <span>👥 候选人: ${candidates.length}</span>
                            <span>✅ 已联系: ${contactedCount}</span>
                            <span>⏳ 待联系: ${pendingCount}</span>
                        </div>
                    </div>
                    <button class="btn-secondary" onclick="togglePositionCandidates(this, '${position}')" style="min-width: 100px;">
                        📋 展开
                    </button>
                </div>
                <div class="candidates-container" id="candidates_${position}" style="display: none;">
                    <div class="candidates-list">
                        ${candidates.map(candidate => createCandidateRow(candidate)).join('')}
                    </div>
                </div>
            `;

            return positionCard;
        }

        // 创建候选人行
        function createCandidateRow(candidate) {
            const contactStatus = candidate.contactStatus || 'pending';
            const statusText = contactStatus === 'contacted' ? '已联系' : '待联系';
            const statusClass = contactStatus === 'contacted' ? 'status-contacted' : 'status-pending';
            
            return `
                <div class="candidate-row">
                    <div class="candidate-info">
                        <div class="candidate-name">${candidate.name}</div>
                        <div class="candidate-details">
                            ${candidate.age} | ${candidate.experience} | ${candidate.education}
                        </div>
                    </div>
                    <div class="candidate-score">
                        <div class="candidate-score-number">${candidate.score}</div>
                        <div class="candidate-score-label">分数</div>
                    </div>
                    <div class="candidate-status">
                        <span class="status-badge ${statusClass}">${statusText}</span>
                    </div>
                    <div class="candidate-actions">
                        <button class="btn-secondary btn-sm" onclick="toggleContactStatus('${candidate.id}', '${candidate.name}')" style="margin-right: 10px;">
                            ${contactStatus === 'contacted' ? '取消联系' : '标记已联系'}
                        </button>
                        <button class="btn-secondary btn-sm" onclick="viewCandidateDetail('${candidate.id}')">
                            查看详情
                        </button>
                    </div>
                </div>
            `;
        }

        // 切换职位候选人显示
        function togglePositionCandidates(button, position) {
            const container = document.getElementById(`candidates_${position}`);
            const isVisible = container.style.display !== 'none';
            
            if (isVisible) {
                container.style.display = 'none';
                button.textContent = '📋 展开';
            } else {
                container.style.display = 'block';
                button.textContent = '📋 收起';
            }
        }

        // 切换联系状态
        function toggleContactStatus(candidateId, candidateName) {
            try {
                const talentPoolData = JSON.parse(localStorage.getItem('talentPool') || '{}');
                
                // 找到候选人并更新状态
                Object.keys(talentPoolData).forEach(position => {
                    const candidates = talentPoolData[position];
                    const candidate = candidates.find(c => c.id === candidateId);
                    if (candidate) {
                        candidate.contactStatus = candidate.contactStatus === 'contacted' ? 'pending' : 'contacted';
                        localStorage.setItem('talentPool', JSON.stringify(talentPoolData));
                        
                        // 刷新显示
                        loadTalentPoolData();
                        
                        showAlert(`候选人 ${candidateName} 状态已更新`, 'success');
                        return;
                    }
                });
            } catch (error) {
                console.error('更新联系状态失败:', error);
                showAlert('更新状态失败', 'error');
            }
        }

        // 查看候选人详情
        function viewCandidateDetail(candidateId) {
            // 这里可以实现查看候选人详细信息的逻辑
            showAlert('候选人详情功能开发中...', 'info');
        }

        // 筛选人才库
        function filterTalentPool() {
            const positionFilter = document.getElementById('positionFilter').value;
            const contactFilter = document.getElementById('contactFilter').value;
            const searchInput = document.getElementById('talentSearchInput').value.toLowerCase();
            
            try {
                const talentPoolData = JSON.parse(localStorage.getItem('talentPool') || '{}');
                let filteredData = {};

                Object.keys(talentPoolData).forEach(position => {
                    if (positionFilter && position !== positionFilter) {
                        return;
                    }

                    const candidates = talentPoolData[position].filter(candidate => {
                        // 联系状态筛选
                        if (contactFilter && candidate.contactStatus !== contactFilter) {
                            return false;
                        }
                        
                        // 姓名搜索
                        if (searchInput && !candidate.name.toLowerCase().includes(searchInput)) {
                            return false;
                        }
                        return true;
                    });

                    if (candidates.length > 0) {
                        filteredData[position] = candidates;
                    }
                });

                // 更新显示
                if (Object.keys(filteredData).length === 0) {
                    showNoQualifiedCandidates();
                } else {
                    updateTalentPoolStats(filteredData);
                    displayPositions(filteredData);
                }
            } catch (error) {
                console.error('筛选人才库失败:', error);
            }
        }

        // 显示空的人才库状态（保留用于兼容性）
        function showEmptyTalentPool() {
            showNoQualifiedCandidates();
        }

        // 导出人才库数据
        function exportTalentPool() {
            try {
                const talentPoolData = JSON.parse(localStorage.getItem('talentPool') || '{}');
                const dataStr = JSON.stringify(talentPoolData, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                
                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `人才库数据_${new Date().toISOString().split('T')[0]}.json`;
                link.click();
                
                showAlert('人才库数据导出成功', 'success');
            } catch (error) {
                console.error('导出人才库数据失败:', error);
                showAlert('导出失败', 'error');
            }
        }

        // 导入人才库数据
        function importTalentPool() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        try {
                            const data = JSON.parse(e.target.result);
                            localStorage.setItem('talentPool', JSON.stringify(data));
                            showAlert('人才库数据导入成功', 'success');
                            
                            // 如果当前在人才库页面，刷新显示
                            if (document.getElementById('talentSection').style.display !== 'none') {
                                loadTalentPoolData();
                            }
                        } catch (error) {
                            showAlert('导入失败：文件格式错误', 'error');
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }

        // 清空人才库数据
        function clearTalentPool() {
            if (confirm('确定要清空所有人才库数据吗？此操作不可恢复！')) {
                localStorage.removeItem('talentPool');
                showAlert('人才库数据已清空', 'success');
                
                // 如果当前在人才库页面，刷新显示
                if (document.getElementById('talentSection').style.display !== 'none') {
                    loadTalentPoolData();
                }
            }
        }

        // 添加候选人到人才库
        function addCandidateToTalentPool(candidate, position, score) {
            try {
                const talentPoolData = JSON.parse(localStorage.getItem('talentPool') || '{}');
                
                if (!talentPoolData[position]) {
                    talentPoolData[position] = [];
                }

                // 检查是否已存在
                const existingIndex = talentPoolData[position].findIndex(c => c.id === candidate.id);
                if (existingIndex !== -1) {
                    // 更新现有候选人信息
                    talentPoolData[position][existingIndex] = {
                        ...candidate,
                        score: score,
                        contactStatus: talentPoolData[position][existingIndex].contactStatus || 'pending'
                    };
                } else {
                    // 添加新候选人
                    talentPoolData[position].push({
                        ...candidate,
                        score: score,
                        contactStatus: 'pending'
                    });
                }

                localStorage.setItem('talentPool', JSON.stringify(talentPoolData));
                console.log(`候选人 ${candidate.name} 已添加到人才库职位 ${position}`);
            } catch (error) {
                console.error('添加候选人到人才库失败:', error);
            }
        }

        // 生成候选人唯一ID
        function generateCandidateId(name) {
            return `candidate_${name}_${Date.now()}`;
        }

        // 获取当前JD职位名称
        function getCurrentPositionName() {
            // 尝试从JD下拉框获取职位名称
            const jdDropdown = document.getElementById('jdDropdown');
            if (jdDropdown && jdDropdown.value) {
                return jdDropdown.value;
            }
            
            // 如果没有选择JD，尝试从页面标题获取
            const headerTitle = document.querySelector('.header h1');
            if (headerTitle) {
                return headerTitle.textContent.replace('智能简历筛选工具', '').trim() || '未知职位';
            }
            
            return '未知职位';
        }

        // 初始化人才库示例数据
        function initializeTalentPoolSampleData() {
            try {
                const existingData = localStorage.getItem('talentPool');
                if (!existingData || existingData === '{}') {
                    const sampleData = {
                        'AI产品经理': [
                            {
                                id: 'candidate_张三_001',
                                name: '张三',
                                age: '28',
                                experience: '5年',
                                education: '本科',
                                score: 88,
                                contactStatus: 'contacted'
                            },
                            {
                                id: 'candidate_李四_002',
                                name: '李四',
                                age: '30',
                                experience: '7年',
                                education: '硕士',
                                score: 92,
                                contactStatus: 'pending'
                            }
                        ],
                        '软件工程师': [
                            {
                                id: 'candidate_王五_003',
                                name: '王五',
                                age: '26',
                                experience: '3年',
                                education: '本科',
                                score: 85,
                                contactStatus: 'pending'
                            }
                        ]
                    };
                    
                    localStorage.setItem('talentPool', JSON.stringify(sampleData));
                    console.log('人才库示例数据初始化完成');
                }
            } catch (error) {
                console.error('初始化人才库示例数据失败:', error);
            }
        }

        // 页面加载时初始化（不再需要模拟数据）
        document.addEventListener('DOMContentLoaded', function() {
            // 人才库将在用户点击时从真实API加载数据
            console.log('人才库功能已初始化，等待用户访问');
        });
    </script>
</body>
</html> 