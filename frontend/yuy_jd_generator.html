<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title>宇曜科技 - 智能JD生成器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        :root {
            --primary-color: #d4af37;
            --secondary-color: #8b7355;
            --success-color: #1e5631; /* 修改为墨绿色 */
            --danger-color: #dc3545;
            --light-color: #f8f9fa;
            --dark-color: #1a1a1a;
            --border-color: #d4af37;
            --border-radius: 8px;
            --box-shadow: 0 4px 20px rgba(212, 175, 55, 0.2);
            --gold-gradient: linear-gradient(135deg, #d4af37, #ffd700, #b8860b);
            --dark-gold: #b8860b;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #000000 0%, #1a1a1a 25%, #2d2416 50%, #1a1a1a 75%, #000000 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* 统一选区与滚动条视觉风格 */
        ::selection {
            background: rgba(212, 175, 55, 0.25);
            color: #fff;
        }

        /* WebKit 滚动条样式 */
        *::-webkit-scrollbar {
            width: 10px;
            height: 10px;
        }

        *::-webkit-scrollbar-track {
            background: transparent;
        }

        *::-webkit-scrollbar-thumb {
            background: linear-gradient(180deg, rgba(212, 175, 55, 0.35), rgba(184, 134, 11, 0.35));
            border-radius: 8px;
            border: 2px solid rgba(0, 0, 0, 0.2);
        }

        *::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(180deg, rgba(212, 175, 55, 0.55), rgba(184, 134, 11, 0.55));
        }

        /* Firefox 滚动条颜色 */
        * {
            scrollbar-color: rgba(212, 175, 55, 0.35) transparent;
            scrollbar-width: thin;
        }

        .particles-canvas {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 0;
        }

        .company-title {
            position: relative;
            display: inline-block;
        }

        .company-title::before {
            content: '';
            position: absolute;
            top: 50%;
            left: -50px;
            right: -50px;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
            transform: translateY(-50%);
            z-index: 10;
        }

        .company-title::after {
            content: '';
            position: absolute;
            top: 50%;
            left: -30px;
            right: -30px;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transform: translateY(-50%);
            z-index: 10;
            filter: blur(1px);
        }

        .floating {
            animation: floating 6s ease-in-out infinite;
        }

        @keyframes floating {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        .glow {
            box-shadow: 0 0 30px rgba(74, 108, 247, 0.3);
        }

        .text-glow {
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
        }

        .gradient-text {
            background: var(--gold-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .glass-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: var(--border-radius);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
        }

        .glass-input {
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(5px);
        }

        .glass-input:focus {
            background: rgba(255, 255, 255, 0.12);
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.3);
        }

        .ai-btn {
            background: var(--gold-gradient);
            border: none;
            color: #000;
            padding: 12px 24px;
            border-radius: var(--border-radius);
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .ai-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .ai-btn:hover::before {
            left: 100%;
        }

        .ai-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(212, 175, 55, 0.6);
        }

        .ai-btn:focus-visible,
        .skip-btn:focus-visible,
        .edit-btn:focus-visible {
            outline: none;
            box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.35);
        }

        /* 评估简历按钮特殊样式 */
        .ai-btn.bg-purple-600 {
            background: linear-gradient(135deg, #9333ea, #7c3aed);
            border: 1px solid rgba(147, 51, 234, 0.3);
        }

        .ai-btn.bg-purple-600:hover {
            background: linear-gradient(135deg, #a855f7, #9333ea);
            box-shadow: 0 8px 25px rgba(147, 51, 234, 0.6);
        }

        /* 统一“保存/复制”等操作按钮风格（与主按钮保持一致的高端质感） */
        .ai-btn.bg-green-600,
        .ai-btn.bg-blue-600 {
            background: var(--gold-gradient);
            border: 1px solid rgba(212, 175, 55, 0.35);
            color: #000;
        }

        .ai-btn.bg-green-600:hover,
        .ai-btn.bg-blue-600:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(212, 175, 55, 0.6);
        }

        .ai-btn:disabled {
            background: rgba(108, 117, 125, 0.5);
            cursor: not-allowed;
            transform: none;
        }

        .progress-ring {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 14px;
        }

        .progress-ring.active {
            background: conic-gradient(var(--primary-color) 0deg, var(--primary-color) 45deg, rgba(212, 175, 55, 0.1) 45deg);
            animation: pulse 2s ease-in-out infinite;
        }

        .progress-ring.completed {
            background: conic-gradient(var(--success-color) 0deg, var(--success-color) 360deg);
        }

        @keyframes pulse {
            0%, 100% {
                box-shadow: 0 0 0 0 rgba(212, 175, 55, 0.7);
            }
            50% {
                box-shadow: 0 0 0 8px rgba(212, 175, 55, 0);
            }
        }

        .message {
            margin-bottom: 16px;
            padding: 16px 20px;
            border-radius: 12px;
            max-width: 85%;
            word-wrap: break-word;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.system {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            align-self: flex-start;
            border-left: 4px solid var(--primary-color);
        }

        .message.user {
            background: var(--gold-gradient);
            color: #000;
            align-self: flex-end;
            margin-left: auto;
        }

        .loading-spinner {
            border: 3px solid rgba(255, 255, 255, 0.1);
            border-left-color: var(--primary-color);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        /* 统一选项卡风格（见下方“选项区域样式”块） */

        .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }

        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }

        .jd-content {
            background: rgba(255, 255, 255, 0.05);
            border: 2px solid rgba(255, 255, 255, 0.1);
            color: white;
            padding: 20px;
            border-radius: var(--border-radius);
            min-height: 400px;
            resize: vertical;
            font-family: 'JetBrains Mono', monospace;
            line-height: 1.6;
            overflow-y: auto;
        }

        .jd-content:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.3);
            outline: none;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-badge.active {
            background: rgba(74, 108, 247, 0.2);
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
        }

        .status-badge.completed {
            background: rgba(30, 86, 49, 0.2); /* 墨绿色背景 */
            color: var(--success-color);
            border: 1px solid var(--success-color);
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            padding: 16px 24px;
            border-radius: var(--border-radius);
            color: white;
            font-weight: 500;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: linear-gradient(135deg, var(--success-color), #2a7d4f);
        }

        .notification.error {
            background: linear-gradient(135deg, var(--danger-color), #e74c3c);
        }

        .notification.info {
            background: linear-gradient(135deg, #3a3a3a, #1f1f1f);
            border: 1px solid rgba(212, 175, 55, 0.25);
        }

        /* 公司信息卡片样式 */
        .company-info-card {
            background: rgba(255, 255, 255, 0.05);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .company-info-card:hover {
            border-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .company-info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .company-info-item:last-child {
            border-bottom: none;
        }

        .company-info-label {
            color: rgba(255, 255, 255, 0.7);
            font-weight: 600;
            font-size: 14px;
        }

        .company-info-value {
            color: #ffffff;
            font-size: 14px;
            max-width: 200px;
            text-align: right;
            word-wrap: break-word;
        }

        .edit-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: rgba(255, 255, 255, 0.8);
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .edit-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            color: #fff;
            border-color: rgba(255, 255, 255, 0.5);
        }

        /* 统一编辑切换按钮激活态（用于 Markdown/原始切换） */
        .edit-btn.active {
            background: rgba(212, 175, 55, 0.15);
            color: var(--primary-color);
            border-color: rgba(212, 175, 55, 0.5);
        }

        /* 公司信息配置界面 */
        .company-config-screen {
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(20px);
        }

        .config-form-group {
            margin-bottom: 20px;
        }

        .config-label {
            display: block;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .config-input {
            width: 100%;
            background: rgba(255, 255, 255, 0.08);
            border: 2px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 12px;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .config-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
            outline: none;
        }

        /* 下拉菜单样式 */
        .config-input option {
            background: #1a1a1a;
            color: white;
            padding: 8px;
        }

        .config-input option:hover {
            background: #2a2a2a;
        }

        .config-input option:checked {
            background: var(--primary-color);
            color: #000;
        }

        .config-textarea {
            min-height: 80px;
            resize: vertical;
        }

        .skip-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 24px;
            border-radius: var(--border-radius);
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .skip-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            color: #fff;
            border-color: rgba(255, 255, 255, 0.5);
        }

        /* AI建议内容样式 */
        .advice-content h3 {
            color: var(--primary-color);
            font-size: 18px;
            font-weight: 600;
            margin: 20px 0 10px 0;
            border-bottom: 1px solid rgba(212, 175, 55, 0.3);
            padding-bottom: 5px;
        }

        .advice-content h4 {
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            font-weight: 600;
            margin: 15px 0 8px 0;
        }

        .advice-content p {
            margin: 10px 0;
            line-height: 1.6;
        }

        .advice-content ul {
            margin: 10px 0;
            padding-left: 20px;
        }

        .advice-content li {
            margin: 5px 0;
            list-style-type: disc;
        }

        .advice-content strong {
            color: var(--primary-color);
            font-weight: 600;
        }

        .advice-content .highlight {
            background: rgba(212, 175, 55, 0.2);
            padding: 2px 6px;
            border-radius: 4px;
        }

        .advice-section {
            background: rgba(255, 255, 255, 0.05);
            border-left: 4px solid var(--primary-color);
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 8px 8px 0;
        }

        .advice-section h4 {
            margin-top: 0;
            color: var(--primary-color);
        }

        /* 选项区域样式 */
        .option-item {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 12px 16px;
            color: rgba(255, 255, 255, 0.9);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            line-height: 1.4;
        }

        .option-item:hover {
            background: rgba(212, 175, 55, 0.12);
            border-color: rgba(212, 175, 55, 0.35);
            color: var(--primary-color);
            transform: translateY(-1px);
        }

        .option-item:active {
            transform: translateY(0);
        }

        /* Markdown预览样式 */
        .markdown-preview {
            color: #f8f9fa;
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            overflow-y: auto;
        }

        .markdown-preview h1 {
            font-size: 1.8rem;
            font-weight: 700;
            margin: 1.5rem 0 1rem;
            padding-bottom: 0.3rem;
            border-bottom: 1px solid rgba(212, 175, 55, 0.3);
            color: var(--primary-color);
        }

        .markdown-preview h2 {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 1.4rem 0 0.8rem;
            color: var(--primary-color);
        }

        .markdown-preview h3 {
            font-size: 1.3rem;
            font-weight: 600;
            margin: 1.2rem 0 0.7rem;
            color: var(--primary-color);
        }

        .markdown-preview h4 {
            font-size: 1.1rem;
            font-weight: 600;
            margin: 1rem 0 0.6rem;
            color: var(--primary-color);
        }

        .markdown-preview p {
            margin: 0.8rem 0;
        }

        .markdown-preview ul, .markdown-preview ol {
            padding-left: 1.5rem;
            margin: 0.8rem 0;
        }

        .markdown-preview li {
            margin: 0.3rem 0;
        }

        .markdown-preview ul li {
            list-style-type: disc;
        }

        .markdown-preview ol li {
            list-style-type: decimal;
        }

        .markdown-preview code {
            background: rgba(255, 255, 255, 0.1);
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
            font-family: 'JetBrains Mono', monospace;
            font-size: 0.9rem;
        }

        .markdown-preview pre {
            background: rgba(255, 255, 255, 0.05);
            padding: 1rem;
            border-radius: 5px;
            overflow-x: auto;
            margin: 1rem 0;
        }

        .markdown-preview pre code {
            background: transparent;
            padding: 0;
        }

        .markdown-preview blockquote {
            border-left: 3px solid var(--primary-color);
            padding-left: 1rem;
            margin: 1rem 0;
            color: rgba(255, 255, 255, 0.8);
        }

        .markdown-preview table {
            border-collapse: collapse;
            width: 100%;
            margin: 1rem 0;
        }

        .markdown-preview th, .markdown-preview td {
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 0.5rem;
            text-align: left;
        }

        .markdown-preview th {
            background: rgba(255, 255, 255, 0.1);
            font-weight: 600;
        }

        /* 卡片悬浮细化，统一风格 */
        .glass-card:hover {
            border-color: rgba(255, 255, 255, 0.2);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.45);
        }

        /* 拖拽项样式 */
        .draggable-item {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: var(--border-radius);
            padding: 12px 16px;
            margin-bottom: 8px;
            cursor: grab;
            transition: all 0.2s ease;
            position: relative;
        }

        .draggable-item:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        .draggable-item.dragging {
            opacity: 0.5;
            cursor: grabbing;
        }

        .draggable-item .drag-handle {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--border-radius) 0 0 var(--border-radius);
            cursor: grab;
        }

        .draggable-item .drag-handle:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .draggable-item .item-content {
            margin-left: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .draggable-item .item-number {
            display: inline-block;
            width: 24px;
            height: 24px;
            line-height: 24px;
            text-align: center;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 50%;
            margin-right: 8px;
            font-size: 12px;
            font-weight: 600;
            flex-shrink: 0;
        }

        .draggable-item .item-text {
            flex: 1;
            word-break: break-word;
        }

        .draggable-item .edit-item-btn,
        .draggable-item .delete-item-btn {
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
            font-size: 14px;
            flex-shrink: 0;
        }

        .draggable-item .edit-item-btn:hover {
            background: rgba(212, 175, 55, 0.2);
            color: var(--primary-color);
        }

        .draggable-item .delete-item-btn:hover {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
        }

        .draggable-item .edit-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 14px;
            width: 100%;
            outline: none;
        }

        .draggable-item .edit-input:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.3);
        }

        /* 用户消息分点显示样式 */
        .user-point {
            margin: 5px 0;
            position: relative;
            padding-left: 5px;
        }

        .user-point:not(:last-child) {
            margin-bottom: 8px;
        }

        /* 系统消息总结样式 */
        .summary-message {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 12px;
            margin-top: 10px;
        }

        .summary-item {
            margin: 10px 0;
        }

        .summary-item strong {
            color: var(--primary-color);
            font-weight: 600;
        }

        .summary-message ul {
            margin: 8px 0 12px 0;
            padding-left: 20px;
        }

        .summary-message li {
            margin: 5px 0;
            line-height: 1.4;
        }

        /* JD修改页面特殊样式 */
        #jdModificationScreen .grid {
            min-height: 550px;
        }

        #jdModificationScreen #modificationConversationArea,
        #jdModificationScreen #jdPreviewWrapper {
            height: 550px;
        }

        /* 在大屏幕上进一步优化JD修改界面 */
        @media (min-width: 1280px) {
            #jdModificationScreen .grid {
                min-height: 650px;
            }

            #jdModificationScreen #modificationConversationArea,
            #jdModificationScreen #jdPreviewWrapper {
                height: 650px;
            }
        }

        /* 在超大屏幕上进一步优化JD修改界面 */
        @media (min-width: 1536px) {
            #jdModificationScreen .grid {
                min-height: 750px;
            }

            #jdModificationScreen #modificationConversationArea,
            #jdModificationScreen #jdPreviewWrapper {
                height: 750px;
            }
        }

        /* 公司配置页面特殊样式 */
        #companyConfigScreen {
            max-width: 900px;
            margin: 0 auto;
        }


        @media (min-height: 800px) {
            #companyConfigScreen {
                margin-top: 2vh;
            }
        }
    </style>
</head>
<body class="text-white min-h-screen">
<canvas class="particles-canvas" id="particlesCanvas"></canvas>

<!-- 通知组件 -->
<div class="notification" id="notification"></div>

<div class="relative z-10 min-h-screen">
    <!-- 顶部标题区域 -->
    <div class="relative pt-12 pb-8">
        <div class="text-center">
            <h1 class="company-title text-4xl md:text-6xl font-bold gradient-text floating text-glow mb-4">
                宇曜科技
            </h1>
            <p class="text-white/70 text-lg font-light tracking-wider mb-2">
                AI驱动未来 · 智慧点亮世界
            </p>
            <h2 class="text-2xl md:text-3xl font-semibold text-white mb-8">
                智能JD生成器
            </h2>
        </div>
    </div>

    <!-- 主容器 -->
    <div class="max-w-7xl mx-auto px-4 pb-12">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">

            <!-- 左侧信息面板 -->
            <div class="lg:col-span-1" id="leftSidebar">
                <!-- 公司信息卡片 -->
                <div class="company-info-card" id="companyInfoCard" style="display: none;">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-white flex items-center">
                            <span class="w-2 h-2 bg-white/30 rounded-full mr-3"></span>
                            公司信息
                        </h3>
                        <button class="edit-btn" id="editCompanyBtn">修改</button>
                    </div>
                    <div id="companyInfoContent">
                        <!-- 公司信息将动态显示 -->
                    </div>
                </div>

                <!-- 进度面板 -->
                <div class="glass-card p-6 sticky top-6" id="progressPanel">
                    <h3 class="text-xl font-semibold text-white mb-6 flex items-center">
                        <span class="w-2 h-2 bg-white/30 rounded-full mr-3"></span>
                        对话进度
                    </h3>
                    <div class="space-y-4" id="progressSteps">
                        <!-- 进度步骤将动态生成 -->
                    </div>
                </div>
            </div>

            <!-- 中间主要内容区域 -->
            <div class="lg:col-span-3" id="mainContent">

                <!-- 公司信息配置界面 -->
                <div class="glass-card p-8" id="companyConfigScreen">
                    <div class="text-center mb-8">
                        <div class="w-16 h-16 bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl">🏢</span>
                        </div>
                        <h3 class="text-2xl font-semibold text-white mb-3">
                            配置公司信息
                        </h3>
                        <p class="text-white/70 text-lg">
                            首次使用请先配置公司基本信息，以便更精准地生成JD
                        </p>
                    </div>

                    <div class="max-w-3xl mx-auto">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="config-form-group">
                                <label class="config-label">公司名称 *</label>
                                <input class="config-input" id="companyName" placeholder="例如：宇曜科技有限公司" type="text">
                            </div>
                            <div class="config-form-group">
                                <label class="config-label">所属行业 *</label>
                                <input class="config-input" id="companyIndustry" placeholder="例如：互联网科技" type="text">
                            </div>
                            <div class="config-form-group">
                                <label class="config-label">公司规模 *</label>
                                <select class="config-input" id="companySize">
                                    <option value="">请选择公司规模</option>
                                    <option value="20人以下">20人以下</option>
                                    <option value="20-99人">20-99人</option>
                                    <option value="100-499人">100-499人</option>
                                    <option value="500-999人">500-999人</option>
                                    <option value="1000人以上">1000人以上</option>
                                </select>
                            </div>
                            <div class="config-form-group">
                                <label class="config-label">办公地点 *</label>
                                <input class="config-input" id="companyLocation" placeholder="例如：北京市朝阳区" type="text">
                            </div>
                            <div class="config-form-group">
                                <label class="config-label">发展阶段</label>
                                <select class="config-input" id="companyStage">
                                    <option value="">请选择发展阶段</option>
                                    <option value="初创期">初创期</option>
                                    <option value="成长期">成长期</option>
                                    <option value="成熟期">成熟期</option>
                                    <option value="扩张期">扩张期</option>
                                </select>
                            </div>
                            <div class="config-form-group">
                                <label class="config-label">融资情况</label>
                                <select class="config-input" id="companyFunding">
                                    <option value="">请选择融资情况</option>
                                    <option value="未融资">未融资</option>
                                    <option value="天使轮">天使轮</option>
                                    <option value="A轮">A轮</option>
                                    <option value="B轮">B轮</option>
                                    <option value="C轮及以后">C轮及以后</option>
                                    <option value="已上市">已上市</option>
                                </select>
                            </div>
                        </div>
                        <div class="config-form-group">
                            <label class="config-label">业务描述 *</label>
                            <textarea class="config-input config-textarea" id="companyBusiness"
                                      placeholder="请详细描述公司的主要业务和服务，例如：专注于企业级SaaS产品开发，为中小企业提供数字化转型解决方案..."></textarea>
                        </div>
                        <div class="config-form-group">
                            <label class="config-label">公司文化/优势</label>
                            <textarea class="config-input config-textarea" id="companyCulture"
                                      placeholder="例如：技术驱动、扁平化管理、弹性工作制、完善的培训体系..."></textarea>
                        </div>
                        <div class="text-center mt-8">
                            <button class="ai-btn mr-4" id="saveCompanyInfoBtn">
                                <span class="relative z-10">保存并继续</span>
                            </button>
                            <button class="skip-btn" id="skipCompanyConfigBtn">
                                <span class="relative z-10">跳过配置</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 岗位咨询界面 -->
                <div class="glass-card p-8" id="jobConsultScreen" style="display: none;">
                    <div class="text-center mb-8">
                        <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl">🤔</span>
                        </div>
                        <h3 class="text-2xl font-semibold text-white mb-3">
                            岗位分析与建议
                        </h3>
                        <p class="text-white/70 text-lg">
                            告诉我您想招聘什么岗位，我来为您提供专业建议
                        </p>
                    </div>

                    <div class="max-w-2xl mx-auto">
                        <label class="block text-white font-medium mb-3">
                            您想招聘什么岗位？
                        </label>
                        <input
                                class="w-full glass-input text-white placeholder-white/50 rounded-lg p-4 outline-none transition-all duration-300"
                                id="positionInput"
                                placeholder="例如：前端开发工程师、产品经理、UI设计师..."
                                type="text"
                        />
                        <div class="text-center mt-6">
                            <button class="ai-btn" id="getPositionAdviceBtn">
                                <span class="relative z-10">获取岗位建议</span>
                            </button>
                        </div>
                    </div>

                    <!-- AI建议区域 -->
                    <div class="max-w-4xl mx-auto mt-8" id="positionAdviceArea" style="display: none;">
                        <div class="glass-input p-6 rounded-lg">
                            <h4 class="text-lg font-semibold text-white mb-4 flex items-center">
                                <span class="text-2xl mr-3">🎯</span>
                                AI岗位分析建议
                            </h4>
                            <div class="text-white/90" id="positionAdviceContent">
                                <!-- AI建议内容将在这里显示 -->
                            </div>
                        </div>
                        <div class="text-center mt-6">
                            <button class="ai-btn" id="proceedToJDCreationBtn">
                                <span class="relative z-10">明白了，开始创建JD</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 基本信息配置界面 -->
                <div class="glass-card p-8" id="basicInfoConfigScreen" style="display: none;">
                    <div class="text-center mb-8">
                        <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl">📋</span>
                        </div>
                        <h3 class="text-2xl font-semibold text-white mb-3">
                            配置岗位基本信息
                        </h3>
                        <p class="text-white/70 text-lg">
                            请填写岗位的基本信息，这些信息将用于生成JD
                        </p>
                    </div>

                    <div class="max-w-3xl mx-auto">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="config-form-group">
                                <label class="config-label">薪资范围 *</label>
                                <input class="config-input" id="salaryRange" placeholder="例如：15-25K" type="text">
                            </div>
                            <div class="config-form-group">
                                <label class="config-label">岗位性质 *</label>
                                <select class="config-input" id="jobType">
                                    <option value="">请选择岗位性质</option>
                                    <option value="全职">全职</option>
                                    <option value="兼职">兼职</option>
                                    <option value="实习">实习</option>
                                    <option value="外包">外包</option>
                                </select>
                            </div>
                            <div class="config-form-group">
                                <label class="config-label">学历要求 *</label>
                                <select class="config-input" id="educationLevel">
                                    <option value="">请选择学历要求</option>
                                    <option value="不限">不限</option>
                                    <option value="专科">专科</option>
                                    <option value="本科">本科</option>
                                    <option value="硕士">硕士</option>
                                    <option value="博士">博士</option>
                                </select>
                            </div>
                            <div class="config-form-group">
                                <label class="config-label">年龄要求</label>
                                <input class="config-input" id="ageRange" placeholder="例如：25-35岁" type="text">
                            </div>
                            <div class="config-form-group">
                                <label class="config-label">工作经验 *</label>
                                <select class="config-input" id="workExperience">
                                    <option value="">请选择工作经验</option>
                                    <option value="应届毕业生">应届毕业生</option>
                                    <option value="1年以下">1年以下</option>
                                    <option value="1-3年">1-3年</option>
                                    <option value="3-5年">3-5年</option>
                                    <option value="5-8年">5-8年</option>
                                    <option value="8年以上">8年以上</option>
                                </select>
                            </div>
                            <div class="config-form-group">
                                <label class="config-label">工作地点 *</label>
                                <input class="config-input" id="workLocation" placeholder="例如：北京市朝阳区" type="text">
                            </div>
                            <div class="config-form-group">
                                <label class="config-label">工作方式 *</label>
                                <select class="config-input" id="workMode">
                                    <option value="">请选择工作方式</option>
                                    <option value="现场办公">现场办公</option>
                                    <option value="远程办公">远程办公</option>
                                    <option value="混合办公">混合办公</option>
                                </select>
                            </div>
                            <div class="config-form-group">
                                <label class="config-label">专业要求</label>
                                <input class="config-input" id="majorRequirement" placeholder="例如：计算机相关专业优先"
                                       type="text">
                            </div>
                        </div>
                        <div class="text-center mt-8">
                            <button class="ai-btn mr-4" id="saveBasicInfoBtn">
                                <span class="relative z-10">保存并开始对话</span>
                            </button>
                            <button class="skip-btn" id="skipBasicInfoBtn">
                                <span class="relative z-10">跳过配置</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 欢迎界面 -->
                <div class="glass-card p-8" id="welcomeScreen" style="display: none;">
                    <div class="text-center mb-8">
                        <div class="w-16 h-16 bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl">🎯</span>
                        </div>
                        <h3 class="text-2xl font-semibold text-white mb-3">
                            开始创建专业JD
                        </h3>
                        <p class="text-white/70 text-lg">
                            通过4轮核心对话，生成精准的职位描述
                        </p>
                    </div>

                    <div class="max-w-2xl mx-auto">
                        <label class="block text-white font-medium mb-3">
                            请描述要招聘的岗位基本情况：
                        </label>
                        <textarea
                                class="w-full h-32 glass-input text-white placeholder-white/50 rounded-lg p-4 resize-none outline-none transition-all duration-300"
                                id="jobDescription"
                                placeholder="例如：需要招聘一名前端开发工程师，负责用户界面开发和用户体验优化，要求熟悉React/Vue框架..."
                        ></textarea>
                        <div class="text-right mt-4">
                            <button class="ai-btn" id="startConversationBtn">
                                <span class="relative z-10">开始对话</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 对话界面 -->
                <div class="glass-card hidden" id="conversationScreen">
                    <div class="p-6 border-b border-white/10">
                        <h3 class="text-xl font-semibold text-white flex items-center">
                            <span class="w-2 h-2 bg-white/50 rounded-full mr-3 animate-pulse"></span>
                            AI助手对话中
                        </h3>
                    </div>

                    <div class="p-6">
                        <!-- 对话区域 -->
                        <div class="h-96 overflow-y-auto scrollbar-hide mb-6 space-y-4" id="conversationArea">
                            <!-- 对话消息将动态添加 -->
                        </div>

                        <!-- 选项区域 -->
                        <div class="mb-6" id="optionsArea">
                            <!-- 选项将动态生成 -->
                        </div>

                        <!-- 输入区域 -->
                        <div class="space-y-3">
                            <div class="flex gap-3">
                                    <textarea
                                            class="flex-1 glass-input text-white placeholder-white/50 rounded-lg p-4 resize-none outline-none min-h-[80px] max-h-[200px]"
                                            id="userInput"
                                            placeholder="请输入您的回答...（输入'生成JD'或'结束'可完成对话）"
                                            rows="3"
                                    ></textarea>
                                <div class="flex flex-col gap-2">
                                    <button class="ai-btn px-6" id="sendBtn">
                                        <span class="relative z-10">发送</span>
                                    </button>
                                    <button class="skip-btn px-4 py-2 text-sm" id="generateJDBtn">
                                        <span class="relative z-10">生成JD</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 总结界面 -->
                <div class="glass-card p-8 hidden" id="summaryScreen">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl">📋</span>
                        </div>
                        <h3 class="text-2xl font-semibold text-white mb-3">
                            信息总结与确认
                        </h3>
                        <p class="text-white/70">
                            请核对以下信息，确认无误后生成JD
                        </p>
                    </div>

                    <div class="mb-8" id="summaryContent">
                        <!-- 总结内容将动态生成 -->
                    </div>

                    <!-- 核心职责排序区域 -->
                    <div class="mb-8 glass-input p-6 rounded-lg" id="coreResponsibilitiesSection">
                        <h4 class="text-lg font-semibold text-white mb-4">核心职责（按重要性排序，可拖拽调整顺序）</h4>
                        <div class="space-y-2" id="responsibilitiesList">
                            <!-- 职责项将动态生成 -->
                        </div>
                    </div>

                    <!-- 必备技能排序区域 -->
                    <div class="mb-8 glass-input p-6 rounded-lg" id="coreSkillsSection">
                        <h4 class="text-lg font-semibold text-white mb-4">核心技能（按重要性排序，可拖拽调整顺序）</h4>
                        <div class="space-y-2" id="skillsList">
                            <!-- 技能项将动态生成 -->
                        </div>
                    </div>

                    <!-- 优先技能排序区域 -->
                    <div class="mb-8 glass-input p-6 rounded-lg" id="prioritySkillsSection">
                        <h4 class="text-lg font-semibold text-white mb-4">拓展技能（按重要性排序，可拖拽调整顺序）</h4>
                        <div class="space-y-2" id="prioritySkillsList">
                            <!-- 优先技能项将动态生成 -->
                        </div>
                    </div>

                    <!-- 加分项排序区域 -->
                    <div class="mb-8 glass-input p-6 rounded-lg" id="bonusSkillsSection">
                        <h4 class="text-lg font-semibold text-white mb-4">加分项（按重要性排序，可拖拽调整顺序）</h4>
                        <div class="space-y-2" id="bonusSkillsList">
                            <!-- 加分项将动态生成 -->
                        </div>
                    </div>

                    <div class="flex justify-center gap-4">
                        <button class="skip-btn" id="backBtn">
                            <span class="relative z-10">返回修改</span>
                        </button>
                        <button class="ai-btn" id="confirmBtn">
                            <span class="relative z-10">确认生成JD</span>
                        </button>
                    </div>
                </div>

                <!-- JD修改界面 -->
                <div class="glass-card p-8 hidden" id="jdModificationScreen">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl">✨</span>
                        </div>
                        <h3 class="text-2xl font-semibold text-white mb-3">
                            JD预览与编辑
                        </h3>
                        <p class="text-white/70">
                            您可以直接编辑JD内容或通过AI助手进行修改
                        </p>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 max-w-full mx-auto">
                        <!-- 对话历史 -->
                        <div>
                            <div class="flex justify-between items-center mb-4">
                                <h4 class="text-lg font-semibold text-white">对话历史</h4>
                            </div>
                            <div class="h-[500px] overflow-y-auto scrollbar-hide glass-input p-4 rounded-lg mb-4"
                                 id="modificationConversationArea">
                                <!-- 对话历史将动态显示 -->
                            </div>
                            <div class="flex gap-3">
                                    <textarea
                                            class="flex-1 glass-input text-white placeholder-white/50 rounded-lg p-3 resize-none outline-none"
                                            id="modificationInput"
                                            placeholder="请输入修改要求，例如：把技能要求中的React改为Vue..."
                                            rows="2"
                                    ></textarea>
                                <button class="ai-btn px-4" id="modifyBtn">
                                    <span class="relative z-10">修改</span>
                                </button>
                            </div>
                        </div>

                        <!-- JD内容 -->
                        <div>
                            <div class="flex justify-between items-center mb-4">
                                <h4 class="text-lg font-semibold text-white">JD内容</h4>
                                <div class="flex gap-2">
                                    <button class="edit-btn active" id="viewMarkdownBtn">Markdown预览</button>
                                    <button class="edit-btn" id="viewRawBtn">原始编辑</button>
                                </div>
                            </div>
                            <div class="h-[500px]" id="jdPreviewWrapper">
                                    <textarea
                                            class="jd-content w-full outline-none hidden h-full"
                                            id="jdPreviewContent"
                                            placeholder="JD内容将在此显示..."
                                    ></textarea>
                                <div class="jd-content w-full markdown-preview h-full overflow-y-auto"
                                     id="markdownPreview"></div>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-center gap-4 mt-8">
                        <button class="ai-btn bg-green-600 hover:bg-green-500" id="saveJDBtn">
                            <span class="relative z-10">保存JD</span>
                        </button>
                        <button class="ai-btn bg-blue-600 hover:bg-blue-500" id="finalCopyBtn">
                            <span class="relative z-10">复制JD</span>
                        </button>
                        <button class="ai-btn bg-purple-600 hover:bg-purple-500" id="startResumeEvaluationBtn">
                            <span class="relative z-10">开始评估简历</span>
                        </button>
                        <button class="skip-btn" id="finalRestartBtn">
                            <span class="relative z-10">重新生成</span>
                        </button>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<!-- 加载动画 -->
<div class="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 hidden" id="loadingScreen">
    <div class="flex items-center justify-center h-full">
        <div class="glass-card p-8 text-center">
            <div class="loading-spinner mx-auto mb-6"></div>
            <p class="text-white text-lg font-medium" id="loadingText">
                正在生成JD，请稍候...
            </p>
        </div>
    </div>
</div>

<script>
    // 粒子背景效果
    function initParticles() {
        const canvas = document.getElementById('particlesCanvas');
        const ctx = canvas.getContext('2d');

        function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        }

        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        const particles = [];

        class Particle {
            constructor() {
                this.x = Math.random() * canvas.width;
                this.y = Math.random() * canvas.height;
                this.size = Math.random() * 2 + 0.5;
                this.speedX = (Math.random() - 0.5) * 0.3;
                this.speedY = (Math.random() - 0.5) * 0.3;
                this.opacity = Math.random() * 0.8 + 0.2;
                this.life = Math.random() * 200 + 100;
                this.age = 0;
            }

            update() {
                this.x += this.speedX;
                this.y += this.speedY;
                this.age++;

                if (this.age < this.life * 0.3) {
                    this.opacity = (this.age / (this.life * 0.3)) * 0.8;
                } else if (this.age > this.life * 0.7) {
                    this.opacity = (1 - (this.age - this.life * 0.7) / (this.life * 0.3)) * 0.8;
                }

                if (this.x < 0 || this.x > canvas.width ||
                    this.y < 0 || this.y > canvas.height ||
                    this.age >= this.life) {
                    this.reset();
                }
            }

            reset() {
                this.x = Math.random() * canvas.width;
                this.y = Math.random() * canvas.height;
                this.age = 0;
                this.life = Math.random() * 200 + 100;
            }

            draw() {
                ctx.save();
                ctx.globalAlpha = this.opacity;
                ctx.fillStyle = '#ffffff';
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        }

        const particleCount = Math.floor((canvas.width * canvas.height) / 3000);
        for (let i = 0; i < particleCount; i++) {
            particles.push(new Particle());
        }

        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            particles.forEach(particle => {
                particle.update();
                particle.draw();
            });

            requestAnimationFrame(animate);
        }

        animate();
    }

    // 全局状态管理
    const state = {
        currentRound: 0,
        selectedOptions: {},
        customInputs: {},
        companyInfo: null,
        positionName: '',
        positionAdvice: '',
        jobDescription: '',
        basicInfo: null, // 基本信息
        conversationHistory: [],
        isSupplementQuestion: false,  // 是否为补充询问
        responsibilities: [], // 核心职责列表
        skills: [], // 必备技能列表
        prioritySkills: [], // 优先技能列表
        bonusSkills: [] // 加分项列表
    };

    // DOM元素
    const elements = {
        companyConfigScreen: document.getElementById('companyConfigScreen'),
        jobConsultScreen: document.getElementById('jobConsultScreen'),
        welcomeScreen: document.getElementById('welcomeScreen'),
        conversationScreen: document.getElementById('conversationScreen'),
        summaryScreen: document.getElementById('summaryScreen'),
        jdModificationScreen: document.getElementById('jdModificationScreen'),
        loadingScreen: document.getElementById('loadingScreen'),
        companyInfoCard: document.getElementById('companyInfoCard'),
        companyInfoContent: document.getElementById('companyInfoContent'),
        editCompanyBtn: document.getElementById('editCompanyBtn'),
        // 公司信息表单
        companyName: document.getElementById('companyName'),
        companyIndustry: document.getElementById('companyIndustry'),
        companySize: document.getElementById('companySize'),
        companyLocation: document.getElementById('companyLocation'),
        companyStage: document.getElementById('companyStage'),
        companyFunding: document.getElementById('companyFunding'),
        companyBusiness: document.getElementById('companyBusiness'),
        companyCulture: document.getElementById('companyCulture'),
        saveCompanyInfoBtn: document.getElementById('saveCompanyInfoBtn'),
        skipCompanyConfigBtn: document.getElementById('skipCompanyConfigBtn'),
        // 基本信息配置界面
        basicInfoConfigScreen: document.getElementById('basicInfoConfigScreen'),
        salaryRange: document.getElementById('salaryRange'),
        jobType: document.getElementById('jobType'),
        educationLevel: document.getElementById('educationLevel'),
        ageRange: document.getElementById('ageRange'),
        workExperience: document.getElementById('workExperience'),
        workLocation: document.getElementById('workLocation'),
        workMode: document.getElementById('workMode'),
        majorRequirement: document.getElementById('majorRequirement'),
        saveBasicInfoBtn: document.getElementById('saveBasicInfoBtn'),
        skipBasicInfoBtn: document.getElementById('skipBasicInfoBtn'),
        // 岗位咨询
        positionInput: document.getElementById('positionInput'),
        getPositionAdviceBtn: document.getElementById('getPositionAdviceBtn'),
        positionAdviceArea: document.getElementById('positionAdviceArea'),
        positionAdviceContent: document.getElementById('positionAdviceContent'),
        proceedToJDCreationBtn: document.getElementById('proceedToJDCreationBtn'),
        jobDescription: document.getElementById('jobDescription'),
        startConversationBtn: document.getElementById('startConversationBtn'),
        conversationArea: document.getElementById('conversationArea'),
        optionsArea: document.getElementById('optionsArea'),
        userInput: document.getElementById('userInput'),
        sendBtn: document.getElementById('sendBtn'),
        generateJDBtn: document.getElementById('generateJDBtn'),
        progressSteps: document.getElementById('progressSteps'),
        summaryContent: document.getElementById('summaryContent'),
        confirmBtn: document.getElementById('confirmBtn'),
        backBtn: document.getElementById('backBtn'),
        modificationConversationArea: document.getElementById('modificationConversationArea'),
        modificationInput: document.getElementById('modificationInput'),
        modifyBtn: document.getElementById('modifyBtn'),
        jdPreviewContent: document.getElementById('jdPreviewContent'),
        finalCopyBtn: document.getElementById('finalCopyBtn'),
        finalRestartBtn: document.getElementById('finalRestartBtn'),
        saveJDBtn: document.getElementById('saveJDBtn'),
        startResumeEvaluationBtn: document.getElementById('startResumeEvaluationBtn'),
        loadingText: document.getElementById('loadingText'),
        notification: document.getElementById('notification'),
        viewMarkdownBtn: document.getElementById('viewMarkdownBtn'),
        viewRawBtn: document.getElementById('viewRawBtn'),
        markdownPreview: document.getElementById('markdownPreview'),
        responsibilitiesList: document.getElementById('responsibilitiesList'),
        skillsList: document.getElementById('skillsList'),
        prioritySkillsList: document.getElementById('prioritySkillsList'),
        bonusSkillsList: document.getElementById('bonusSkillsList'),
        coreSkillsSection: document.getElementById('coreSkillsSection'),
        prioritySkillsSection: document.getElementById('prioritySkillsSection'),
        bonusSkillsSection: document.getElementById('bonusSkillsSection'),
        leftSidebar: document.getElementById('leftSidebar'),
        mainContent: document.getElementById('mainContent')
    };

    // 进度步骤配置（从后端获取，这里作为默认值）
    const progressStepsConfig = [
        {title: '核心职责确定类', icon: '📋'},
        {title: '核心技能需求类', icon: '⚡'},
        {title: '扩展技能和工具类', icon: '🛠️'},
        {title: '简历加分项权重类', icon: '⭐'}
    ];

    // 初始化进度步骤
    function initProgressSteps() {
        elements.progressSteps.innerHTML = progressStepsConfig.map((step, index) => `
                <div class="progress-step flex items-center space-x-3 p-3 rounded-lg transition-all duration-300" data-step="${index}">
                    <div class="progress-ring" data-step="${index}">
                        ${step.icon}
                    </div>
                    <div>
                        <div class="text-white font-medium text-sm">${step.title}</div>
                        <div class="status-badge hidden" data-step="${index}">待开始</div>
                    </div>
                </div>
            `).join('');
    }

    // 更新进度
    function updateProgress(currentRound) {
        document.querySelectorAll('.progress-step').forEach((step, index) => {
            const ring = step.querySelector('.progress-ring');
            const badge = step.querySelector('.status-badge');

            step.classList.remove('bg-white/5');
            ring.classList.remove('active', 'completed');
            badge.classList.remove('active', 'completed');
            badge.classList.add('hidden');

            if (index < currentRound) {
                // 之前的轮次显示为已完成
                ring.classList.add('completed');
                badge.textContent = '已完成';
                badge.classList.remove('hidden');
                badge.classList.add('completed');
            } else if (index === currentRound) {
                // 当前轮次显示为进行中
                step.classList.add('bg-white/5');
                ring.classList.add('active');
                badge.textContent = '进行中';
                badge.classList.remove('hidden');
                badge.classList.add('active');
            }
        });
    }

    // 显示通知
    function showNotification(message, type = 'success') {
        elements.notification.textContent = message;
        elements.notification.className = `notification ${type}`;
        elements.notification.classList.add('show');

        setTimeout(() => {
            elements.notification.classList.remove('show');
        }, 3000);
    }

    // 显示/隐藏加载动画
    function showLoading(show, text = '正在生成JD，请稍候...') {
        if (show) {
            elements.loadingText.textContent = text;
            elements.loadingScreen.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        } else {
            elements.loadingScreen.classList.add('hidden');
            document.body.style.overflow = 'auto';
        }
    }

    // 切换界面
    function switchScreen(screenName) {
        ['companyConfigScreen', 'jobConsultScreen', 'basicInfoConfigScreen', 'welcomeScreen', 'conversationScreen', 'summaryScreen', 'jdModificationScreen'].forEach(screen => {
            elements[screen].style.display = 'none';
        });
        elements[screenName].style.display = 'block';

        // 根据当前屏幕控制左侧面板的显示
        const progressPanel = document.getElementById('progressPanel');
        const companyInfoCard = elements.companyInfoCard;
        const mainContainer = document.querySelector('.max-w-7xl');


        if (screenName === 'jdModificationScreen') {
            // 只在JD修改界面隐藏左侧面板
            elements.leftSidebar.style.display = 'none';
            elements.mainContent.className = 'lg:col-span-4';

            // 增加主容器宽度，使其接近全屏
            if (mainContainer) {
                mainContainer.classList.remove('max-w-7xl');
                mainContainer.classList.add('max-w-[95vw]');
            }
        } else if (screenName === 'companyConfigScreen') {
            // 公司配置页面：隐藏左侧面板，主内容区域居中
            elements.leftSidebar.style.display = 'none';
            elements.mainContent.className = 'lg:col-span-4 flex justify-center items-center';

            // 保持主容器原始宽度
            if (mainContainer) {
                mainContainer.classList.remove('max-w-[95vw]');
                mainContainer.classList.add('max-w-7xl');
            }

            // 隐藏进度面板和公司信息卡片
            if (progressPanel) progressPanel.style.display = 'none';
            if (companyInfoCard) companyInfoCard.style.display = 'none';
        } else {
            // 其他页面正常显示左侧面板
            elements.leftSidebar.style.display = 'block';
            elements.mainContent.className = 'lg:col-span-3';

            // 恢复主容器原始宽度
            if (mainContainer) {
                mainContainer.classList.remove('max-w-[95vw]');
                mainContainer.classList.add('max-w-7xl');
            }

            // 根据不同页面控制进度面板和公司信息卡片的显示
            if (progressPanel && companyInfoCard) {
                if (screenName === 'jobConsultScreen') {
                    // 岗位咨询阶段：隐藏进度面板，显示公司信息卡片
                    progressPanel.style.display = 'none';
                    if (state.companyInfo) {
                        companyInfoCard.style.display = 'block';
                    }
                } else {
                    // 对话及后续阶段：显示进度面板，显示公司信息卡片
                    progressPanel.style.display = 'block';
                    if (state.companyInfo) {
                        companyInfoCard.style.display = 'block';
                    }
                }
            }
        }
    }

    // 加载公司信息
    function loadCompanyInfo() {
        const savedInfo = localStorage.getItem('companyInfo');
        if (savedInfo) {
            try {
                state.companyInfo = JSON.parse(savedInfo);
                displayCompanyInfo();
                switchScreen('jobConsultScreen');
                elements.companyInfoCard.style.display = 'block';
            } catch (error) {
                console.error('加载公司信息失败:', error);
                localStorage.removeItem('companyInfo');
                switchScreen('companyConfigScreen');
            }
        } else {
            switchScreen('companyConfigScreen');
        }
    }

    // 显示公司信息
    function displayCompanyInfo() {
        if (!state.companyInfo) return;

        const info = state.companyInfo;
        elements.companyInfoContent.innerHTML = `
                <div class="company-info-item">
                    <span class="company-info-label">公司名称</span>
                    <span class="company-info-value">${info.name}</span>
                </div>
                <div class="company-info-item">
                    <span class="company-info-label">所属行业</span>
                    <span class="company-info-value">${info.industry}</span>
                </div>
                <div class="company-info-item">
                    <span class="company-info-label">公司规模</span>
                    <span class="company-info-value">${info.size}</span>
                </div>
                <div class="company-info-item">
                    <span class="company-info-label">办公地点</span>
                    <span class="company-info-value">${info.location}</span>
                </div>
                ${info.stage ? `
                <div class="company-info-item">
                    <span class="company-info-label">发展阶段</span>
                    <span class="company-info-value">${info.stage}</span>
                </div>
                ` : ''}
                ${info.funding ? `
                <div class="company-info-item">
                    <span class="company-info-label">融资情况</span>
                    <span class="company-info-value">${info.funding}</span>
                </div>
                ` : ''}
            `;
    }

    // 保存公司信息
    function saveCompanyInfo() {
        // 验证必填字段
        const requiredFields = [
            {element: elements.companyName, name: '公司名称'},
            {element: elements.companyIndustry, name: '所属行业'},
            {element: elements.companySize, name: '公司规模'},
            {element: elements.companyLocation, name: '办公地点'},
            {element: elements.companyBusiness, name: '业务描述'}
        ];

        for (const field of requiredFields) {
            if (!field.element.value.trim()) {
                showNotification(`请填写${field.name}`, 'error');
                field.element.focus();
                return;
            }
        }

        // 收集信息
        const companyInfo = {
            name: elements.companyName.value.trim(),
            industry: elements.companyIndustry.value.trim(),
            size: elements.companySize.value,
            location: elements.companyLocation.value.trim(),
            stage: elements.companyStage.value,
            funding: elements.companyFunding.value,
            business: elements.companyBusiness.value.trim(),
            culture: elements.companyCulture.value.trim()
        };

        // 保存到localStorage和state
        localStorage.setItem('companyInfo', JSON.stringify(companyInfo));
        state.companyInfo = companyInfo;

        // 显示公司信息卡片
        displayCompanyInfo();
        elements.companyInfoCard.style.display = 'block';

        // 同时保存到服务器文件
        saveCompanyInfoToFile(companyInfo);

        // 切换到岗位咨询界面
        switchScreen('jobConsultScreen');
        showNotification('公司信息保存成功！', 'success');
    }

    // 编辑公司信息
    function editCompanyInfo() {
        if (state.companyInfo) {
            // 填入现有信息
            elements.companyName.value = state.companyInfo.name || '';
            elements.companyIndustry.value = state.companyInfo.industry || '';
            elements.companySize.value = state.companyInfo.size || '';
            elements.companyLocation.value = state.companyInfo.location || '';
            elements.companyStage.value = state.companyInfo.stage || '';
            elements.companyFunding.value = state.companyInfo.funding || '';
            elements.companyBusiness.value = state.companyInfo.business || '';
            elements.companyCulture.value = state.companyInfo.culture || '';
        }
        switchScreen('companyConfigScreen');
    }

    // 跳过公司配置
    function skipCompanyConfig() {
        switchScreen('jobConsultScreen');
    }

    // 获取岗位建议
    async function getPositionAdvice() {
        const positionName = elements.positionInput.value.trim();
        if (!positionName) {
            showNotification('请输入要招聘的岗位名称', 'error');
            return;
        }

        state.positionName = positionName;
        showLoading(true, '正在分析岗位，生成专业建议...');

        try {
            // 构建精简的请求内容
            const requestContent = `请为"${positionName}"这个岗位提供精简的招聘建议，请按以下格式输出：

## 1. 岗位市场分析
- **市场需求**：该岗位的市场需求情况
- **薪资水平**：不同经验层次的薪资范围
- **人才供给**：人才市场供给情况分析

## 2. 核心技能要求
- **核心技能**：候选人必须具备的核心技能
- **加分技能**：优先考虑的额外技能
- **技术栈**：相关的技术栈要求

## 3. 招聘难点与解决方案
- **常见难点**：招聘该岗位的主要困难
- **解决建议**：针对性的解决方案
- **注意事项**：招聘过程中需要注意的要点

请用通俗易懂的语言，帮助不熟悉该岗位的HR更好地理解和招聘。只需要以上三个模块，不要其他内容。`;

            const response = await fetch('/api/v1/get_position_advice', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    position_name: positionName,
                    company_info: state.companyInfo,
                    request_content: requestContent
                })
            });

            const data = await response.json();

            if (data.success) {
                state.positionAdvice = data.advice;
                elements.positionAdviceContent.innerHTML = formatAdviceContent(data.advice);
                elements.positionAdviceContent.className = 'text-white/90 advice-content';
                elements.positionAdviceArea.style.display = 'block';
                showNotification('岗位分析完成！', 'success');
            } else {
                showNotification('获取岗位建议失败: ' + data.message, 'error');
            }
        } catch (error) {
            console.error('Error:', error);
            showNotification('网络错误，请重试', 'error');
        } finally {
            showLoading(false);
        }
    }

    // 格式化AI建议内容
    function formatAdviceContent(content) {
        // 转换markdown格式到HTML
        let formattedContent = content
            // 处理标题
            .replace(/###\s*(.*?)(?=\n|$)/g, '<h4>$1</h4>')
            .replace(/##\s*(.*?)(?=\n|$)/g, '<h3>$1</h3>')
            // 处理粗体
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            // 处理列表项
            .replace(/^\d+\.\s*(.*?)(?=\n|$)/gm, '<li>$1</li>')
            .replace(/^-\s*(.*?)(?=\n|$)/gm, '<li>$1</li>')
            // 处理段落
            .replace(/\n\n/g, '</p><p>')
            .replace(/\n/g, '<br/>');

        // 包装在段落中
        formattedContent = '<p>' + formattedContent + '</p>';

        // 处理列表
        formattedContent = formattedContent.replace(/(<li>.*?<\/li>)/gs, function (match) {
            return '<ul>' + match + '</ul>';
        });

        // 创建分段显示
        const sections = formattedContent.split(/(<h[34]>.*?<\/h[34]>)/);
        let result = '';

        for (let i = 0; i < sections.length; i++) {
            if (sections[i].match(/<h[34]>/)) {
                // 这是一个标题，创建新的section
                const title = sections[i];
                const content = sections[i + 1] || '';
                result += `<div class="advice-section">${title}${content}</div>`;
                i++; // 跳过下一个内容部分
            } else if (sections[i].trim() && !sections[i].match(/<h[34]>/)) {
                result += sections[i];
            }
        }

        return result || formattedContent;
    }

    // 进入JD创建流程
    async function proceedToJDCreation() {
        // 将岗位信息和建议合并到jobDescription
        state.jobDescription = `招聘岗位：${state.positionName}\n\nAI分析建议：\n${state.positionAdvice}`;

        // 进入基本信息配置界面
        switchScreen('basicInfoConfigScreen');
    }

    // 保存基本信息
    function saveBasicInfo() {
        // 验证必填字段
        const requiredFields = [
            {element: elements.salaryRange, name: '薪资范围'},
            {element: elements.jobType, name: '岗位性质'},
            {element: elements.educationLevel, name: '学历要求'},
            {element: elements.workExperience, name: '工作经验'},
            {element: elements.workLocation, name: '工作地点'},
            {element: elements.workMode, name: '工作方式'}
        ];

        for (const field of requiredFields) {
            if (!field.element.value.trim()) {
                showNotification(`请填写${field.name}`, 'error');
                field.element.focus();
                return;
            }
        }

        // 收集基本信息
        const basicInfo = {
            salaryRange: elements.salaryRange.value.trim(),
            jobType: elements.jobType.value,
            educationLevel: elements.educationLevel.value,
            ageRange: elements.ageRange.value.trim(),
            workExperience: elements.workExperience.value,
            workLocation: elements.workLocation.value.trim(),
            workMode: elements.workMode.value,
            majorRequirement: elements.majorRequirement.value.trim()
        };

        // 保存到state
        state.basicInfo = basicInfo;

        // 重置会话状态并开始对话
        startConversationWithBasicInfo();
    }

    // 跳过基本信息配置
    function skipBasicInfo() {
        state.basicInfo = null;
        startConversationWithBasicInfo();
    }

    // 开始对话（包含基本信息）
    async function startConversationWithBasicInfo() {
        // 重置会话状态
        await resetSession();

        // 直接进入对话界面
        switchScreen('conversationScreen');

        addMessage('system', '欢迎使用宇曜科技智能JD生成器，我将通过4轮核心对话帮您生成专业的招聘JD。');
        updateProgress(0);

        // 直接开始第1轮对话
        await fetchNextQuestion();
    }

    // 保存公司信息到文件
    async function saveCompanyInfoToFile(companyInfo) {
        try {
            const response = await fetch('/api/v1/save_company_info', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    company_info: companyInfo
                })
            });

            const data = await response.json();
            if (data.success) {
                console.log('公司信息已保存到文件:', data.file_path);
            } else {
                console.error('保存公司信息到文件失败:', data.message);
            }
        } catch (error) {
            console.error('保存公司信息到文件出错:', error);
        }
    }

    // 生成公司描述
    function generateCompanyDescription() {
        if (!state.companyInfo) return '';

        const info = state.companyInfo;
        let description = `公司名称：${info.name}，`;
        description += `所属行业：${info.industry}，`;
        description += `公司规模：${info.size}，`;
        description += `办公地点：${info.location}`;

        if (info.stage) description += `，发展阶段：${info.stage}`;
        if (info.funding) description += `，融资情况：${info.funding}`;

        description += `。业务描述：${info.business}`;

        if (info.culture) description += `。公司文化：${info.culture}`;

        return description;
    }

    // 添加消息到对话区域
    function addMessage(type, content) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;

        // 优化文本显示
        let formattedContent = content;

        // 如果是系统消息且内容较长，尝试格式化显示
        if (type === 'system' && content.length > 200) {
            // 检测是否为第八轮信息总结
            if (content.includes('信息总结') || content.includes('收集到以下核心要求')) {
                // 将长文本分段显示
                formattedContent = formatSummaryMessage(content);
            }
        }

        // 如果是用户消息，尝试分点显示
        if (type === 'user') {
            formattedContent = formatUserMessage(content);
        }

        messageDiv.innerHTML = formattedContent;

        elements.conversationArea.appendChild(messageDiv);
        elements.conversationArea.scrollTop = elements.conversationArea.scrollHeight;

        state.conversationHistory.push({type, content});
    }

    // 格式化用户消息，分点显示
    function formatUserMessage(content) {
        // 如果内容包含分号，按分号分割并显示为列表
        if (content.includes('；') || content.includes(';')) {
            const items = content.replace(/；/g, ';').split(';').filter(item => item.trim());
            if (items.length > 1) {
                return items.map((item, index) => `<div class="user-point">${index + 1}. ${item.trim()}</div>`).join('');
            }
        }

        // 如果内容包含换行，按换行分割并显示为列表
        if (content.includes('\n')) {
            const lines = content.split('\n').filter(line => line.trim());
            if (lines.length > 1) {
                return lines.map((line, index) => `<div class="user-point">${index + 1}. ${line.trim()}</div>`).join('');
            }
        }

        return content;
    }

    // 格式化总结消息
    function formatSummaryMessage(content) {
        // 提取关键信息并格式化显示
        let formatted = '<div class="summary-message">';

        // 尝试提取薪资范围
        const salaryMatch = content.match(/薪资范围[：:]\s*([^，。\n]+)/);
        if (salaryMatch) {
            formatted += `<div class="summary-item"><strong>薪资范围:</strong> ${salaryMatch[1]}</div>`;
        }

        // 尝试提取核心职责
        const respMatch = content.match(/核心职责[：:]\s*([^。]+)/);
        if (respMatch) {
            const responsibilities = respMatch[1].split(/[,，、]/).filter(item => item.trim());
            formatted += '<div class="summary-item"><strong>核心职责:</strong></div><ul>';
            responsibilities.forEach(item => {
                formatted += `<li>${item.trim()}</li>`;
            });
            formatted += '</ul>';
        }

        // 尝试提取必备能力
        const skillsMatch = content.match(/必备能力[：:]\s*([^。]+)/);
        if (skillsMatch) {
            const skills = skillsMatch[1].split(/[,，、]/).filter(item => item.trim());
            formatted += '<div class="summary-item"><strong>必备能力:</strong></div><ul>';
            skills.forEach(item => {
                formatted += `<li>${item.trim()}</li>`;
            });
            formatted += '</ul>';
        }

        // 如果没有成功提取结构化信息，则保持原样
        if (formatted === '<div class="summary-message">') {
            return content;
        }

        formatted += '</div>';
        return formatted;
    }

    // 从AI回复中提取纯问题内容（移除选项部分）
    function extractQuestionFromMessage(message) {
        // 查找选项开始的位置的各种模式
        const optionPatterns = [
            /参考选项[\s\S]*?：/i,
            /以下是一些参考选项/i,
            /您可以从以下选项中选择/i,
            /参考答案/i,
            /可选项/i,
            /\n\s*[①②③④⑤⑥⑦⑧⑨⑩]/,  // 中文圆圈数字
            /\n\s*[1-9]\.\s+/,  // 数字列表
            /\n\s*[A-Z][\.\)]\s+/,  // 字母选项
            /\n\s*【[^】]+】/,  // 中文括号选项
            /（[多可]选[择项]*）/,  // 多选或可选标记
            /\n\s*\w+：[^：\n]+/  // 冒号分隔的选项
        ];

        let cleanMessage = message.trim();

        // 移除选项部分
        for (const pattern of optionPatterns) {
            const match = cleanMessage.search(pattern);
            if (match !== -1) {
                cleanMessage = cleanMessage.substring(0, match).trim();
                break;
            }
        }

        // 进一步清理格式
        cleanMessage = cleanMessage
            .replace(/：\s*$/, '')  // 移除末尾冒号
            .replace(/\s*$/, '')    // 移除末尾空白
            .replace(/\n\n+/g, '\n') // 合并多余换行
            .trim();

        // 如果清理后内容太短，返回原消息的前200字符
        if (cleanMessage.length < 20) {
            return message.substring(0, 200).trim() + (message.length > 200 ? '...' : '');
        }

        return cleanMessage;
    }

    // 显示选项
    function showOptions(options) {
        elements.optionsArea.innerHTML = '';

        if (!options || options.length === 0) return;

        const optionsContainer = document.createElement('div');
        optionsContainer.className = 'space-y-2 mb-4';

        const title = document.createElement('div');
        title.className = 'text-white/70 text-sm mb-3';
        title.textContent = '参考选项（仅供参考，选择点击可快速填入）：';
        optionsContainer.appendChild(title);

        // 过滤第一轮薪资询问时的无关选项
        const filteredOptions = filterIrrelevantOptions(options);

        filteredOptions.forEach((option, index) => {
            const optionDiv = document.createElement('div');
            optionDiv.className = 'option-item';
            optionDiv.textContent = `${index + 1}. ${option}`;

            optionDiv.addEventListener('click', () => {
                // 如果选项中包含分号，按分号分割并添加换行
                if (option.includes('；') || option.includes(';')) {
                    const items = option.replace(/；/g, ';').split(';').map(item => item.trim()).filter(Boolean);
                    const formattedOption = items.join('\n');

                    const currentValue = elements.userInput.value.trim();
                    if (currentValue) {
                        elements.userInput.value = currentValue + '\n' + formattedOption;
                    } else {
                        elements.userInput.value = formattedOption;
                    }
                } else {
                    const currentValue = elements.userInput.value.trim();
                    if (currentValue) {
                        elements.userInput.value = currentValue + '\n' + option;
                    } else {
                        elements.userInput.value = option;
                    }
                }
                elements.userInput.focus();
            });

            optionsContainer.appendChild(optionDiv);
        });

        elements.optionsArea.appendChild(optionsContainer);
    }

    // 过滤第一轮薪资询问时的无关选项
    function filterIrrelevantOptions(options) {
        // 检查当前是否是第一轮且询问薪资
        if (state.currentRound === 1) {
            const lastSystemMessage = state.conversationHistory.filter(msg => msg.type === 'system').pop();
            if (lastSystemMessage && (lastSystemMessage.content.includes('薪资') || lastSystemMessage.content.includes('薪酬'))) {
                // 只保留包含薪资相关信息的选项
                return options.filter(option =>
                    option.includes('K') ||
                    option.includes('k') ||
                    option.includes('薪') ||
                    option.includes('元') ||
                    option.includes('万') ||
                    /\d+/.test(option) // 包含数字
                );
            }
        }
        return options;
    }

    // 开始对话
    async function startConversation() {
        const jobDescription = elements.jobDescription.value.trim();
        if (!jobDescription) {
            showNotification('请描述要招聘的岗位基本情况', 'error');
            return;
        }

        // 重置会话状态
        await resetSession();

        state.jobDescription = jobDescription;
        switchScreen('conversationScreen');

        addMessage('system', '欢迎使用宇曜科技智能JD生成器，我将通过4轮对话帮您生成专业的招聘JD。');
        updateProgress(0);

        await fetchNextQuestion();
    }

    // 获取下一个问题
    async function fetchNextQuestion(userInput = null) {
        showLoading(true, '正在获取下一个问题...');
        elements.sendBtn.disabled = true;

        try {
            console.log(`发送请求，当前轮次: ${state.currentRound}, 用户输入: ${userInput}`);
            const requestBody = {
                company_description: generateCompanyDescription() + '\n\n岗位基本情况：' + state.jobDescription,
                current_round: state.currentRound,
                selected_options: state.selectedOptions,
                custom_inputs: state.customInputs,
                user_input: userInput
            };

            // 如果有基本信息，添加到请求中
            if (state.basicInfo) {
                requestBody.basic_info = state.basicInfo;
            }

            const response = await fetch('/api/v1/conversation', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(requestBody)
            });

            const data = await response.json();

            if (data.success) {
                if (data.is_summary_round) {
                    console.log('进入总结阶段');
                    showSummaryScreen(data.message, data.summary_data);
                    return;
                }

                // 更新轮次
                console.log(`收到响应，next_round: ${data.next_round}`);
                state.currentRound = data.next_round;
                console.log(`更新后当前轮次: ${state.currentRound}`);

                // 进度显示：使用当前轮次
                updateProgress(state.currentRound - 1); // 显示当前进行的轮次

                // 提取纯问题内容（移除选项部分）
                const cleanMessage = extractQuestionFromMessage(data.message);
                addMessage('system', cleanMessage);

                // 单独显示选项
                showOptions(data.options);

                // 标记这是否为补充询问
                state.isSupplementQuestion = data.is_supplement || false;

                elements.userInput.focus();
            } else {
                console.error('获取问题失败:', data.message);
                showNotification('获取问题失败: ' + data.message, 'error');
            }
        } catch (error) {
            console.error('Error:', error);
            showNotification('网络错误，请重试', 'error');
        } finally {
            showLoading(false);
            elements.sendBtn.disabled = false;
        }
    }

    // 获取补充询问（按照终端版本逻辑）
    async function fetchSupplementQuestion() {
        showLoading(true, '正在生成补充问题...');

        try {
            const requestBody = {
                company_description: generateCompanyDescription() + '\n\n岗位基本情况：' + state.jobDescription,
                current_round: state.currentRound, // 传入当前轮次
                selected_options: state.selectedOptions,
                custom_inputs: state.customInputs,
                user_input: null
            };

            // 如果有基本信息，添加到请求中
            if (state.basicInfo) {
                requestBody.basic_info = state.basicInfo;
            }

            const response = await fetch('/api/v1/supplement_question', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(requestBody)
            });

            const data = await response.json();

            if (data.success) {
                // 提取纯问题内容
                const cleanMessage = extractQuestionFromMessage(data.message);
                addMessage('system', cleanMessage);

                // 显示选项
                showOptions(data.options);

                // 标记为补充询问
                state.isSupplementQuestion = true;

                elements.userInput.placeholder = "补充回答（输入'跳过'或直接按Enter跳过）：";
                elements.userInput.focus();
            } else {
                // 无需补充询问，直接进入下一轮
                console.log('无需补充询问，进入下一轮');
                await fetchNextQuestion();
            }
        } catch (error) {
            console.error('获取补充问题失败:', error);
            showNotification('获取补充问题失败', 'error');
            // 即使补充询问失败，也要继续进入下一轮
            await fetchNextQuestion();
        } finally {
            showLoading(false);
        }
    }

    // 发送用户输入（按照终端版本逻辑）
    async function sendUserInput() {
        const userInput = elements.userInput.value.trim();

        // 如果是补充询问且输入为空或"跳过"，跳过补充询问
        if (state.isSupplementQuestion && (!userInput || userInput === '跳过')) {
            state.isSupplementQuestion = false;
            elements.userInput.value = '';
            elements.userInput.placeholder = "请输入您的回答...（输入'生成JD'或'结束'可完成对话）";
            elements.optionsArea.innerHTML = '';
            // 跳过补充询问，直接进入下一轮
            await fetchNextQuestion();
            return;
        }

        if (!userInput) {
            showNotification('请输入您的回答', 'error');
            return;
        }

        addMessage('user', userInput);
        elements.userInput.value = '';
        elements.userInput.placeholder = "请输入您的回答...（输入'生成JD'或'结束'可完成对话）";
        elements.optionsArea.innerHTML = '';

        // 检查用户是否要求生成JD
        if (userInput === "生成JD" || userInput === "结束") {
            showNotification('正在生成JD，请稍候...', 'info');
            await fetchNextQuestion(userInput);
            return;
        }

        // 如果是补充询问的回答，直接进入下一轮
        if (state.isSupplementQuestion) {
            state.isSupplementQuestion = false;
            // 补充询问回答完毕，现在进入下一轮
            await fetchNextQuestion();
        } else {
            // 正常回答后，直接进入下一轮
            // 注意：这里传递用户输入，后端会根据当前轮次和用户输入生成下一轮问题
            console.log(`用户回答完毕，准备进入下一轮，当前轮次: ${state.currentRound}`);
            await fetchNextQuestion(userInput);
        }
    }

    // 显示总结界面
    function showSummaryScreen(message, summaryData = null) {
        switchScreen('summaryScreen');

        if (summaryData && typeof summaryData === 'object') {
            // 添加调试信息显示
            const debugInfo = document.createElement('div');
            debugInfo.className = 'glass-input p-4 rounded-lg mb-4';
            debugInfo.innerHTML = `
                <h4 class="text-yellow-400 mb-2">调试信息（JSON数据）:</h4>
                <pre class="text-white text-xs whitespace-pre-wrap">${JSON.stringify(summaryData, null, 2)}</pre>
            `;
            elements.summaryContent.appendChild(debugInfo);

            displayStructuredSummary(summaryData);
        } else {
            elements.summaryContent.innerHTML = `
                    <div class="glass-input p-6 rounded-lg">
                        <pre class="text-white whitespace-pre-wrap">${message}</pre>
                    </div>
                `;
        }

        updateProgress(3); // 显示第4轮完成状态
    }

    // 提取核心职责内容
    function extractCoreContent(text) {
        // 移除包装性内容的模式
        const wrapperPatterns = [
            /与.*?协作/i,
            /与.*?合作/i,
            /与.*?一起/i,
            /负责.*?的/i,
            /参与.*?的/i,
            /推动.*?的/i,
            /确保.*?的/i,
            /协助.*?的/i,
            /配合.*?的/i
        ];

        // 尝试提取核心部分
        for (const pattern of wrapperPatterns) {
            const match = text.match(pattern);
            if (match && match.index > 0) {
                // 如果匹配到包装性内容，并且不是在开头，可能是前置包装
                return text.substring(match.index + match[0].length).trim();
            } else if (match && match.index === 0) {
                // 如果匹配到包装性内容在开头，可能是后置包装
                const endIndex = text.indexOf('，', match.index + match[0].length);
                if (endIndex > 0) {
                    return text.substring(match.index + match[0].length, endIndex).trim();
                }
            }
        }

        // 如果没有明确的包装模式，尝试按逗号分割并取最长的片段
        const segments = text.split(/[，,；;]/);
        if (segments.length > 1) {
            return segments.reduce((a, b) => a.length > b.length ? a : b).trim();
        }

        // 如果上述方法都不适用，返回原文
        return text;
    }

    // 解析职责或技能列表
    function parseList(text, type) {
        if (!text) return [];

        // 如果text是数组，直接返回
        if (Array.isArray(text)) {
            console.log(`parseList接收到数组: ${JSON.stringify(text)}`);
            return text.map(item => {
                if (typeof item === 'string') {
                    return type === 'responsibilities' ? extractCoreContent(item) : item;
                }
                return String(item);
            });
        }

        // 确保text是字符串
        if (typeof text !== 'string') {
            console.warn(`parseList接收到非字符串类型: ${typeof text}`, text);
            text = String(text);
        }

        // 清理文本，移除多余的空白字符
        text = text.trim();

        // 匹配列表项的模式
        const patterns = [
            /(\d+)[\.、]\s*(.*?)(?=\n\d+[\.、]|\n$|$)/g,  // 数字列表
            /[•●]\s*(.*?)(?=\n[•●]|\n$|$)/g,  // 圆点列表
            /[-–—]\s*(.*?)(?=\n[-–—]|\n$|$)/g,  // 短横线列表
            /[①②③④⑤⑥⑦⑧⑨⑩]\s*(.*?)(?=\n[①②③④⑤⑥⑦⑧⑨⑩]|\n$|$)/g  // 中文圆圈数字
        ];

        let items = [];
        let matches;

        // 尝试使用各种模式提取列表项
        for (const pattern of patterns) {
            matches = [...text.matchAll(pattern)];
            if (matches.length > 0) {
                items = matches.map(match => match[2] || match[1]).filter(item => item.trim().length > 0);
                break;
            }
        }

        // 如果没有找到列表项，尝试按分号或逗号分割
        if (items.length === 0) {
            if (text.includes('；') || text.includes(';')) {
                items = text.split(/[；;]/).filter(item => item.trim().length > 0);
            } else if (text.includes('，') || text.includes(',')) {
                items = text.split(/[，,]/).filter(item => item.trim().length > 0);
            } else {
                // 最后尝试按行分割
                items = text.split(/\n+/).filter(line => line.trim().length > 0);
            }
        }

        // 清理每个项目，移除多余的空白和标点
        items = items.map(item => {
            item = item.trim();
            // 移除开头的数字和标点
            item = item.replace(/^[\d\.、①②③④⑤⑥⑦⑧⑨⑩\s]+/, '');
            // 移除末尾的标点
            item = item.replace(/[，。；,.;\s]+$/, '');
            return item.trim();
        }).filter(item => item.length > 0);

        // 对于职责，提取核心内容
        if (type === 'responsibilities') {
            items = items.map(item => extractCoreContent(item));
        }

        return items;
    }

    // 创建可拖拽的项目
    function createDraggableItem(content, index, container) {
        const item = document.createElement('div');
        item.className = 'draggable-item';
        item.draggable = true;
        item.dataset.index = index;

        const handle = document.createElement('div');
        handle.className = 'drag-handle';
        item.appendChild(handle);

        const contentDiv = document.createElement('div');
        contentDiv.className = 'item-content';

        const numberSpan = document.createElement('span');
        numberSpan.className = 'item-number';
        numberSpan.textContent = index + 1;
        contentDiv.appendChild(numberSpan);

        const textSpan = document.createElement('span');
        textSpan.textContent = content;
        textSpan.className = 'item-text';
        contentDiv.appendChild(textSpan);

        // 添加编辑按钮
        const editBtn = document.createElement('button');
        editBtn.className = 'edit-item-btn';
        editBtn.innerHTML = '✏️';
        editBtn.title = '编辑';
        editBtn.onclick = () => editItem(item, content, container);
        contentDiv.appendChild(editBtn);

        // 添加删除按钮
        const deleteBtn = document.createElement('button');
        deleteBtn.className = 'delete-item-btn';
        deleteBtn.innerHTML = '🗑️';
        deleteBtn.title = '删除';
        deleteBtn.onclick = () => deleteItem(item, container);
        contentDiv.appendChild(deleteBtn);

        item.appendChild(contentDiv);
        container.appendChild(item);

        // 添加拖拽事件
        item.addEventListener('dragstart', () => {
            item.classList.add('dragging');
        });

        item.addEventListener('dragend', () => {
            item.classList.remove('dragging');
            // 更新索引和编号
            updateItemsOrder(container);
        });

        return item;
    }

    // 编辑项目
    function editItem(item, originalContent, container) {
        const textSpan = item.querySelector('.item-text');
        const originalText = textSpan.textContent;

        // 创建输入框
        const input = document.createElement('input');
        input.type = 'text';
        input.value = originalText;
        input.className = 'edit-input';

        // 替换文本为输入框
        textSpan.style.display = 'none';
        textSpan.parentNode.insertBefore(input, textSpan);
        input.focus();

        // 处理保存和取消
        function saveEdit() {
            const newContent = input.value.trim();
            if (newContent && newContent !== originalText) {
                textSpan.textContent = newContent;
                // 更新状态中的内容
                updateItemContent(container, item.dataset.index, newContent);
            }
            textSpan.style.display = 'inline';
            input.remove();
        }

        function cancelEdit() {
            textSpan.style.display = 'inline';
            input.remove();
        }

        input.addEventListener('blur', saveEdit);
        input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                saveEdit();
            } else if (e.key === 'Escape') {
                cancelEdit();
            }
        });
    }

    // 删除项目
    function deleteItem(item, container) {
        if (confirm('确定要删除这个项目吗？')) {
            const index = parseInt(item.dataset.index);
            item.remove();

            // 更新状态中的内容
            removeItemContent(container, index);

            // 重新排序
            updateItemsOrder(container);
        }
    }

    // 更新项目内容
    function updateItemContent(container, index, newContent) {
        if (container.id === 'responsibilitiesList') {
            state.responsibilities[index] = newContent;
        } else if (container.id === 'skillsList') {
            state.skills[index] = newContent;
        } else if (container.id === 'prioritySkillsList') {
            state.prioritySkills[index] = newContent;
        } else if (container.id === 'bonusSkillsList') {
            state.bonusSkills[index] = newContent;
        }
    }

    // 删除项目内容
    function removeItemContent(container, index) {
        if (container.id === 'responsibilitiesList') {
            state.responsibilities.splice(index, 1);
        } else if (container.id === 'skillsList') {
            state.skills.splice(index, 1);
        } else if (container.id === 'prioritySkillsList') {
            state.prioritySkills.splice(index, 1);
        } else if (container.id === 'bonusSkillsList') {
            state.bonusSkills.splice(index, 1);
        }
    }

    // 更新项目顺序
    function updateItemsOrder(container) {
        const items = container.querySelectorAll('.draggable-item');
        items.forEach((item, index) => {
            item.dataset.index = index;
            const numberSpan = item.querySelector('.item-number');
            if (numberSpan) {
                numberSpan.textContent = index + 1;
            }
        });

        // 更新状态中的顺序
        if (container.id === 'responsibilitiesList') {
            state.responsibilities = Array.from(items).map(item =>
                item.querySelector('.item-text').textContent
            );
        } else if (container.id === 'skillsList') {
            state.skills = Array.from(items).map(item =>
                item.querySelector('.item-text').textContent
            );
        } else if (container.id === 'prioritySkillsList') {
            state.prioritySkills = Array.from(items).map(item =>
                item.querySelector('.item-text').textContent
            );
        } else if (container.id === 'bonusSkillsList') {
            state.bonusSkills = Array.from(items).map(item =>
                item.querySelector('.item-text').textContent
            );
        }
    }

    // 初始化拖拽容器
    function initDraggableContainer(container) {
        container.addEventListener('dragover', e => {
            e.preventDefault();
            const afterElement = getDragAfterElement(container, e.clientY);
            const draggable = document.querySelector('.dragging');
            if (draggable) {
                if (afterElement) {
                    container.insertBefore(draggable, afterElement);
                } else {
                    container.appendChild(draggable);
                }
            }
        });
    }

    // 获取拖拽后的位置
    function getDragAfterElement(container, y) {
        const draggableElements = [...container.querySelectorAll('.draggable-item:not(.dragging)')];

        return draggableElements.reduce((closest, child) => {
            const box = child.getBoundingClientRect();
            const offset = y - box.top - box.height / 2;

            if (offset < 0 && offset > closest.offset) {
                return {offset: offset, element: child};
            } else {
                return closest;
            }
        }, {offset: Number.NEGATIVE_INFINITY}).element;
    }

    // 显示结构化总结
    function displayStructuredSummary(jsonData) {
        console.log('收到的JSON数据:', jsonData); // 添加调试信息

        const container = elements.summaryContent;

        const fields = [
            {key: '职位', label: '职位名称', required: true},
            {key: '薪资', label: '薪资待遇'},
            {key: '地区', label: '工作地区'},
            {key: '行业', label: '所属行业'}
        ];

        // 如果有基本信息，使用基本信息填充默认值
        if (state.basicInfo) {
            const basicInfo = state.basicInfo;
            if (!jsonData['薪资'] && basicInfo.salaryRange) {
                jsonData['薪资'] = basicInfo.salaryRange;
            }
            if (!jsonData['地区'] && basicInfo.workLocation) {
                jsonData['地区'] = basicInfo.workLocation;
            }
        }

        const form = document.createElement('div');
        form.className = 'space-y-4';

        fields.forEach(field => {
            const fieldDiv = document.createElement('div');
            fieldDiv.innerHTML = `
                    <label class="block text-white font-medium mb-2">
                        ${field.label}${field.required ? ' *' : ''}:
                    </label>
                    <input 
                        type="text" 
                        id="field_${field.key}" 
                        value="${jsonData[field.key] || ''}"
                        class="w-full glass-input text-white placeholder-white/50 rounded-lg p-3 outline-none"
                        ${field.required ? 'required' : ''}
                    />
                `;
            form.appendChild(fieldDiv);
        });

        container.innerHTML = '';
        container.appendChild(form);

        // 提取并显示核心职责
        if (jsonData['核心职责']) {
            // 如果存在核心职责字段，直接解析
            console.log('找到核心职责字段:', jsonData['核心职责']);
            state.responsibilities = parseList(jsonData['核心职责'], 'responsibilities');
            console.log('解析后的核心职责:', state.responsibilities);
            displayResponsibilitiesList();
        } else if (jsonData['核心职责1'] || jsonData['核心职责2'] || jsonData['核心职责3']) {
            // 兼容旧格式
            const responsibilities = [];
            if (jsonData['核心职责1']) responsibilities.push(jsonData['核心职责1']);
            if (jsonData['核心职责2']) responsibilities.push(jsonData['核心职责2']);
            if (jsonData['核心职责3']) responsibilities.push(jsonData['核心职责3']);
            if (jsonData['核心职责4']) responsibilities.push(jsonData['核心职责4']);

            // 提取核心内容
            state.responsibilities = responsibilities.map(extractCoreContent);

            // 显示职责列表
            displayResponsibilitiesList();
        }

        // 检查元素是否存在
        console.log('检查元素是否存在:', {
            coreSkillsSection: !!elements.coreSkillsSection,
            prioritySkillsSection: !!elements.prioritySkillsSection,
            bonusSkillsSection: !!elements.bonusSkillsSection,
            skillsList: !!elements.skillsList,
            prioritySkillsList: !!elements.prioritySkillsList,
            bonusSkillsList: !!elements.bonusSkillsList
        });

        // 提取并显示技能要求
        extractAndDisplaySkills(jsonData);

        // 初始化拖拽功能
        if (elements.responsibilitiesList) initDraggableContainer(elements.responsibilitiesList);
        if (elements.skillsList) initDraggableContainer(elements.skillsList);
        if (elements.prioritySkillsList) initDraggableContainer(elements.prioritySkillsList);
        if (elements.bonusSkillsList) initDraggableContainer(elements.bonusSkillsList);
    }

    // 提取并显示不同类别的技能
    function extractAndDisplaySkills(jsonData) {
        console.log('开始提取技能数据...'); // 添加调试信息

        // 清空现有技能列表
        state.skills = [];
        state.prioritySkills = [];
        state.bonusSkills = [];

        // 检查元素是否存在
        if (!elements.coreSkillsSection) {
            console.error('coreSkillsSection 元素不存在');
            return;
        }
        if (!elements.prioritySkillsSection) {
            console.error('prioritySkillsSection 元素不存在');
            return;
        }
        if (!elements.bonusSkillsSection) {
            console.error('bonusSkillsSection 元素不存在');
            return;
        }

        // 提取核心技能
        if (jsonData['核心技能']) {
            console.log('找到核心技能字段:', jsonData['核心技能']); // 调试信息
            state.skills = parseList(jsonData['核心技能'], 'skills');
            console.log('解析后的核心技能:', state.skills); // 调试信息
            elements.coreSkillsSection.style.display = 'block';
        } else {
            console.log('未找到核心技能字段'); // 调试信息
            elements.coreSkillsSection.style.display = 'none';
        }

        // 提取拓展技能
        if (jsonData['拓展技能']) {
            console.log('找到拓展技能字段:', jsonData['拓展技能']); // 调试信息
            state.prioritySkills = parseList(jsonData['拓展技能'], 'skills');
            console.log('解析后的拓展技能:', state.prioritySkills); // 调试信息
            elements.prioritySkillsSection.style.display = 'block';
        } else {
            console.log('未找到拓展技能字段'); // 调试信息
            elements.prioritySkillsSection.style.display = 'none';
        }

        // 提取加分项
        if (jsonData['加分项']) {
            console.log('找到加分项字段:', jsonData['加分项']); // 调试信息
            state.bonusSkills = parseList(jsonData['加分项'], 'skills');
            console.log('解析后的加分项:', state.bonusSkills); // 调试信息
            elements.bonusSkillsSection.style.display = 'block';
        } else {
            console.log('未找到加分项字段'); // 调试信息
            elements.bonusSkillsSection.style.display = 'none';
        }

        // 如果没有分类的技能，尝试从其他字段中提取
        if (state.skills.length === 0) {
            // 尝试从必备技能字段提取
            if (jsonData['必备技能']) {
                console.log('尝试从必备技能字段提取:', jsonData['必备技能']); // 调试信息
                state.skills = parseList(jsonData['必备技能'], 'skills');
                elements.coreSkillsSection.style.display = 'block';
            }
            // 尝试从优先技能字段提取
            if (state.prioritySkills.length === 0 && jsonData['优先技能']) {
                console.log('尝试从优先技能字段提取:', jsonData['优先技能']); // 调试信息
                state.prioritySkills = parseList(jsonData['优先技能'], 'skills');
                elements.prioritySkillsSection.style.display = 'block';
            }
        }

        // 如果仍然没有技能，尝试从技能要求中提取
        if (state.skills.length === 0 && jsonData['技能要求']) {
            console.log('尝试从技能要求字段提取:', jsonData['技能要求']); // 调试信息
            // 解析技能列表
            const skillsText = jsonData['技能要求'];
            state.skills = parseList(skillsText, 'skills');

            // 如果没有成功解析出多个技能点，尝试按分号拆分
            if (state.skills.length <= 1 && skillsText.includes('；')) {
                state.skills = skillsText.split('；').filter(item => item.trim().length > 0);
            } else if (state.skills.length <= 1 && skillsText.includes(';')) {
                state.skills = skillsText.split(';').filter(item => item.trim().length > 0);
            }

            elements.coreSkillsSection.style.display = 'block';
        }

        console.log('最终技能数据:', { // 调试信息
            skills: state.skills,
            prioritySkills: state.prioritySkills,
            bonusSkills: state.bonusSkills
        });

        // 显示各类技能列表
        displaySkillsList();
        displayPrioritySkillsList();
        displayBonusSkillsList();
    }

    // 显示职责列表
    function displayResponsibilitiesList() {
        elements.responsibilitiesList.innerHTML = '';
        state.responsibilities.forEach((responsibility, index) => {
            createDraggableItem(responsibility, index, elements.responsibilitiesList);
        });
    }

    // 显示技能列表
    function displaySkillsList() {
        console.log('显示核心技能列表，技能数量:', state.skills.length);
        if (!elements.skillsList) {
            console.error('skillsList 元素不存在');
            return;
        }
        elements.skillsList.innerHTML = '';
        state.skills.forEach((skill, index) => {
            console.log(`创建核心技能项目 ${index}:`, skill);
            createDraggableItem(skill, index, elements.skillsList);
        });
    }

    // 显示优先技能列表
    function displayPrioritySkillsList() {
        console.log('显示拓展技能列表，技能数量:', state.prioritySkills.length);
        if (!elements.prioritySkillsList) {
            console.error('prioritySkillsList 元素不存在');
            return;
        }
        elements.prioritySkillsList.innerHTML = '';
        state.prioritySkills.forEach((skill, index) => {
            console.log(`创建拓展技能项目 ${index}:`, skill);
            createDraggableItem(skill, index, elements.prioritySkillsList);
        });
    }

    // 显示加分项列表
    function displayBonusSkillsList() {
        console.log('显示加分项列表，加分项数量:', state.bonusSkills.length);
        if (!elements.bonusSkillsList) {
            console.error('bonusSkillsList 元素不存在');
            return;
        }
        elements.bonusSkillsList.innerHTML = '';
        state.bonusSkills.forEach((skill, index) => {
            console.log(`创建加分项项目 ${index}:`, skill);
            createDraggableItem(skill, index, elements.bonusSkillsList);
        });
    }

    // 确认并生成JD
    async function confirmAndGenerateJD() {
        const structuredForm = document.querySelector('[id^="field_"]');
        let summaryData;
        let sortedData = null;

        if (structuredForm) {
            // 收集结构化数据
            const data = {};
            const fields = ['职位', '薪资', '地区', '行业'];

            for (const field of fields) {
                const input = document.getElementById(`field_${field}`);
                if (input) {
                    data[field] = input.value.trim();
                }
            }

            // 如果有基本信息，使用基本信息填充
            if (state.basicInfo) {
                data['薪资'] = state.basicInfo.salaryRange;
                data['地区'] = state.basicInfo.workLocation;
                // 其他基本信息字段也可以根据需要添加
            }

            // 添加排序后的核心职责
            state.responsibilities.forEach((resp, index) => {
                data[`核心职责${index + 1}`] = resp;
            });

            // 添加排序后的核心技能
            if (state.skills.length > 0) {
                data['核心技能'] = state.skills.map((skill, index) => `${index + 1}. ${skill}`).join('\n');
            }

            // 添加排序后的拓展技能
            if (state.prioritySkills.length > 0) {
                data['拓展技能'] = state.prioritySkills.map((skill, index) => `${index + 1}. ${skill}`).join('\n');
            }

            // 添加排序后的加分项
            if (state.bonusSkills.length > 0) {
                data['加分项'] = state.bonusSkills.map((skill, index) => `${index + 1}. ${skill}`).join('\n');
            }

            // 检查必填字段
            if (!data['职位']) {
                showNotification('请填写职位名称', 'error');
                return;
            }

            summaryData = data;

            // 创建排序后的数据，确保使用拖拽后的顺序
            sortedData = {
                '职位': data['职位'],
                '薪资': data['薪资'],
                '地区': data['地区'],
                '行业': data['行业']
            };

            // 添加排序后的核心职责（使用拖拽后的顺序）
            state.responsibilities.forEach((resp, index) => {
                sortedData[`核心职责${index + 1}`] = resp;
            });

            // 添加排序后的核心技能（使用拖拽后的顺序）
            if (state.skills.length > 0) {
                sortedData['核心技能'] = state.skills.map((skill, index) => `${index + 1}. ${skill}`).join('\n');
            }

            // 添加排序后的拓展技能（使用拖拽后的顺序）
            if (state.prioritySkills.length > 0) {
                sortedData['拓展技能'] = state.prioritySkills.map((skill, index) => `${index + 1}. ${skill}`).join('\n');
            }

            // 添加排序后的加分项（使用拖拽后的顺序）
            if (state.bonusSkills.length > 0) {
                sortedData['加分项'] = state.bonusSkills.map((skill, index) => `${index + 1}. ${skill}`).join('\n');
            }
        } else {
            summaryData = elements.summaryContent.textContent || elements.summaryContent.innerText;
        }

        console.log('发送的总结数据:', summaryData);
        console.log('发送的排序数据:', sortedData);

        showLoading(true, '正在生成JD，请稍候...');

        try {
            const requestBody = {
                company_description: generateCompanyDescription() + '\n\n岗位基本情况：' + state.jobDescription,
                summary_data: summaryData
            };

            // 如果有基本信息，添加到请求中
            if (state.basicInfo) {
                requestBody.basic_info = state.basicInfo;
            }

            // 如果有排序数据，添加到请求中
            if (sortedData) {
                requestBody.sorted_data = sortedData;
            }

            const response = await fetch('/api/v1/generate_jd', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(requestBody)
            });

            const data = await response.json();

            if (data.success) {
                showJDModificationScreen(data.jd_content);
                showNotification('JD生成成功！', 'success');
            } else {
                showNotification('生成JD失败: ' + data.message, 'error');
            }
        } catch (error) {
            console.error('Error:', error);
            showNotification('网络错误，请重试', 'error');
        } finally {
            showLoading(false);
        }
    }

    // 显示JD修改界面
    function showJDModificationScreen(jdContent) {
        switchScreen('jdModificationScreen');
        elements.jdPreviewContent.value = jdContent;

        // 复制对话历史
        elements.modificationConversationArea.innerHTML = '';
        state.conversationHistory.forEach(msg => {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${msg.type}`;
            messageDiv.textContent = msg.content;
            elements.modificationConversationArea.appendChild(messageDiv);
        });

        // 更新Markdown预览
        updateMarkdownPreview();
        toggleJDPreviewMode('markdown');

        updateProgress(8);
    }

    // 修改JD
    async function modifyJD() {
        const modificationRequest = elements.modificationInput.value.trim();
        if (!modificationRequest) {
            showNotification('请输入修改要求', 'error');
            return;
        }

        const originalJD = elements.jdPreviewContent.value;

        showLoading(true, '正在修改JD，请稍候...');

        try {
            const response = await fetch('/api/v1/modify_jd', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    original_jd: originalJD,
                    modification_request: modificationRequest,
                    conversation_history: state.conversationHistory
                })
            });

            const data = await response.json();

            if (data.success) {
                elements.jdPreviewContent.value = data.modified_jd;
                elements.modificationInput.value = '';

                // 添加修改记录
                const userMsg = document.createElement('div');
                userMsg.className = 'message user';
                userMsg.textContent = `修改要求: ${modificationRequest}`;
                elements.modificationConversationArea.appendChild(userMsg);

                const systemMsg = document.createElement('div');
                systemMsg.className = 'message system';
                systemMsg.textContent = 'JD已根据您的要求进行修改。';
                elements.modificationConversationArea.appendChild(systemMsg);

                elements.modificationConversationArea.scrollTop = elements.modificationConversationArea.scrollHeight;

                showNotification('JD修改成功！', 'success');
            } else {
                showNotification('修改JD失败: ' + data.message, 'error');
            }
        } catch (error) {
            console.error('Error:', error);
            showNotification('网络错误，请重试', 'error');
        } finally {
            showLoading(false);
        }
    }

    // 保存JD
    async function saveJD() {
        const jdContent = elements.jdPreviewContent.value.trim();
        if (!jdContent) {
            showNotification('JD内容不能为空', 'error');
            return;
        }

        showLoading(true, '正在保存JD...');

        try {
            const positionNameMatch = jdContent.match(/^#*\s*(.*?)(?:\n|$)/);
            const positionName = positionNameMatch ? positionNameMatch[1].trim() : "未知岗位";
            const timestamp = new Date().toISOString().replace(/[-:]/g, '').replace('T', '_').substring(0, 15);
            const filename = `${positionName}_${timestamp}.md`;

            const response = await fetch('/save_jd', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    content: jdContent,
                    filename: filename
                })
            });

            const data = await response.json();

            if (data.success) {
                showNotification('JD保存成功！', 'success');
            } else {
                showNotification('保存失败: ' + data.message, 'error');
            }
        } catch (error) {
            console.error('Error:', error);
            showNotification('保存失败，请重试', 'error');
        } finally {
            showLoading(false);
        }
    }

    // 复制JD到剪贴板
    function copyJDToClipboard() {
        const jdContent = elements.jdPreviewContent.value;
        navigator.clipboard.writeText(jdContent)
            .then(() => {
                showNotification('JD已复制到剪贴板', 'success');
            })
            .catch(() => {
                showNotification('复制失败，请手动复制', 'error');
            });
    }

    // 开始评估简历
    function startResumeEvaluation() {
        // 获取当前JD内容
        const jdContent = elements.jdPreviewContent.value.trim();
        if (!jdContent) {
            showNotification('JD内容不能为空，请先生成JD', 'error');
            return;
        }

        // 显示确认对话框
        const confirmed = confirm('即将跳转到简历评估系统（端口8008），是否继续？\n\n注意：请确保简历评估系统已在8008端口运行。');
        if (!confirmed) {
            return;
        }

        try {
            // 构建简历评估系统的URL
            const resumeEvaluationUrl = 'http://localhost:8008';

            // 在新窗口中打开简历评估系统
            window.open(resumeEvaluationUrl, '_blank');

            showNotification('已打开简历评估系统，请在新窗口中操作', 'success');

            // 可选：也可以在当前窗口跳转
            // window.location.href = resumeEvaluationUrl;

        } catch (error) {
            console.error('跳转失败:', error);
            showNotification('跳转失败，请手动访问 http://localhost:8008', 'error');
        }
    }

    // 重新开始
    async function restart() {
        // 重置服务器端会话
        try {
            await fetch('/api/v1/reset_session', {method: 'POST'});
        } catch (error) {
            console.error('重置会话失败:', error);
        }

        // 重置状态
        state.currentRound = 0;
        state.selectedOptions = {};
        state.customInputs = {};
        state.conversationHistory = [];
        state.positionName = '';
        state.positionAdvice = '';
        state.basicInfo = null; // 重置基本信息
        state.isSupplementQuestion = false;

        // 重置界面
        if (state.companyInfo) {
            switchScreen('jobConsultScreen');
        } else {
            switchScreen('companyConfigScreen');
        }
        elements.conversationArea.innerHTML = '';
        elements.optionsArea.innerHTML = '';
        elements.userInput.value = '';
        elements.jobDescription.value = '';
        elements.positionInput.value = '';
        elements.positionAdviceArea.style.display = 'none';
        elements.positionAdviceContent.textContent = '';

        // 重置基本信息表单
        if (elements.salaryRange) elements.salaryRange.value = '';
        if (elements.jobType) elements.jobType.value = '';
        if (elements.educationLevel) elements.educationLevel.value = '';
        if (elements.ageRange) elements.ageRange.value = '';
        if (elements.workExperience) elements.workExperience.value = '';
        if (elements.workLocation) elements.workLocation.value = '';
        if (elements.workMode) elements.workMode.value = '';
        if (elements.majorRequirement) elements.majorRequirement.value = '';

        // 重置进度
        updateProgress(0);
    }

    // 重置会话
    async function resetSession() {
        try {
            await fetch('/api/v1/reset_session', {method: 'POST'});
            // 重置前端状态
            state.currentRound = 0;
            state.selectedOptions = {};
            state.customInputs = {};
            state.conversationHistory = [];
            state.isSupplementQuestion = false;
            // 注意：不重置基本信息，因为基本信息是用户主动配置的
            elements.conversationArea.innerHTML = '';
            elements.optionsArea.innerHTML = '';
            elements.userInput.value = '';
            elements.userInput.placeholder = "请输入您的回答...（输入'生成JD'或'结束'可完成对话）";
        } catch (error) {
            console.error('重置会话失败:', error);
        }
    }

    // 返回对话
    function goBackToConversation() {
        switchScreen('conversationScreen');
    }

    // 简单的Markdown解析器
    function parseMarkdown(text) {
        if (!text) return '';

        // 处理标题
        text = text.replace(/^# (.*?)$/gm, '<h1>$1</h1>');
        text = text.replace(/^## (.*?)$/gm, '<h2>$1</h2>');
        text = text.replace(/^### (.*?)$/gm, '<h3>$1</h3>');
        text = text.replace(/^#### (.*?)$/gm, '<h4>$1</h4>');

        // 处理粗体和斜体
        text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        text = text.replace(/\*(.*?)\*/g, '<em>$1</em>');

        // 处理列表
        text = text.replace(/^\d+\. (.*?)$/gm, '<li>$1</li>');
        text = text.replace(/^- (.*?)$/gm, '<li>$1</li>');

        // 将连续的li元素包装在ul或ol中
        let inList = false;
        let listType = '';
        const lines = text.split('\n');
        const processedLines = [];

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];

            if (line.startsWith('<li>')) {
                if (!inList) {
                    // 检查是否为有序列表
                    const isOrderedList = i > 0 && /^\d+\. /.test(lines[i - 1]);
                    listType = isOrderedList ? 'ol' : 'ul';
                    processedLines.push(`<${listType}>`);
                    inList = true;
                }
                processedLines.push(line);
            } else {
                if (inList) {
                    processedLines.push(`</${listType}>`);
                    inList = false;
                }
                processedLines.push(line);
            }
        }

        if (inList) {
            processedLines.push(`</${listType}>`);
        }

        text = processedLines.join('\n');

        // 处理段落（将连续的文本行合并为一个段落）
        text = text.replace(/^(?!<[hou]|<li|<\/[oul])(.*?)$/gm, '<p>$1</p>');

        // 处理换行
        text = text.replace(/\n\n+/g, '<br>');

        return text;
    }

    // 更新Markdown预览
    function updateMarkdownPreview() {
        const rawContent = elements.jdPreviewContent.value;
        const parsedContent = parseMarkdown(rawContent);
        elements.markdownPreview.innerHTML = parsedContent;
    }

    // 切换JD预览模式
    function toggleJDPreviewMode(mode) {
        if (mode === 'markdown') {
            elements.jdPreviewContent.classList.add('hidden');
            elements.markdownPreview.classList.remove('hidden');
            elements.viewMarkdownBtn.classList.add('active');
            elements.viewRawBtn.classList.remove('active');
            updateMarkdownPreview();
        } else {
            elements.jdPreviewContent.classList.remove('hidden');
            elements.markdownPreview.classList.add('hidden');
            elements.viewMarkdownBtn.classList.remove('active');
            elements.viewRawBtn.classList.add('active');
        }
    }

    // 事件监听器
    document.addEventListener('DOMContentLoaded', function () {
        initParticles();
        initProgressSteps();
        updateProgress(0);

        // 加载公司信息
        loadCompanyInfo();

        // 绑定事件
        elements.saveCompanyInfoBtn.addEventListener('click', saveCompanyInfo);
        elements.skipCompanyConfigBtn.addEventListener('click', skipCompanyConfig);
        elements.editCompanyBtn.addEventListener('click', editCompanyInfo);
        elements.getPositionAdviceBtn.addEventListener('click', getPositionAdvice);
        elements.proceedToJDCreationBtn.addEventListener('click', proceedToJDCreation);
        elements.saveBasicInfoBtn.addEventListener('click', saveBasicInfo);
        elements.skipBasicInfoBtn.addEventListener('click', skipBasicInfo);
        elements.startConversationBtn.addEventListener('click', startConversation);
        elements.sendBtn.addEventListener('click', sendUserInput);
        elements.generateJDBtn.addEventListener('click', function () {
            elements.userInput.value = '生成JD';
            sendUserInput();
        });
        elements.confirmBtn.addEventListener('click', confirmAndGenerateJD);
        elements.backBtn.addEventListener('click', goBackToConversation);
        elements.modifyBtn.addEventListener('click', modifyJD);
        elements.saveJDBtn.addEventListener('click', saveJD);
        elements.finalCopyBtn.addEventListener('click', copyJDToClipboard);
        elements.startResumeEvaluationBtn.addEventListener('click', startResumeEvaluation);
        elements.finalRestartBtn.addEventListener('click', restart);

        // 绑定JD预览模式切换事件
        elements.viewMarkdownBtn.addEventListener('click', () => toggleJDPreviewMode('markdown'));
        elements.viewRawBtn.addEventListener('click', () => toggleJDPreviewMode('raw'));

        // 监听JD内容变化，更新预览
        elements.jdPreviewContent.addEventListener('input', updateMarkdownPreview);

        // 初始化拖拽容器
        initAllDraggableContainers();

        // 键盘事件
        elements.userInput.addEventListener('keypress', function (e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendUserInput();
            }
        });

        elements.modificationInput.addEventListener('keypress', function (e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                modifyJD();
            }
        });

        elements.positionInput.addEventListener('keypress', function (e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                getPositionAdvice();
            }
        });
    });

    // 初始化所有拖拽容器
    function initAllDraggableContainers() {
        const containers = [
            elements.responsibilitiesList,
            elements.skillsList,
            elements.prioritySkillsList,
            elements.bonusSkillsList
        ];

        containers.forEach(container => {
            if (container) {
                initDraggableContainer(container);
            }
        });
    }
</script>
</body>
</html> 