<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title>智能简历评估系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #ffffff;
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px 20px;
            border-radius: 20px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .main-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }

        /* 任务卡片样式 */
        .task-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .task-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            padding: 25px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .task-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 16px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .task-card:hover::before {
            opacity: 1;
        }

        .task-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(102, 126, 234, 0.3);
        }

        .task-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
            position: relative;
            z-index: 2;
        }

        .task-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .task-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .task-status.completed {
            background: rgba(16, 185, 129, 0.8);
        }

        .task-status.running {
            background: rgba(59, 130, 246, 0.8);
        }

        .task-status.starting {
            background: rgba(255, 193, 7, 0.8);
        }

        .task-status.failed {
            background: rgba(239, 68, 68, 0.8);
        }

        .task-info {
            position: relative;
            z-index: 2;
        }

        .task-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .task-meta span {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .task-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-top: 15px;
        }

        .stat-item {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 8px;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            display: block;
        }

        .stat-label {
            font-size: 0.7rem;
            opacity: 0.8;
            margin-top: 2px;
        }

        /* 任务进度条样式 */
        .task-progress {
            margin-top: 15px;
        }

        .progress-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 0.85rem;
            opacity: 0.9;
        }

        .progress-bar-bg {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            height: 8px;
            overflow: hidden;
            position: relative;
        }

        .progress-bar-fill {
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.6));
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s ease;
            position: relative;
        }

        .progress-bar-fill.running {
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
        }

        .progress-bar-fill.completed {
            background: linear-gradient(90deg, #10b981, #059669);
        }

        .progress-bar-fill.failed {
            background: linear-gradient(90deg, #ef4444, #dc2626);
        }

        .progress-bar-fill.starting {
            background: linear-gradient(90deg, #f59e0b, #d97706);
        }

        /* 进度动画效果 */
        .progress-bar-fill.running::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: progressGlow 2s infinite;
        }

        @keyframes progressGlow {
            0% {
                transform: translateX(-100%);
            }
            100% {
                transform: translateX(100%);
            }
        }

        /* 任务详情页面样式 */
        .task-detail-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 20px 20px 0 0;
            margin: -40px -40px 30px -40px;
        }

        .task-detail-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .task-detail-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .detail-meta-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        .detail-meta-label {
            font-size: 0.8rem;
            opacity: 0.8;
            margin-bottom: 5px;
        }

        .detail-meta-value {
            font-size: 1.1rem;
            font-weight: 600;
        }

        /* 候选人卡片增强样式 */
        .candidate-card {
            background: white;
            border-radius: 16px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 1px solid #f1f5f9;
        }

        .candidate-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .candidate-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f1f5f9;
        }

        .candidate-name {
            font-size: 1.3rem;
            font-weight: 700;
            color: #1e293b;
        }

        .candidate-score {
            padding: 8px 16px;
            border-radius: 25px;
            font-weight: 700;
            font-size: 1rem;
        }

        .candidate-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .info-row {
            background: #f8fafc;
            padding: 12px;
            border-radius: 8px;
        }

        .info-label {
            font-size: 0.8rem;
            color: #64748b;
            display: block;
            margin-bottom: 4px;
            font-weight: 500;
        }

        .info-value {
            font-size: 1rem;
            font-weight: 600;
            color: #1e293b;
        }

        .candidate-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn-card {
            flex: 1;
            min-width: 120px;
            padding: 12px 20px;
            border: none;
            border-radius: 10px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .btn-resume {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
        }

        .btn-resume:hover {
            background: linear-gradient(135deg, #2563eb, #1e40af);
            transform: translateY(-2px);
        }

        .btn-report {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }

        .btn-report:hover {
            background: linear-gradient(135deg, #059669, #047857);
            transform: translateY(-2px);
        }

        .btn-detail {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            color: white;
        }

        .btn-detail:hover {
            background: linear-gradient(135deg, #7c3aed, #6d28d9);
            transform: translateY(-2px);
        }

        /* 分数等级样式 */
        .score-excellent {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }

        .score-good {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
        }

        .score-average {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
        }

        .score-poor {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }

        /* 导航按钮样式 */
        .nav-button {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            color: #64748b;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .nav-button:hover {
            background: #f8fafc;
            border-color: #cbd5e1;
            color: #475569;
            transform: translateX(-3px);
        }

        /* 空状态样式 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #64748b;
        }

        .empty-state-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .empty-state-desc {
            font-size: 1rem;
            opacity: 0.8;
        }

        /* 任务列表和任务详情界面的特殊样式 */
        .task-list-section,
        .task-detail-section {
            padding: 40px;
        }

        .section-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .section-header h2 {
            font-size: 2rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 10px;
        }

        .section-header p {
            color: #64748b;
            font-size: 1.1rem;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .task-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .task-detail-meta {
                grid-template-columns: 1fr;
            }

            .candidate-info {
                grid-template-columns: 1fr;
            }

            .candidate-actions {
                flex-direction: column;
            }

            .btn-card {
                min-width: auto;
            }
        }

        /* 滚动条美化 */
        .task-grid::-webkit-scrollbar,
        .candidate-list::-webkit-scrollbar {
            width: 8px;
        }

        .task-grid::-webkit-scrollbar-track,
        .candidate-list::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }

        .task-grid::-webkit-scrollbar-thumb,
        .candidate-list::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }

        .task-grid::-webkit-scrollbar-thumb:hover,
        .candidate-list::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        .content-area {
            padding: 40px;
        }

        .upload-section {
            text-align: center;
        }

        .upload-zone {
            border: 2px dashed #cbd5e1;
            border-radius: 12px;
            padding: 60px 20px;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            cursor: pointer;
            background: #f8fafc;
        }

        .upload-zone:hover {
            border-color: #3b82f6;
            background: #eff6ff;
        }

        .upload-text {
            font-size: 1.2rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .upload-hint {
            color: #64748b;
            font-size: 0.9rem;
        }

        .file-types {
            margin-top: 5px;
            color: #64748b;
            font-size: 0.8rem;
        }

        .file-input {
            display: none;
        }

        .file-list {
            margin-top: 30px;
        }

        .file-item {
            display: flex;
            align-items: center;
            padding: 15px;
            background: #f1f5f9;
            border-radius: 8px;
            margin-bottom: 10px;
        }

        .file-info {
            flex: 1;
            margin-left: 15px;
        }

        .file-name {
            font-weight: 500;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .file-remove {
            background: #ef4444;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 0.8rem;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-primary:disabled {
            background: #94a3b8;
            cursor: not-allowed;
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #475569;
        }

        .btn-info {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-info:hover {
            background: #138496;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
        }

        .btn-success {
            background: #10b981;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-success:hover {
            background: #059669;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .btn-warning {
            background: #f59e0b;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-warning:hover {
            background: #d97706;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
        }


        /* 增强的候选人卡片样式 */
        .enhanced-card {
            transition: all 0.3s ease;
            border-radius: 12px;
            overflow: hidden;
        }

        .enhanced-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        /* 可点击的历史记录卡片样式 */
        .clickable-history-card {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .clickable-history-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 32px rgba(59, 130, 246, 0.15);
            border-color: #3b82f6;
        }

        .clickable-history-card:active {
            transform: translateY(-2px);
        }

        .clickable-history-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(147, 51, 234, 0.05));
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .clickable-history-card:hover::before {
            opacity: 1;
        }

        /* 搜索输入框样式增强 */
        #historySearchInput:focus,
        #historyCategoryFilter:focus,
        #historyScoreFilter:focus {
            border-color: #3b82f6;
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .progress-section {
            display: none;
            text-align: center;
        }

        .progress-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 30px;
        }

        .progress-bar-container {
            background: #f1f5f9;
            border-radius: 10px;
            height: 20px;
            margin-bottom: 30px;
            overflow: hidden;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
            border-radius: 10px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .progress-log {
            background: #1e293b;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.5;
            text-align: left;
        }

        .results-section {
            display: none;
        }

        .results-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 30px;
            text-align: center;
        }

        .candidate-list {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }

        .candidate-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .candidate-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .candidate-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .candidate-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: #1e293b;
        }

        .candidate-score {
            font-size: 1.1rem;
            font-weight: 700;
            padding: 6px 12px;
            border-radius: 15px;
            color: white;
        }

        .score-excellent {
            background: #10b981;
        }

        .score-good {
            background: #3b82f6;
        }

        .score-average {
            background: #f59e0b;
        }

        .score-poor {
            background: #ef4444;
        }

        .candidate-info {
            margin-bottom: 15px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .info-label {
            color: #64748b;
        }

        .info-value {
            font-weight: 500;
            color: #1e293b;
        }

        .candidate-actions {
            display: flex;
            gap: 8px;
        }

        .btn-card {
            flex: 1;
            padding: 8px 12px;
            font-size: 0.85rem;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .btn-resume {
            background: #3b82f6;
            color: white;
        }

        .btn-resume:hover {
            background: #2563eb;
        }

        .btn-report {
            background: #10b981;
            color: white;
        }

        .btn-report:hover {
            background: #059669;
        }

        /* 弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 2% auto;
            padding: 0;
            border-radius: 12px;
            width: 95%;
            max-width: none;
            max-height: 95vh;
            height: 95vh;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin: 0;
        }

        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            opacity: 0.7;
        }

        .modal-body {
            padding: 20px;
            max-height: calc(95vh - 100px);
            overflow-y: auto;
            line-height: 1.6;
            height: calc(95vh - 100px);
        }

        .modal-body pre {
            background: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            white-space: pre-wrap;
            word-wrap: break-word;
            font-family: 'Microsoft YaHei', sans-serif;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 8px;
            font-weight: 500;
        }

        .alert-error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }

        .alert-success {
            background: #f0fdf4;
            color: #16a34a;
            border: 1px solid #bbf7d0;
        }

        .alert-info {
            background: #eff6ff;
            color: #2563eb;
            border: 1px solid #bfdbfe;
        }

        @media (max-width: 1024px) {
            .candidate-list {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .content-area {
                padding: 20px;
            }

            .candidate-list {
                grid-template-columns: 1fr;
            }

            .modal-content {
                width: 95%;
                margin: 10% auto;
            }

            .action-buttons {
                flex-direction: column;
            }
        }

        /* 人才库样式 */
        .position-card {
            transition: all 0.3s ease;
        }

        .position-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .candidate-row {
            transition: all 0.3s ease;
        }

        .candidate-row:hover {
            background: #f1f5f9 !important;
            transform: translateX(5px);
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.8rem;
            border-radius: 6px;
        }

        .status-contacted {
            background: #d1fae5 !important;
            color: #065f46 !important;
        }

        .status-pending {
            background: #fef3c7 !important;
            color: #92400e !important;
        }

        .stats-container {
            margin-bottom: 30px;
        }

        .stats-grid {
            gap: 25px;
        }

        .stat-card {
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .filter-container {
            margin-bottom: 30px;
        }

        .positions-container {
            margin-bottom: 30px;
        }

        .candidates-container {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
        }

        .candidates-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .candidate-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .position-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .candidate-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .candidate-actions {
                width: 100%;
                justify-content: space-between;
            }
        }
    </style>
</head>
<body>
<div class="container">
    <div class="header">
        <h1>智能简历评估系统</h1>
        <p>基于AI的三轮筛选简历评估平台</p>
    </div>

    <div class="main-card">
        <div class="content-area">
            <!-- 错误提示区域 -->
            <div id="alertArea"></div>

            <!-- 上传简历部分 -->
            <div class="upload-section" id="uploadSection">
                <div class="upload-zone" id="uploadZone">
                    <div class="upload-text">拖拽文件到此处或点击上传</div>
                    <div class="upload-hint">支持多种格式，最大 10MB</div>
                    <div class="file-types">TXT文件直接评估 | PDF和图片文件会自动提取文本</div>
                    <input accept=".txt,.pdf,.jpg,.jpeg,.png" class="file-input" id="fileInput" multiple type="file">
                </div>

                <!-- JD 选择区域 -->
                <div style="margin: 10px 0 20px; text-align: left;">
                    <div style="font-weight: 600; color:#1e293b; margin-bottom:8px;">上传JD文件 <span
                            style="color:#64748b; font-size:0.9rem;">(支持TXT/MD/JSON)</span></div>
                    <div id="jdFileRow" style="margin-top:10px;">
                        <input accept=".txt,.json,.md" id="jdFileInput" type="file"/>
                        <div style="color:#64748b; font-size:0.85rem; margin-top:5px;">
                            📄 支持格式：TXT、MD、JSON
                        </div>
                    </div>
                    <div id="roundSelectRow" style="margin-top:10px;">
                        <div style="font-weight: 600; color:#1e293b; margin-bottom:8px;">选择筛选轮次</div>
                        <label style="margin-right:12px;"><input checked class="roundOpt" type="checkbox" value="1">
                            初筛(1)</label>
                        <label style="margin-right:12px;"><input checked class="roundOpt" type="checkbox" value="2">
                            画像(2)</label>
                        <label style="margin-right:12px;"><input checked class="roundOpt" type="checkbox" value="2.5">
                            匹配(2.5)</label>
                        <label style="margin-right:12px;"><input class="roundOpt" type="checkbox" value="3">
                            终筛(3)</label>
                    </div>
                </div>

                <div class="file-list" id="fileList"></div>

                <div class="action-buttons">
                    <button class="btn btn-primary" disabled id="startEvaluation">
                        🚀 开始评估
                    </button>
                    <button class="btn btn-secondary" onclick="showTaskList()">
                        📋 任务管理
                    </button>
                    <button class="btn btn-secondary" onclick="showHistoryList()">
                        📚 历史记录
                    </button>
                    <button class="btn btn-secondary" onclick="showTalentPool()">
                        👥 人才库
                    </button>
                    <button class="btn btn-secondary" id="clearFiles">
                        🗑️ 清空文件
                    </button>
                </div>
            </div>

            <!-- 评估进度部分 -->
            <div class="progress-section" id="progressSection">
                <div class="progress-title">正在评估简历...</div>
                <div class="progress-bar-container">
                    <div class="progress-bar" id="progressBar"></div>
                </div>
                <div class="progress-log" id="progressLog"></div>
                <div class="action-buttons">
                    <button class="btn btn-secondary" id="cancelEvaluation">
                        ⏹️ 取消评估
                    </button>
                </div>
            </div>

            <!-- 结果展示部分 -->
            <div class="results-section" id="resultsSection">
                <div class="results-title">评估结果</div>
                <div class="candidate-list" id="candidateList"></div>
                <div class="action-buttons">
                    <button class="btn btn-primary" id="exportResults">
                        📊 导出结果
                    </button>
                    <button class="btn btn-secondary" onclick="showTaskList()">
                        📋 查看所有任务
                    </button>
                    <button class="btn btn-secondary" onclick="showHistoryList()">
                        📚 历史记录
                    </button>
                    <button class="btn btn-secondary" onclick="showTalentPool()">
                        👥 人才库
                    </button>
                    <button class="btn btn-secondary" id="newEvaluation">
                        🔄 新建评估
                    </button>
                </div>
            </div>

            <!-- 多任务进度区域 -->
            <div class="results-section" id="tasksSection" style="display:block;">
                <div class="results-title">进行中的与已完成的评估任务</div>
                <div class="candidate-list" id="tasksContainer"></div>
            </div>

            <!-- 任务列表界面 -->
            <div class="task-list-section" id="taskListSection" style="display: none;">
                <div class="section-header">
                    <h2>评估任务管理</h2>
                    <p>点击任务卡片查看详细结果</p>
                </div>

                <div class="task-grid" id="taskGrid">
                    <!-- 任务卡片将在这里动态生成 -->
                </div>

                <!-- 空状态 -->
                <div class="empty-state" id="emptyTaskState">
                    <div class="empty-state-icon">📋</div>
                    <div class="empty-state-title">暂无评估任务</div>
                    <div class="empty-state-desc">开始上传简历文件来创建新的评估任务</div>
                </div>

                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="showSection('uploadSection')">
                        ➕ 开始新评估
                    </button>
                    <button class="btn btn-secondary" onclick="showHistoryList()">
                        📚 历史记录
                    </button>
                    <button class="btn btn-secondary" onclick="showTalentPool()">
                        👥 人才库
                    </button>
                </div>
            </div>

            <!-- 任务详情界面 -->
            <div class="task-detail-section" id="taskDetailSection" style="display: none;">
                <a class="nav-button" href="#" onclick="showTaskList()">
                    ← 返回任务列表
                </a>

                <div class="task-detail-header" id="taskDetailHeader">
                    <!-- 任务详情头部将动态生成 -->
                </div>

                <div class="candidate-list" id="candidateDetailList">
                    <!-- 候选人详情列表将在这里动态生成 -->
                </div>
            </div>

            <!-- 历史记录列表界面 -->
            <div class="task-list-section" id="historyListSection" style="display: none;">
                <div class="section-header">
                    <h2>评估历史记录</h2>
                    <p>查看过往的评估结果和详细报告</p>
                </div>

                <div class="task-grid" id="historyGrid">
                    <!-- 历史记录卡片将在这里动态生成 -->
                </div>

                <!-- 空状态 -->
                <div class="empty-state" id="emptyHistoryState">
                    <div class="empty-state-icon">📚</div>
                    <div class="empty-state-title">暂无历史记录</div>
                    <div class="empty-state-desc">开始评估简历后，历史记录将在这里显示</div>
                </div>

                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="showSection('uploadSection')">
                        ➕ 开始新评估
                    </button>
                    <button class="btn btn-secondary" onclick="showTaskList()">
                        📋 任务管理
                    </button>
                    <button class="btn btn-secondary" onclick="showTalentPool()">
                        👥 人才库
                    </button>
                    <button class="btn btn-info" onclick="debugHistoryAPI()">
                        🔧 调试历史记录
                    </button>
                    <button class="btn btn-secondary" onclick="testHistoryDisplay()">
                        🧪 测试显示功能
                    </button>
                    <button class="btn btn-success" onclick="testHistoryClickFunction()">
                        🎯 测试点击功能
                    </button>
                    <button class="btn btn-warning" onclick="simulateHistoryClick()">
                        📄 直接跳转演示
                    </button>
                </div>
            </div>

            <!-- 历史记录详情界面 -->
            <div class="task-detail-section" id="historyDetailSection" style="display: none;">
                <a class="nav-button" href="#" onclick="showSection('uploadSection')">
                    ← 返回首页
                </a>

                <div class="task-detail-header" id="historyDetailHeader">
                    <!-- 历史记录详情头部将动态生成 -->
                </div>

                <!-- 搜索和过滤控件 -->
                <div style="background: white; padding: 20px; margin-bottom: 20px; border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <div style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
                        <div style="flex: 1; min-width: 200px;">
                            <input id="historySearchInput" oninput="filterHistoryResults()" placeholder="🔍 搜索候选人姓名..."
                                   style="width: 100%; padding: 10px 15px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px; transition: border-color 0.3s;"
                                   type="text"/>
                        </div>
                        <div>
                            <select id="historyCategoryFilter" onchange="filterHistoryResults()"
                                    style="padding: 10px 15px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px; background: white;">
                                <option value="">全部分类</option>
                                <option value="最终通过">✅ 最终通过</option>
                                <option value="最终不合格">❌ 最终不合格</option>
                                <option value="二筛合格">🟡 二筛合格</option>
                                <option value="二筛不合格">🔴 二筛不合格</option>
                                <option value="初筛合格">🟢 初筛合格</option>
                                <option value="初筛不合格">⚫ 初筛不合格</option>
                                <option value="画像分析">🔍 画像分析</option>
                            </select>
                        </div>
                        <div>
                            <select id="historyScoreFilter" onchange="filterHistoryResults()"
                                    style="padding: 10px 15px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px; background: white;">
                                <option value="">全部分数</option>
                                <option value="high">60分以上</option>
                                <option value="medium">40-59分</option>
                                <option value="low">40分以下</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="history-categories" id="historyCategoriesContainer">
                    <!-- 历史记录分类将在这里动态生成 -->
                </div>
            </div>
        </div>
    </div>
</div>

    <!-- 人才库界面 -->
    <div class="task-list-section" id="talentPoolSection" style="display: none;">
        <div class="section-header">
            <h2>👥 人才库</h2>
            <p>管理通过评估的候选人信息，按职位分类存储</p>
        </div>

        <!-- 统计信息 -->
        <div class="stats-container" style="background: white; padding: 20px; margin-bottom: 20px; border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
            <div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                <div class="stat-card" style="text-align: center; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 12px;">
                    <div class="stat-number" id="totalCandidates" style="font-size: 2rem; font-weight: bold;">0</div>
                    <div class="stat-label">总候选人</div>
                </div>
                <div class="stat-card" style="text-align: center; padding: 20px; background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; border-radius: 12px;">
                    <div class="stat-number" id="contactedCandidates" style="font-size: 2rem; font-weight: bold;">0</div>
                    <div class="stat-label">已联系</div>
                </div>
                <div class="stat-card" style="text-align: center; padding: 20px; background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; border-radius: 12px;">
                    <div class="stat-number" id="pendingCandidates" style="font-size: 2rem; font-weight: bold;">0</div>
                    <div class="stat-label">待联系</div>
                </div>
                <div class="stat-card" style="text-align: center; padding: 20px; background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); color: white; border-radius: 12px;">
                    <div class="stat-number" id="totalPositions" style="font-size: 2rem; font-weight: bold;">0</div>
                    <div class="stat-label">职位数量</div>
                </div>
            </div>
        </div>

        <!-- 筛选条件 -->
        <div class="filter-container" style="background: white; padding: 20px; margin-bottom: 20px; border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
            <div style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
                <div style="flex: 1; min-width: 200px;">
                    <select id="positionFilter" onchange="filterTalentPool()" style="width: 100%; padding: 10px 15px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px; background: white;">
                        <option value="">全部职位</option>
                    </select>
                </div>
                <div>
                    <select id="contactFilter" onchange="filterTalentPool()" style="padding: 10px 15px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px; background: white;">
                        <option value="">全部状态</option>
                        <option value="contacted">已联系</option>
                        <option value="pending">待联系</option>
                    </select>
                </div>
                <div>
                    <input id="talentSearchInput" oninput="filterTalentPool()" placeholder="🔍 搜索候选人姓名..." style="padding: 10px 15px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px; width: 200px;">
                </div>
            </div>
        </div>

        <!-- 职位列表 -->
        <div class="positions-container" id="positionsContainer">
            <!-- 职位卡片将在这里动态生成 -->
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyTalentPoolState" style="display: none;">
            <div class="empty-state-icon">👥</div>
            <div class="empty-state-title">暂无人才信息</div>
            <div class="stat-desc">开始评估简历后，通过评估的候选人将自动添加到人才库</div>
        </div>

        <div class="action-buttons">
            <button class="btn btn-primary" onclick="showSection('uploadSection')">
                ➕ 开始新评估
            </button>
            <button class="btn btn-secondary" onclick="showTaskList()">
                📋 任务管理
            </button>
            <button class="btn btn-secondary" onclick="showHistoryList()">
                📚 历史记录
            </button>
            <button class="btn btn-info" onclick="exportTalentPool()">
                📤 导出数据
            </button>
            <button class="btn btn-warning" onclick="importTalentPool()">
                📥 导入数据
            </button>
            <button class="btn btn-danger" onclick="clearTalentPool()">
                🗑️ 清空数据
            </button>
        </div>
    </div>

<!-- 弹窗模态框 -->
<div class="modal" id="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title" id="modalTitle">标题</h2>
            <div class="modal-controls">
                <button class="btn btn-secondary" id="copyContentBtn" title="复制内容">📋</button>
                <button class="btn btn-secondary" id="downloadContentBtn" title="下载报告">💾</button>
                <span class="close" id="closeModal">&times;</span>
            </div>
        </div>
        <div class="modal-body" id="modalBody">
            内容加载中...
        </div>
    </div>
</div>

<script>
    // 全局变量
    let uploadedFiles = [];
    let isEvaluating = false; // 保留变量但不再阻塞新任务
    const API_BASE_URL = 'http://localhost:8008';

    // DOM元素
    const uploadZone = document.getElementById('uploadZone');
    const fileInput = document.getElementById('fileInput');
    const fileList = document.getElementById('fileList');
    const startEvaluation = document.getElementById('startEvaluation');
    const clearFiles = document.getElementById('clearFiles');
    const uploadSection = document.getElementById('uploadSection');
    const progressSection = document.getElementById('progressSection');
    const resultsSection = document.getElementById('resultsSection');
    const progressBar = document.getElementById('progressBar');
    const progressLog = document.getElementById('progressLog');
    const candidateList = document.getElementById('candidateList');
    const alertArea = document.getElementById('alertArea');
    const modal = document.getElementById('modal');
    const modalTitle = document.getElementById('modalTitle');
    const modalBody = document.getElementById('modalBody');
    const closeModal = document.getElementById('closeModal');
    const copyContentBtn = document.getElementById('copyContentBtn');
    const downloadContentBtn = document.getElementById('downloadContentBtn');
    // JD 选择元素
    const jdPathRow = document.getElementById('jdPathRow');
    const jdFileRow = document.getElementById('jdFileRow');
    const jdPathInput = document.getElementById('jdPathInput');
    const jdFileInput = document.getElementById('jdFileInput');
    // 多任务
    const tasksContainer = document.getElementById('tasksContainer');
    const tasks = {}; // taskId -> {intervalId, cardEl}

    // 初始化
    document.addEventListener('DOMContentLoaded', function () {
        initializeEventListeners();
        // 初始化JD源选择UI显示
        onJDSourceChange();
        // 从localStorage恢复JD路径
        restoreJDPath();
        checkBackendStatus();
    });

    // 检查后端状态
    async function checkBackendStatus() {
        try {
            const response = await fetch(API_BASE_URL + '/status');
            if (response.ok) {
                showAlert('后端服务连接正常', 'success');
            } else {
                showAlert('后端服务连接失败，请确保服务在端口 8004 运行', 'error');
            }
        } catch (error) {
            showAlert('无法连接到后端服务，请确保服务在端口 8004 运行', 'error');
        }
    }

    // 显示提示信息
    function showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-' + type;
        alertDiv.textContent = message;

        alertArea.innerHTML = '';
        alertArea.appendChild(alertDiv);

        if (type === 'success' || type === 'info') {
            setTimeout(() => {
                alertDiv.remove();
            }, 3000);
        }
    }

    // 事件监听器
    function initializeEventListeners() {
        uploadZone.addEventListener('click', () => fileInput.click());
        uploadZone.addEventListener('dragover', handleDragOver);
        uploadZone.addEventListener('dragleave', handleDragLeave);
        uploadZone.addEventListener('drop', handleDrop);
        fileInput.addEventListener('change', handleFileSelect);

        startEvaluation.addEventListener('click', startEvaluationProcess);
        // 回车键也触发开始评估，避免按钮失焦无响应
        document.addEventListener('keydown', (e) => {
            if ((e.key === 'Enter' || e.keyCode === 13) && document.activeElement && document.activeElement.tagName !== 'TEXTAREA') {
                if (!startEvaluation.disabled) startEvaluationProcess();
            }
        });
        clearFiles.addEventListener('click', clearAllFiles);
        document.getElementById('cancelEvaluation').addEventListener('click', cancelEvaluation);
        document.getElementById('newEvaluation').addEventListener('click', newEvaluation);
        document.getElementById('exportResults').addEventListener('click', exportResults);

        // 弹窗事件
        closeModal.addEventListener('click', closeModalWindow);
        copyContentBtn.addEventListener('click', copyModalContent);
        downloadContentBtn.addEventListener('click', downloadModalContent);
        window.addEventListener('click', function (event) {
            if (event.target === modal) {
                closeModalWindow();
            }
        });

        // 仅上传JD，不再需要路径切换与保存
    }

    // 不再需要 JD 来源切换

    // 拖拽处理
    function handleDragOver(e) {
        e.preventDefault();
        uploadZone.style.borderColor = '#3b82f6';
        uploadZone.style.background = '#eff6ff';
    }

    function handleDragLeave(e) {
        e.preventDefault();
        uploadZone.style.borderColor = '#cbd5e1';
        uploadZone.style.background = '#f8fafc';
    }

    function handleDrop(e) {
        e.preventDefault();
        uploadZone.style.borderColor = '#cbd5e1';
        uploadZone.style.background = '#f8fafc';
        const files = Array.from(e.dataTransfer.files);
        addFiles(files);
    }

    // 文件选择处理
    function handleFileSelect(e) {
        const files = Array.from(e.target.files);
        addFiles(files);
        // 重置input，允许选择相同文件名再次触发change
        try {
            e.target.value = '';
        } catch (err) {
        }
    }

    // 添加文件
    function addFiles(files) {
        const validFiles = files.filter(file => {
            // 检查文件类型
            const fileName = file.name.toLowerCase();
            const isValidType = file.type === 'text/plain' ||
                fileName.endsWith('.txt') ||
                file.type === 'application/pdf' ||
                fileName.endsWith('.pdf') ||
                file.type.startsWith('image/') ||
                fileName.endsWith('.jpg') ||
                fileName.endsWith('.jpeg') ||
                fileName.endsWith('.png');

            const isValidSize = file.size <= 10 * 1024 * 1024;

            if (!isValidType) {
                showAlert('文件 ' + file.name + ' 格式不支持，支持的格式：TXT、PDF、JPG、JPEG、PNG', 'error');
                return false;
            }
            if (!isValidSize) {
                showAlert('文件 ' + file.name + ' 大小超过10MB限制', 'error');
                return false;
            }
            return true;
        });

        // 允许重新选择同名文件（如果size不同或时间不同），用名称+大小去重不再适用，改用一个随机ID
        validFiles.forEach(file => {
            const withId = new File([file], file.name, {type: file.type, lastModified: Date.now()});
            uploadedFiles.push(withId);
        });

        updateFileList();
        updateStartButton();
    }

    // 更新文件列表显示
    function updateFileList() {
        fileList.innerHTML = '';

        uploadedFiles.forEach((file, index) => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';

            const candidateName = extractCandidateName(file.name);

            // 获取文件类型图标
            let fileIcon = '📄'; // 默认文档图标
            const fileName = file.name.toLowerCase();
            if (fileName.endsWith('.pdf')) {
                fileIcon = '📕'; // PDF图标
            } else if (fileName.endsWith('.jpg') || fileName.endsWith('.jpeg') || fileName.endsWith('.png')) {
                fileIcon = '🖼️'; // 图片图标
            }

            fileItem.innerHTML =
                '<div class="file-info">' +
                '<div class="file-name">' + fileIcon + ' ' + candidateName + '</div>' +
                '<div>' + file.name + ' (' + formatFileSize(file.size) + ')</div>' +
                '</div>' +
                '<button class="file-remove" onclick="removeFile(' + index + ')">删除</button>';

            fileList.appendChild(fileItem);
        });
    }

    // 格式化文件大小
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // 提取候选人姓名
    function extractCandidateName(filename) {
        const nameWithoutExt = filename.replace(/\.[^/.]+$/, '');
        if (nameWithoutExt.includes('_')) {
            return nameWithoutExt.split('_')[0];
        }
        return nameWithoutExt;
    }

    // 删除文件
    function removeFile(index) {
        uploadedFiles.splice(index, 1);
        updateFileList();
        updateStartButton();
    }

    // 清空所有文件
    function clearAllFiles() {
        uploadedFiles = [];
        updateFileList();
        updateStartButton();
        // 清空文件选择控件的值，避免再次选择相同文件无变化
        try {
            fileInput.value = '';
        } catch (err) {
        }
    }

    // 更新开始按钮状态
    function updateStartButton() {
        startEvaluation.disabled = uploadedFiles.length === 0;
    }

    // 开始评估流程
    async function startEvaluationProcess() {
        if (uploadedFiles.length === 0) {
            showAlert('请先上传简历文件', 'error');
            return;
        }

        try {
            // 准备上传文件
            const formData = new FormData();
            // 仅上传JD文件
            if (!jdFileInput.files || jdFileInput.files.length === 0) {
                showAlert('请选择要上传的JD文件', 'error');
                return;
            }
            formData.append('jd_file', jdFileInput.files[0]);
            // 轮次
            const roundOpts = Array.from(document.querySelectorAll('.roundOpt'));
            const selectedRounds = roundOpts.filter(o => o.checked).map(o => o.value);
            if (selectedRounds.length === 0) {
                showAlert('请至少选择一个筛选轮次', 'error');
                return;
            }
            selectedRounds.forEach(r => formData.append('round_select', r));
            uploadedFiles.forEach(file => {
                formData.append('files', file);
                formData.append('candidate_names', extractCandidateName(file.name));
            });

            // 启动新任务（不阻塞后续任务发起）
            const response = await fetch(API_BASE_URL + '/start_task', {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error('API调用失败: ' + response.status);
            }

            const data = await response.json();
            if (!data.success) {
                throw new Error(data.message || '发起任务失败');
            }

            const taskId = data.task_id;
            createAndPollTask(taskId);

            // 清空当前已选文件，便于继续选择下一批
            clearAllFiles();
            // 只清空文件上传输入，保留路径输入
            try {
                if (jdFileInput) jdFileInput.value = '';
            } catch (e) {
            }

            showAlert('已发起新任务，正在评估中...', 'info');
            console.log('任务已启动，任务ID:', taskId);

            // 自动跳转到任务管理页面显示新任务
            setTimeout(() => {
                console.log('跳转到任务管理页面');
                showTaskList();
            }, 1000);

        } catch (error) {
            console.error('评估失败:', error);
            showAlert('评估任务发起失败: ' + error.message, 'error');
        }
    }

    function createAndPollTask(taskId) {
        const card = document.createElement('div');
        card.className = 'candidate-card';
        card.id = 'task-' + taskId;
        card.innerHTML = `
                <div class="candidate-header">
                    <div class="candidate-name">任务 ${taskId.slice(0, 8)}</div>
                    <div class="candidate-score score-good" id="taskStatus-${taskId}">运行中</div>
                </div>
                <div class="candidate-info">
                    <div class="info-row"><span class="info-label">岗位:</span><span class="info-value" id="taskTitle-${taskId}">识别中...</span></div>
                    <div class="info-row"><span class="info-label">进度:</span><span class="info-value" id="taskProgress-${taskId}">0/0</span></div>
                </div>
                <div class="progress-bar-container"><div class="progress-bar" id="taskBar-${taskId}" style="width:0%"></div></div>
                <div class="progress-log" id="taskLog-${taskId}" style="height:200px;"></div>
            `;
        tasksContainer.prepend(card);
        const intervalId = setInterval(() => pollTaskStatus(taskId), 2000);
        tasks[taskId] = {intervalId, cardEl: card};
        // 立即查询一次
        pollTaskStatus(taskId);
    }

    async function pollTaskStatus(taskId) {
        try {
            const res = await fetch(`${API_BASE_URL}/task_status/${taskId}`);
            if (!res.ok) return;
            const data = await res.json();
            if (!data.success) return;
            const t = data.task;
            // 更新UI
            const titleEl = document.getElementById(`taskTitle-${taskId}`);
            const statusEl = document.getElementById(`taskStatus-${taskId}`);
            const progressEl = document.getElementById(`taskProgress-${taskId}`);
            const barEl = document.getElementById(`taskBar-${taskId}`);
            const logEl = document.getElementById(`taskLog-${taskId}`);
            if (titleEl && t.position_title) titleEl.textContent = t.position_title;
            if (progressEl) progressEl.textContent = `${t.completed || 0}/${t.total || 0}`;
            const percent = t.total ? Math.round(((t.completed || 0) / t.total) * 100) : 0;
            if (barEl) barEl.style.width = `${percent}%`;
            if (statusEl) {
                if (t.status === 'completed') {
                    statusEl.textContent = '已完成';
                    statusEl.className = 'candidate-score score-excellent';
                } else if (t.status === 'failed') {
                    statusEl.textContent = '失败';
                    statusEl.className = 'candidate-score score-poor';
                } else {
                    statusEl.textContent = '运行中';
                    statusEl.className = 'candidate-score score-good';
                }
            }
            // 追加明细日志
            if (logEl && Array.isArray(t.details)) {
                logEl.innerHTML = '';
                t.details.forEach(d => {
                    const div = document.createElement('div');
                    if (d.success) {
                        div.style.color = '#10b981';
                        div.textContent = `✓ ${d.candidate_name} 分数: ${d.final_score} 轮次: ${d.final_round} ${d.qualified ? '通过' : '未通过'}`;
                    } else {
                        div.style.color = '#ef4444';
                        div.textContent = `✗ ${d.candidate_name} ${d.message || '失败'}`;
                    }
                    logEl.appendChild(div);
                });
                logEl.scrollTop = logEl.scrollHeight;
            }
            // 完成则停止轮询
            if (t.status === 'completed' || t.status === 'failed') {
                if (tasks[taskId]) {
                    clearInterval(tasks[taskId].intervalId);
                    delete tasks[taskId];
                }
            }
        } catch (e) {
            console.error('查询任务失败', e);
        }
    }

    // 获取最新日志
    async function fetchLatestLogs() {
        try {
            const response = await fetch(API_BASE_URL + '/logs?lines=10');
            if (response.ok) {
                const data = await response.json();
                if (data.logs && data.logs.length > 0) {
                    // 更新进度条
                    updateProgressFromLogs(data.logs);

                    // 添加新日志条目
                    data.logs.forEach(logLine => {
                        // 检查是否包含进度信息
                        if (logLine.includes('进度:')) {
                            addLogEntry(logLine.trim());
                        }
                        // 检查是否包含完成信息
                        else if (logLine.includes('✓ 完成:') || logLine.includes('✗ 失败:')) {
                            addLogEntry(logLine.trim());
                        }
                        // 检查是否包含评估轮次信息
                        else if (logLine.includes('开始第') && logLine.includes('轮评估')) {
                            addLogEntry(logLine.trim());
                        }
                    });
                }
            }
        } catch (error) {
            console.error('获取日志失败:', error);
        }
    }

    // 从日志中更新进度条
    function updateProgressFromLogs(logs) {
        for (let i = logs.length - 1; i >= 0; i--) {
            const logLine = logs[i];
            if (logLine.includes('进度:')) {
                const progressMatch = logLine.match(/进度:\s*(\d+\.?\d*)%/);
                if (progressMatch && progressMatch[1]) {
                    const progressValue = parseFloat(progressMatch[1]);
                    // 确保进度至少为5%，最多为95%（100%留给完成时）
                    const adjustedProgress = Math.max(5, Math.min(95, progressValue));
                    progressBar.style.width = adjustedProgress + '%';
                    return;
                }
            }
        }
    }

    // 此函数已不再使用，由upload_batch_resumes接口直接处理文件
    // 保留此函数仅作为参考
    async function saveFilesToVLDirectory() {
        for (let file of uploadedFiles) {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('candidate_name', extractCandidateName(file.name));

            const response = await fetch(API_BASE_URL + '/save_file_to_vl', {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error('保存文件失败: ' + file.name);
            }
        }
    }

    // 显示结果
    function showResults(results) {
        showSection('resultsSection');
        displayCandidates(results);
    }

    // 显示候选人列表
    function displayCandidates(candidates) {
        candidateList.innerHTML = '';

        // 按分数排序
        candidates.sort((a, b) => b.final_score - a.final_score);

        // 获取当前JD职位名称
        const currentPosition = getCurrentPositionName();

        candidates.forEach(candidate => {
            const candidateCard = document.createElement('div');
            candidateCard.className = 'candidate-card';

            const statusText = candidate.qualified ? '通过' : '未通过';
            const statusColor = candidate.qualified ? '#10b981' : '#ef4444';
            const scoreClass = getScoreClass(candidate.final_score);

            // 如果候选人通过评估，自动添加到人才库
            if (candidate.qualified && currentPosition) {
                addCandidateToTalentPool({
                    id: generateCandidateId(candidate.candidate_name),
                    name: candidate.candidate_name,
                    age: candidate.basic_info?.age || '未知',
                    experience: candidate.basic_info?.experience || '未知',
                    education: candidate.basic_info?.education || '未知'
                }, currentPosition, candidate.final_score);
            }

            candidateCard.innerHTML = `
                    <div class="candidate-header">
                        <div class="candidate-name">${candidate.candidate_name}</div>
                        <div class="candidate-score ${scoreClass}">${candidate.final_score}分</div>
                    </div>
                    <div class="candidate-info">
                        <div class="info-row">
                            <span class="info-label">最终轮次:</span>
                            <span class="info-value">第${candidate.final_round}轮</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">评估状态:</span>
                            <span class="info-value" style="color: ${statusColor}">${statusText}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">评估时间:</span>
                            <span class="info-value">${new Date().toLocaleDateString()}</span>
                        </div>
                    </div>
                    <div class="candidate-actions">
                        <button class="btn-card btn-resume" onclick="viewResume('${candidate.candidate_name}')">
                            📄 简历
                        </button>
                        <button class="btn-card btn-report" onclick="viewReports('${candidate.candidate_name}')">
                            📊 评估结果
                        </button>
                    </div>
                `;

            candidateList.appendChild(candidateCard);
        });

        // 显示人才库提示
        if (candidates.some(c => c.qualified)) {
            showAlert('通过的候选人已自动添加到人才库', 'success');
        }
    }

    // 获取分数等级样式
    function getScoreClass(score) {
        if (score >= 85) return 'score-excellent';
        if (score >= 70) return 'score-good';
        if (score >= 60) return 'score-average';
        return 'score-poor';
    }

    // 查看简历
    async function viewResume(candidateName) {
        try {
            const response = await fetch(`${API_BASE_URL}/get_resume_content/${candidateName}`);
            if (response.ok) {
                const data = await response.json();
                showModal(`${candidateName} - 简历内容`, data.content);
            } else {
                // 如果API不存在，显示文件路径信息
                const content = `简历文件位置：\n\nVL识别结果/${candidateName}_VL识别结果.txt\n\n请手动打开对应路径查看简历文件。`;
                showModal(`${candidateName} - 简历信息`, content);
            }
        } catch (error) {
            const content = `简历文件位置：\n\nVL识别结果/${candidateName}_VL识别结果.txt\n\n请手动打开对应路径查看简历文件。`;
            showModal(`${candidateName} - 简历信息`, content);
        }
    }

    // 查看报告
    async function viewReports(candidateName) {
        // 首先尝试获取候选人的评估状态
        let isQualified = false;
        let finalRound = 3;
        let finalScore = 0;

        // 从当前显示的候选人列表中查找该候选人的状态
        const candidateElements = document.querySelectorAll('.candidate-card');
        for (let element of candidateElements) {
            const nameElement = element.querySelector('.candidate-name');
            if (nameElement && nameElement.textContent === candidateName) {
                const statusElement = element.querySelector('.info-value[style*="color"]');
                if (statusElement && statusElement.textContent === '通过') {
                    isQualified = true;
                }

                // 获取轮次信息
                const roundElement = element.querySelector('.info-row:nth-child(1) .info-value');
                if (roundElement) {
                    const roundMatch = roundElement.textContent.match(/第(\d+)轮/);
                    if (roundMatch) {
                        finalRound = parseInt(roundMatch[1]);
                    }
                }

                // 获取分数信息
                const scoreElement = element.querySelector('.candidate-score');
                if (scoreElement) {
                    const scoreMatch = scoreElement.textContent.match(/(\d+)分/);
                    if (scoreMatch) {
                        finalScore = parseInt(scoreMatch[1]);
                    }
                }

                break;
            }
        }

        try {
            // 尝试从API获取报告内容
            const response = await fetch(`${API_BASE_URL}/get_evaluation_reports/${candidateName}`);
            if (response.ok) {
                const data = await response.json();
                if (data.success && data.reports && data.reports.length > 0) {
                    let content = '';
                    data.reports.forEach((report, index) => {
                        content += `=== 第${report.round}轮评估报告 ===\n\n${report.content}\n\n`;
                    });
                    showModal(`${candidateName} - 评估报告`, content);
                    return;
                }
            }

            // 如果API获取失败，尝试直接读取文件
            // 根据候选人状态和轮次构建可能的文件路径
            let possiblePaths = [];

            // 第三轮的文件名格式可能包含分数
            if (finalRound === 3) {
                if (isQualified) {
                    possiblePaths.push(`筛选结果/最终通过/${finalScore}分_${candidateName}_第3轮_评估报告.md`);
                    possiblePaths.push(`筛选结果/最终通过/通过_${candidateName}_第3轮_评估报告.md`);
                } else {
                    possiblePaths.push(`筛选结果/最终不合格/${finalScore}分_${candidateName}_第3轮_评估报告.md`);
                    possiblePaths.push(`筛选结果/最终不合格/未通过_${candidateName}_第3轮_评估报告.md`);
                }
            }

            // 第一轮和第二轮的文件名格式
            if (finalRound >= 1) {
                possiblePaths.push(`筛选结果/初筛${isQualified ? '合格' : '不合格'}/${isQualified ? '通过' : '未通过'}_${candidateName}_第1轮_评估报告.md`);
            }

            if (finalRound >= 2) {
                possiblePaths.push(`筛选结果/二筛${isQualified ? '合格' : '不合格'}/${isQualified ? '通过' : '未通过'}_${candidateName}_第2轮_评估报告.md`);
            }

            // 显示可能的文件路径
            let content = `${candidateName} 的评估报告可能位于以下位置：\n\n`;
            possiblePaths.forEach(path => {
                content += `${path}\n`;
            });
            content += '\n请手动打开对应路径查看报告文件。';

            // 如果是第三轮，提供更详细的信息
            if (finalRound === 3) {
                content += `\n\n候选人信息：\n- 姓名：${candidateName}\n- 最终得分：${finalScore}分\n- 评估结果：${isQualified ? '通过' : '未通过'}\n`;
            }

            showModal(`${candidateName} - 评估报告`, content);

        } catch (error) {
            console.error('获取评估报告失败:', error);

            // 出错时显示基本信息
            let content = `获取评估报告失败: ${error.message}\n\n`;
            content += `候选人 ${candidateName} 的评估结果：\n`;
            content += `- 最终轮次：第${finalRound}轮\n`;
            content += `- 最终得分：${finalScore}分\n`;
            content += `- 评估结果：${isQualified ? '通过' : '未通过'}\n`;

            showModal(`${candidateName} - 评估报告`, content);
        }
    }

    // 显示弹窗
    function showModal(title, content) {
        modalTitle.textContent = title;

        // 存储当前内容到全局变量，供复制和下载使用
        window.currentModalContent = {
            title: title,
            content: content
        };

        // 格式化 Markdown 内容
        const formattedContent = formatMarkdownContent(content);
        modalBody.innerHTML = `<div class="markdown-content">${formattedContent}</div>`;
        modal.style.display = 'block';
    }

    // 直接渲染富内容（不进行Markdown转换）
    function showModalRaw(title, innerHTML) {
        modalTitle.textContent = title;
        // 由调用方维护 window.currentModalContent
        modalBody.innerHTML = innerHTML;
        modal.style.display = 'block';
    }

    // 格式化 Markdown 内容
    function formatMarkdownContent(content) {
        // 基础的 Markdown 渲染
        let html = content
            // 转义 HTML 特殊字符
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            // 处理标题
            .replace(/^#### (.*$)/gm, '<h4>$1</h4>')
            .replace(/^### (.*$)/gm, '<h3>$1</h3>')
            .replace(/^## (.*$)/gm, '<h2>$1</h2>')
            .replace(/^# (.*$)/gm, '<h1>$1</h1>')
            // 处理粗体
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            // 处理斜体
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            // 处理行内代码
            .replace(/`(.*?)`/g, '<code>$1</code>')
            // 处理链接
            .replace(/\[([^\]]*)\]\(([^)]*)\)/g, '<a href="$2" target="_blank">$1</a>')
            // 处理换行
            .replace(/\n\n/g, '</p><p>')
            .replace(/\n/g, '<br>');

        // 包装段落
        html = '<p>' + html + '</p>';

        // 处理列表
        html = html.replace(/(?:^|\n)[-*+] (.*?)(?=\n[-*+]|\n\n|\n$|$)/g, function (match, item) {
            return '<li>' + item + '</li>';
        });

        // 包装无序列表
        html = html.replace(/(<li>.*?<\/li>)+/gs, '<ul>$&</ul>');

        // 清理多余的段落标签
        html = html.replace(/<p><\/p>/g, '');
        html = html.replace(/<p>(<[hul])/g, '$1');
        html = html.replace(/(<\/[hul]>)<\/p>/g, '$1');

        return html;
    }

    // 关闭弹窗
    function closeModalWindow() {
        modal.style.display = 'none';
    }

    // 复制模态框内容
    async function copyModalContent() {
        if (!window.currentModalContent) {
            showAlert('没有可复制的内容', 'error');
            return;
        }

        try {
            await navigator.clipboard.writeText(window.currentModalContent.content);
            showAlert('内容已复制到剪贴板', 'success');
        } catch (error) {
            console.error('复制失败:', error);
            showAlert('复制失败，请手动复制', 'error');
        }
    }

    // 下载模态框内容
    function downloadModalContent() {
        if (!window.currentModalContent) {
            showAlert('没有可下载的内容', 'error');
            return;
        }

        const {title, content} = window.currentModalContent;
        const fileName = `${title.replace(/[^\w\s-]/g, '')}_评估报告.md`;

        const blob = new Blob([content], {type: 'text/markdown;charset=utf-8'});
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        showAlert('文件已下载', 'success');
    }

    // 显示指定部分
    function showSection(sectionId) {
        const sections = ['uploadSection', 'progressSection', 'resultsSection', 'taskListSection', 'taskDetailSection', 'historyListSection', 'historyDetailSection'];

        sections.forEach(id => {
            const section = document.getElementById(id);
            if (section) {
                if (id === sectionId) {
                    section.style.display = 'block';
                } else {
                    section.style.display = 'none';
                }
            }
        });

        // 隐藏多任务进度区域（除非是uploadSection）
        const tasksSection = document.getElementById('tasksSection');
        if (tasksSection) {
            tasksSection.style.display = sectionId === 'uploadSection' ? 'block' : 'none';
        }
    }

    // 添加日志条目
    function addLogEntry(message) {
        // 检查是否已存在相同内容的日志条目，避免重复
        const existingEntries = Array.from(progressLog.children).map(el => el.textContent);

        // 如果消息已包含时间戳，则直接使用
        let entryText = '';
        if (message.match(/^\[\d{2}:\d{2}:\d{2}\]/)) {
            entryText = message;
        } else {
            const timestamp = new Date().toLocaleTimeString();
            entryText = '[' + timestamp + '] ' + message;
        }

        // 检查是否重复
        if (!existingEntries.some(entry => entry.includes(message))) {
            const logEntry = document.createElement('div');
            logEntry.textContent = entryText;

            // 根据消息类型添加不同的样式
            if (message.includes('✓') || message.includes('完成')) {
                logEntry.style.color = '#10b981'; // 成功消息显示绿色
            } else if (message.includes('✗') || message.includes('失败') || message.includes('错误')) {
                logEntry.style.color = '#ef4444'; // 失败消息显示红色
            } else if (message.includes('进度:')) {
                logEntry.style.color = '#3b82f6'; // 进度消息显示蓝色
            }

            progressLog.appendChild(logEntry);
            progressLog.scrollTop = progressLog.scrollHeight;
        }
    }

    // 取消评估
    function cancelEvaluation() {
        if (confirm('确定要取消当前评估吗？')) {
            isEvaluating = false;
            showSection('uploadSection');
            updateStartButton();
            addLogEntry('评估已取消');
        }
    }

    // 新建评估
    function newEvaluation() {
        if (confirm('确定要开始新的评估吗？当前结果将被清空。')) {
            uploadedFiles = [];
            isEvaluating = false;
            showSection('uploadSection');
            updateFileList();
            updateStartButton();
            progressBar.style.width = '0%';
            progressLog.innerHTML = '';
        }
    }

    // 任务管理功能
    let allTasks = []; // 存储所有任务数据
    let currentTaskDetail = null; // 当前查看的任务详情
    let taskRefreshInterval = null; // 任务刷新定时器

    // 历史记录管理功能
    let allHistory = []; // 存储所有历史记录数据
    let currentHistoryDetail = null; // 当前查看的历史记录详情

    // 显示任务列表
    function showTaskList() {
        console.log('显示任务列表页面');
        showSection('taskListSection');

        // 先显示加载状态
        const taskGrid = document.getElementById('taskGrid');
        const emptyState = document.getElementById('emptyTaskState');

        if (taskGrid) {
            taskGrid.style.display = 'block';
            taskGrid.innerHTML = '<div style="text-align: center; padding: 40px; color: #64748b;">正在加载任务数据...</div>';
        }
        if (emptyState) {
            emptyState.style.display = 'none';
        }

        loadAllTasks();
        startTaskRefresh();
    }

    // 显示历史记录列表
    function showHistoryList() {
        console.log('🏠 ========== 开始显示历史记录列表页面 ==========');

        // 检查关键DOM元素
        const historyListSection = document.getElementById('historyListSection');
        const historyGrid = document.getElementById('historyGrid');
        const emptyState = document.getElementById('emptyHistoryState');

        console.log('🔍 DOM元素检查:');
        console.log('  - historyListSection:', historyListSection);
        console.log('  - historyGrid:', historyGrid);
        console.log('  - emptyState:', emptyState);

        if (!historyListSection) {
            console.error('❌ 致命错误：找不到 historyListSection 元素');
            showAlert('页面元素未找到，请刷新页面重试', 'error');
            return;
        }

        console.log('🔄 执行页面切换...');
        showSection('historyListSection');

        // 强制确保页面显示
        setTimeout(() => {
            const currentSection = document.getElementById('historyListSection');
            if (currentSection) {
                const styles = window.getComputedStyle(currentSection);
                console.log('📊 页面切换后状态:');
                console.log('  - display:', styles.display);
                console.log('  - visibility:', styles.visibility);
                console.log('  - opacity:', styles.opacity);

                if (styles.display === 'none') {
                    console.error('❌ 页面切换失败，强制显示');
                    currentSection.style.display = 'block';
                }
            }
        }, 100);

        // 先显示加载状态
        if (historyGrid) {
            // 强制显示和重置样式
            historyGrid.style.display = 'block';
            historyGrid.style.visibility = 'visible';
            historyGrid.style.opacity = '1';
            historyGrid.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #64748b; font-size: 1.1rem; border: 2px dashed #e5e7eb; border-radius: 12px; background: #f8fafc;">
                        🔄 正在加载历史记录...<br>
                        <small style="color: #9ca3af; margin-top: 10px; display: block;">如果长时间无响应，请点击调试按钮</small>
                    </div>
                `;
            console.log('📝 设置加载状态显示，HTML长度:', historyGrid.innerHTML.length);
        } else {
            console.error('❌ 严重错误：historyGrid 元素未找到');

            // 尝试创建缺失的元素
            if (historyListSection) {
                console.log('🔧 尝试修复缺失的historyGrid元素...');
                const newGrid = document.createElement('div');
                newGrid.id = 'historyGrid';
                newGrid.className = 'task-grid';
                newGrid.innerHTML = '<div style="text-align: center; padding: 40px; color: #ef4444;">DOM元素缺失，已自动修复，请重新加载</div>';
                historyListSection.appendChild(newGrid);
            }
        }

        if (emptyState) {
            emptyState.style.display = 'none';
            console.log('🙈 隐藏空状态');
        }

        console.log('📡 开始加载历史记录数据...');
        loadAllHistory();
    }

    // 加载所有历史记录
    async function loadAllHistory() {
        console.log('🔄 开始加载历史记录列表...');
        console.log('📡 请求URL:', `${API_BASE_URL}/api/history`);

        try {
            const response = await fetch(`${API_BASE_URL}/api/history`);
            console.log('📊 历史记录API响应状态:', response.status);
            console.log('📊 响应头:', Object.fromEntries(response.headers.entries()));

            if (response.ok) {
                const data = await response.json();
                console.log('📦 历史记录API返回完整数据:', JSON.stringify(data, null, 2));
                console.log('✅ 数据解析成功');

                if (data.success) {
                    allHistory = data.data || [];
                    console.log(`📋 设置 allHistory 数组，长度: ${allHistory.length}`);

                    if (allHistory.length > 0) {
                        console.log('📄 第一条历史记录示例:', JSON.stringify(allHistory[0], null, 2));
                    }

                    console.log('🎨 开始渲染历史记录网格...');
                    renderHistoryGrid();
                } else {
                    console.error('❌ API返回失败状态:', data.message);
                    showAlert('获取历史记录失败: ' + data.message, 'error');
                    renderEmptyHistoryState();
                }
            } else {
                console.error('❌ 历史记录API请求失败:', response.status);
                const errorText = await response.text();
                console.error('❌ 错误响应内容:', errorText);
                showAlert('获取历史记录失败，请检查网络连接', 'error');
                renderEmptyHistoryState();
            }
        } catch (error) {
            console.error('💥 加载历史记录时发生错误:', error);
            console.error('💥 错误堆栈:', error.stack);
            showAlert('加载历史记录失败: ' + error.message, 'error');
            renderEmptyHistoryState();
        }
    }

    // 渲染历史记录网格
    function renderHistoryGrid() {
        console.log('🎨 ========== renderHistoryGrid 函数开始执行 ==========');

        const historyGrid = document.getElementById('historyGrid');
        const emptyState = document.getElementById('emptyHistoryState');

        console.log('🔍 DOM元素检查:');
        console.log('  - historyGrid:', historyGrid);
        console.log('  - emptyState:', emptyState);
        console.log('  - allHistory长度:', allHistory ? allHistory.length : 'undefined');
        console.log('  - allHistory内容:', allHistory);

        if (!historyGrid) {
            console.error('❌ 致命错误：找不到历史记录网格元素 #historyGrid');
            console.log('🔍 列出当前页面所有ID元素:');
            const allElements = document.querySelectorAll('[id]');
            allElements.forEach(el => console.log(`  - ${el.id}: ${el.tagName}`));

            // 显示错误信息在页面上
            showAlert('DOM元素缺失：historyGrid 不存在，请刷新页面', 'error');
            return;
        }

        // 强制确保元素可见
        historyGrid.style.display = 'block';
        historyGrid.style.visibility = 'visible';
        historyGrid.style.opacity = '1';

        console.log(`📊 准备渲染 ${allHistory ? allHistory.length : 0} 个历史记录`);

        if (!allHistory || allHistory.length === 0) {
            console.log('📭 历史记录为空，显示空状态');
            renderEmptyHistoryState();
            return;
        }

        console.log('🧹 清空现有内容并设置显示状态');
        historyGrid.innerHTML = '';

        if (emptyState) {
            emptyState.style.display = 'none';
        }

        console.log('🔄 开始逐个创建历史记录卡片');
        let successCount = 0;
        let errorCount = 0;

        try {
            allHistory.forEach((history, index) => {
                console.log(`📇 创建第 ${index + 1} 个卡片:`, history.folder_name || '未知文件夹');
                console.log(`📇 卡片数据:`, history);

                const historyCard = createHistoryCard(history);
                if (historyCard) {
                    historyGrid.appendChild(historyCard);
                    successCount++;
                    console.log(`✅ 第 ${index + 1} 个卡片创建成功`);
                } else {
                    errorCount++;
                    console.error(`❌ 第 ${index + 1} 个卡片创建失败`);

                    // 添加错误卡片用于调试
                    const errorCard = document.createElement('div');
                    errorCard.className = 'task-card';
                    errorCard.style.cssText = 'border: 2px solid #ef4444; background: #fef2f2;';
                    errorCard.innerHTML = `
                            <div style="padding: 15px; color: #ef4444; text-align: center;">
                                <div>❌ 卡片 ${index + 1} 创建失败</div>
                                <div style="font-size: 12px; margin-top: 5px;">文件夹: ${history.folder_name || '未知'}</div>
                            </div>
                        `;
                    historyGrid.appendChild(errorCard);
                }
            });

            console.log(`🎉 历史记录渲染完成!`);
            console.log(`📊 渲染统计: 成功 ${successCount} 个, 失败 ${errorCount} 个`);
            console.log('📏 historyGrid 最终子元素数量:', historyGrid.children.length);
            console.log('📏 historyGrid HTML长度:', historyGrid.innerHTML.length);

            // 检查最终显示状态
            const finalStyles = window.getComputedStyle(historyGrid);
            console.log('👁️ 最终显示状态:');
            console.log('  - display:', finalStyles.display);
            console.log('  - visibility:', finalStyles.visibility);
            console.log('  - opacity:', finalStyles.opacity);
            console.log('  - height:', finalStyles.height);
            console.log('  - width:', finalStyles.width);

            // 如果渲染成功但看不到，强制设置样式
            if (historyGrid.children.length > 0 && finalStyles.display === 'none') {
                console.log('🔧 强制显示historyGrid');
                historyGrid.style.display = 'grid';
                historyGrid.style.gridTemplateColumns = 'repeat(auto-fill, minmax(400px, 1fr))';
                historyGrid.style.gap = '20px';
            }

            // 添加成功提示
            if (successCount > 0) {
                showAlert(`历史记录加载完成，显示 ${successCount} 条记录`, 'success');
            }

        } catch (error) {
            console.error('💥 渲染历史记录时发生严重错误:', error);
            console.error('💥 错误堆栈:', error.stack);

            // 显示错误信息
            historyGrid.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #ef4444; border: 2px solid #ef4444; border-radius: 12px; background: #fef2f2;">
                        <div style="font-size: 2rem; margin-bottom: 15px;">💥</div>
                        <div style="font-size: 1.2rem; font-weight: bold; margin-bottom: 10px;">渲染失败</div>
                        <div style="font-size: 0.9rem; color: #7f1d1d;">${error.message}</div>
                        <button onclick="debugHistoryAPI()" style="margin-top: 15px; background: #ef4444; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer;">🔧 运行调试</button>
                    </div>
                `;
            showAlert('历史记录渲染失败: ' + error.message, 'error');
        }
    }

    // 创建历史记录卡片
    function createHistoryCard(history) {
        console.log('🎨 创建历史记录卡片:', history);

        try {
            const card = document.createElement('div');
            card.className = 'task-card clickable-history-card';
            card.style.cursor = 'pointer';
            card.setAttribute('data-folder', history.folder_name || '');
            card.onclick = () => {
                console.log('🖱️ 点击历史记录卡片:', history.folder_name);
                showHistoryDetail(history);
            };

            const formatDate = (dateStr, timeStr) => {
                try {
                    // 处理日期格式 8.15 -> 2024-08-15
                    const currentYear = new Date().getFullYear();
                    const [month, day] = dateStr.split('.');
                    const fullDate = `${currentYear}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;

                    // 处理时间格式 13：14 -> 13:14
                    const formattedTime = timeStr.replace('：', ':');

                    return `${fullDate} ${formattedTime}`;
                } catch (e) {
                    console.warn('日期格式化失败:', e, 'dateStr:', dateStr, 'timeStr:', timeStr);
                    return `${dateStr} ${timeStr}`;
                }
            };

            const totalCandidates = history.total_candidates || 0;
            const dateTimeStr = formatDate(history.date || '未知', history.time || '未知');

            // 计算各分类统计
            const stats = history.stats || {};
            const passedCount = stats['最终通过'] || 0;
            const failedCount = (stats['最终不合格'] || 0) + (stats['初筛不合格'] || 0) + (stats['二筛不合格'] || 0);

            console.log('📊 卡片数据:');
            console.log('  - position_info:', history.position_info);
            console.log('  - folder_name:', history.folder_name);
            console.log('  - total_candidates:', totalCandidates);
            console.log('  - passedCount:', passedCount);
            console.log('  - failedCount:', failedCount);
            console.log('  - dateTimeStr:', dateTimeStr);

            card.innerHTML = `
                    <div class="task-header">
                        <div>
                            <div class="task-title">${history.position_info || '未知岗位'}</div>
                            <div class="task-meta">
                                <span>📅 ${dateTimeStr}</span>
                            </div>
                        </div>
                        <div class="task-status completed">
                            <span>已完成</span>
                            <span style="margin-left: 8px; font-size: 12px; opacity: 0.8;">点击查看详情 👆</span>
                        </div>
                    </div>
                    
                    <div class="task-info">
                        <div class="task-meta">
                            <span>📂 ${history.folder_name || '未知文件夹'}</span>
                        </div>
                        
                        <div class="task-stats">
                            <div class="stat-item">
                                <span class="stat-number">${totalCandidates}</span>
                                <span class="stat-label">总候选人</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number" style="color: #10b981;">${passedCount}</span>
                                <span class="stat-label">最终通过</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number" style="color: #ef4444;">${failedCount}</span>
                                <span class="stat-label">未通过</span>
                            </div>
                        </div>
                        
                        <div style="margin-top: 12px; padding: 8px 12px; background: #f0f9ff; border-radius: 6px; border-left: 3px solid #3b82f6; font-size: 13px; color: #1e40af;">
                            💡 点击此卡片查看详细的评估结果和候选人信息
                        </div>
                    </div>
                `;

            console.log('✅ 历史记录卡片创建成功');
            return card;

        } catch (error) {
            console.error('💥 创建历史记录卡片时发生错误:', error);
            console.error('💥 错误数据:', history);
            console.error('💥 错误堆栈:', error.stack);
            return null;
        }
    }

    // 历史记录列表卡片事件委托，防止内联点击丢失
    (function attachHistoryGridDelegation() {
        const historyGrid = document.getElementById('historyGrid');
        if (!historyGrid) return;
        if (historyGrid._delegationAttached) return;
        historyGrid.addEventListener('click', function (e) {
            const card = e.target.closest('.task-card.clickable-history-card');
            if (!card) return;
            const folder = card.getAttribute('data-folder');
            if (!folder) return;
            const item = (Array.isArray(allHistory) ? allHistory : []).find(h => h.folder_name === folder);
            if (item) {
                showHistoryDetail(item);
            } else {
                // 兜底：仅用 folderName 加载详情
                showSection('historyDetailSection');
                updateHistoryDetailHeader({
                    position_info: folder,
                    date: '',
                    time: '',
                    folder_name: folder,
                    total_candidates: 0,
                    stats: {}
                });
                loadHistoryDetail(folder);
            }
        });
        historyGrid._delegationAttached = true;
    })();

    // 显示历史记录详情 - 直接页面跳转
    async function showHistoryDetail(history) {
        console.log('📄 ========== 直接进入历史记录详情页面 ==========');
        console.log('📁 文件夹:', history.folder_name);
        console.log('💼 岗位:', history.position_info);

        // 1. 设置当前详情数据
        currentHistoryDetail = history;

        // 2. 直接切换到详情页面
        showSection('historyDetailSection');

        // 3. 立即更新头部信息
        updateHistoryDetailHeader(history);

        // 4. 重置过滤器确保显示所有人
        resetHistoryFilters();

        // 5. 立即加载详细内容
        await loadHistoryDetail(history.folder_name);
    }

    // 更新历史记录详情头部 - 简洁版
    function updateHistoryDetailHeader(history) {
        console.log('📋 更新详情页面头部信息...');

        const headerContainer = document.getElementById('historyDetailHeader');
        if (!headerContainer) {
            console.error('❌ historyDetailHeader 元素未找到');
            return;
        }

        const passedCount = history.stats?.['最终通过'] || 0;
        const totalCount = history.total_candidates || 0;
        const passRate = totalCount > 0 ? ((passedCount / totalCount) * 100).toFixed(1) : 0;
        const failedCount = totalCount - passedCount;

        headerContainer.innerHTML = `
                <!-- 返回导航 -->
                <div style="margin-bottom: 20px;">
                    <a href="#" onclick="showSection('uploadSection')" style="color: #3b82f6; text-decoration: none; font-size: 14px; display: inline-flex; align-items: center; gap: 6px;">
                        ← 返回首页
                    </a>
                </div>

                <!-- 页面标题 -->
                <div style="margin-bottom: 30px;">
                    <h1 style="margin: 0; font-size: 2rem; font-weight: bold; color: #1f2937; margin-bottom: 10px;">
                        ${history.position_info || '历史评估记录'}
                    </h1>
                    <div style="display: flex; gap: 20px; align-items: center; color: #6b7280; font-size: 14px;">
                        <span>📅 评估时间：${history.date} ${history.time}</span>
                        <span>📂 批次：${history.folder_name}</span>
                        <span>👥 总数：${totalCount}人</span>
                        <span style="color: #10b981; font-weight: 600;">✅ 通过：${passedCount}人 (${passRate}%)</span>
                    </div>
                </div>
            `;
    }

    // 导出历史记录详情
    function exportHistoryDetail() {
        if (!currentHistoryDetail) {
            showAlert('没有可导出的数据', 'error');
            return;
        }

        showAlert('🚧 导出功能开发中...', 'info');
        console.log('📊 导出历史记录详情:', currentHistoryDetail);
    }

    // 刷新历史记录详情
    function refreshHistoryDetail() {
        if (!currentHistoryDetail) {
            showAlert('没有可刷新的数据', 'error');
            return;
        }

        console.log('🔄 刷新历史记录详情...');
        loadHistoryDetail(currentHistoryDetail.folder_name);
    }

    // 重置历史记录过滤器
    function resetHistoryFilters() {
        console.log('🔄 重置历史记录过滤器，显示所有人...');

        const searchInput = document.getElementById('historySearchInput');
        const categoryFilter = document.getElementById('historyCategoryFilter');
        const scoreFilter = document.getElementById('historyScoreFilter');

        if (searchInput) {
            searchInput.value = '';
            console.log('📝 清空搜索框');
        }

        if (categoryFilter) {
            categoryFilter.value = '';
            console.log('📂 重置分类过滤器');
        }

        if (scoreFilter) {
            scoreFilter.value = '';
            console.log('📊 重置分数过滤器');
        }
    }

    // 加载历史记录详情 - 直接显示
    async function loadHistoryDetail(folderName) {
        console.log('📊 加载历史记录详情:', folderName);

        const categoriesContainer = document.getElementById('historyCategoriesContainer');
        if (!categoriesContainer) {
            console.error('❌ categoriesContainer 元素未找到');
            return;
        }

        try {
            const response = await fetch(`${API_BASE_URL}/api/history/${encodeURIComponent(folderName)}`);

            if (response.ok) {
                const data = await response.json();

                if (data.success) {
                    // 直接渲染候选人结果，不显示额外的提示
                    renderHistoryCategories(data.data);
                } else {
                    categoriesContainer.innerHTML = `
                            <div style="text-align: center; padding: 40px; color: #ef4444;">
                                加载失败: ${data.message}
                            </div>
                        `;
                }
            } else {
                categoriesContainer.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: #ef4444;">
                            网络请求失败 (状态码: ${response.status})
                        </div>
                    `;
            }
        } catch (error) {
            console.error('加载历史记录详情时发生错误:', error);
            categoriesContainer.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #ef4444;">
                        加载失败: ${error.message}
                        <button onclick="loadHistoryDetail('${folderName}')" style="margin-left: 10px; background: #3b82f6; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer;">重试</button>
                    </div>
                `;
        }
    }

    // 存储原始历史数据用于过滤
    let originalHistoryData = null;

    // 渲染历史记录分类
    let historyClickListenerAttached = false;

    function renderHistoryCategories(historyData) {
        console.log('🎨 ========== 渲染历史记录分类 ==========');
        console.log('📊 历史数据:', historyData);

        const categoriesContainer = document.getElementById('historyCategoriesContainer');
        if (!categoriesContainer) return;

        // 存储原始数据
        originalHistoryData = historyData;

        const categories = historyData.categories || {};

        // 统计总数
        let totalCandidates = 0;
        Object.values(categories).forEach(files => {
            totalCandidates += files.length;
        });

        let html = '';

        // 按特定顺序显示分类
        const categoryOrder = ['最终通过', '最终不合格', '二筛合格', '二筛不合格', '初筛合格', '初筛不合格', '画像分析结果'];

        categoryOrder.forEach(orderedCategory => {
            Object.keys(categories).forEach(categoryName => {
                if (categoryName.includes(orderedCategory) || orderedCategory.includes(categoryName)) {
                    const files = categories[categoryName] || [];

                    if (files.length > 0) {
                        // 获取分类对应的颜色和图标
                        let categoryColor = '#667eea';
                        let categoryIcon = '📄';

                        if (categoryName.includes('最终通过')) {
                            categoryColor = '#10b981';
                            categoryIcon = '✅';
                        } else if (categoryName.includes('最终不合格')) {
                            categoryColor = '#ef4444';
                            categoryIcon = '❌';
                        } else if (categoryName.includes('二筛合格')) {
                            categoryColor = '#f59e0b';
                            categoryIcon = '🟡';
                        } else if (categoryName.includes('二筛不合格')) {
                            categoryColor = '#ef4444';
                            categoryIcon = '🔴';
                        } else if (categoryName.includes('初筛合格')) {
                            categoryColor = '#10b981';
                            categoryIcon = '🟢';
                        } else if (categoryName.includes('初筛不合格')) {
                            categoryColor = '#6b7280';
                            categoryIcon = '⚫';
                        } else if (categoryName.includes('画像分析')) {
                            categoryColor = '#3b82f6';
                            categoryIcon = '🔍';
                        }

                        html += `
                                <div class="history-category" data-category="${categoryName}" style="margin-bottom: 30px;">
                                    <div style="background: linear-gradient(135deg, ${categoryColor} 0%, ${categoryColor}dd 100%); color: white; padding: 20px; border-radius: 12px; margin-bottom: 20px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <h3 style="margin: 0; font-size: 1.3rem; font-weight: 600;">
                                                ${categoryIcon} ${categoryName}
                                            </h3>
                                            <div style="display: flex; gap: 15px; align-items: center;">
                                                <span style="background: rgba(255,255,255,0.2); padding: 8px 16px; border-radius: 20px; font-weight: 500;">
                                                    ${files.length} 人
                                                </span>
                                                <span style="background: rgba(255,255,255,0.15); padding: 8px 16px; border-radius: 20px; font-size: 0.9rem;">
                                                    ${((files.length / totalCandidates) * 100).toFixed(1)}%
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="candidate-list" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(400px, 1fr)); gap: 20px;">
                            `;

                        // 按分数排序
                        const sortedFiles = files.sort((a, b) => {
                            const scoreA = parseFloat(a.score.replace('分', ''));
                            const scoreB = parseFloat(b.score.replace('分', ''));
                            return scoreB - scoreA; // 降序
                        });

                        sortedFiles.forEach(file => {
                            html += createHistoryFileCard(file, categoryName, historyData.folder_name);
                        });

                        html += `
                                    </div>
                                </div>
                            `;
                    }
                }
            });
        });

        if (html === '') {
            html = '<div style="text-align: center; padding: 40px; color: #64748b;">该历史记录暂无数据</div>';
        }

        console.log(`✅ 历史记录分类渲染完成，总共 ${totalCandidates} 个候选人`);
        categoriesContainer.innerHTML = html;

        // 事件委托：统一处理候选人卡片点击
        if (!historyClickListenerAttached) {
            categoriesContainer.addEventListener('click', function (e) {
                const card = e.target.closest('.candidate-card.enhanced-card');
                if (!card) return;
                const folder = card.getAttribute('data-folder');
                const category = card.getAttribute('data-category');
                const filename = card.getAttribute('data-filename');
                const candidate = card.getAttribute('data-candidate');
                if (folder && candidate) {
                    // 展示左右分栏（简历 + 勾选的评估结果）
                    showCandidateCombinedReport(folder, candidate || '候选人');
                }
            });
            historyClickListenerAttached = true;
        }

        // 确保在初始渲染后，所有内容都是可见的（没有过滤）
        setTimeout(() => {
            console.log('🔍 验证所有分类是否正确显示...');
            const categoryElements = categoriesContainer.querySelectorAll('.history-category');
            console.log(`📊 找到 ${categoryElements.length} 个分类元素`);

            categoryElements.forEach((element, index) => {
                const categoryName = element.getAttribute('data-category');
                const isVisible = element.style.display !== 'none';
                console.log(`  ${index + 1}. ${categoryName}: ${isVisible ? '✅ 可见' : '❌ 隐藏'}`);
            });
        }, 100);
    }

    // 过滤历史记录结果
    function filterHistoryResults() {
        if (!originalHistoryData) return;

        const searchTerm = document.getElementById('historySearchInput').value.toLowerCase();
        const categoryFilter = document.getElementById('historyCategoryFilter').value;
        const scoreFilter = document.getElementById('historyScoreFilter').value;

        const categories = originalHistoryData.categories || {};
        const filteredCategories = {};

        Object.keys(categories).forEach(categoryName => {
            // 应用分类过滤
            if (categoryFilter && !categoryName.includes(categoryFilter)) {
                return;
            }

            const files = categories[categoryName] || [];
            const filteredFiles = files.filter(file => {
                // 应用姓名搜索
                if (searchTerm && !file.candidate_name.toLowerCase().includes(searchTerm)) {
                    return false;
                }

                // 应用分数过滤
                if (scoreFilter) {
                    const score = parseFloat(file.score.replace('分', ''));
                    if (scoreFilter === 'high' && score < 60) return false;
                    if (scoreFilter === 'medium' && (score < 40 || score >= 60)) return false;
                    if (scoreFilter === 'low' && score >= 40) return false;
                }

                return true;
            });

            if (filteredFiles.length > 0) {
                filteredCategories[categoryName] = filteredFiles;
            }
        });

        // 使用过滤后的数据重新渲染
        const filteredData = {
            ...originalHistoryData,
            categories: filteredCategories
        };

        renderHistoryCategories(filteredData);
    }

    // 创建历史记录文件卡片
    function createHistoryFileCard(file, category, folderName) {
        const score = parseFloat(file.score.replace('分', ''));
        const scoreColor = getScoreColor(score);
        const fileSize = formatFileSize(file.file_size);
        const modifiedTime = new Date(file.modified_time * 1000);
        const formatTime = modifiedTime.toLocaleString('zh-CN');

        // 获取状态标签
        let statusBadge = '';
        let statusColor = '';

        if (category.includes('最终通过')) {
            statusBadge = '✅ 最终通过';
            statusColor = '#10b981';
        } else if (category.includes('最终不合格')) {
            statusBadge = '❌ 最终不合格';
            statusColor = '#ef4444';
        } else if (category.includes('二筛合格')) {
            statusBadge = '🟡 二筛合格';
            statusColor = '#f59e0b';
        } else if (category.includes('二筛不合格')) {
            statusBadge = '🔴 二筛不合格';
            statusColor = '#ef4444';
        } else if (category.includes('初筛合格')) {
            statusBadge = '🟢 初筛合格';
            statusColor = '#10b981';
        } else if (category.includes('初筛不合格')) {
            statusBadge = '⚫ 初筛不合格';
            statusColor = '#6b7280';
        } else if (category.includes('画像分析')) {
            statusBadge = '🔍 画像分析';
            statusColor = '#3b82f6';
        } else {
            statusBadge = '📄 评估报告';
            statusColor = '#8b5cf6';
        }

        return `
                <div class="candidate-card enhanced-card" style="cursor: pointer; border-left: 4px solid ${statusColor};" data-folder="${folderName}" data-category="${category}" data-filename="${file.filename}" data-candidate="${file.candidate_name}">
                    <div class="candidate-header">
                        <div class="candidate-info">
                            <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 10px;">
                                <h3 class="candidate-name" style="margin: 0; color: #1f2937; font-size: 1.1rem;">${file.candidate_name}</h3>
                                <span class="status-badge" style="background: ${statusColor}; color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 500;">
                                    ${statusBadge}
                                </span>
                            </div>
                            <div class="candidate-meta" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 8px; margin-bottom: 10px;">
                                <div style="display: flex; align-items: center; gap: 5px;">
                                    <span style="background: ${scoreColor}; color: white; padding: 3px 8px; border-radius: 6px; font-weight: bold; font-size: 0.9rem;">
                                        ${file.score}
                                    </span>
                                    <span style="color: #6b7280; font-size: 0.85rem;">分数</span>
                                </div>
                                <div style="display: flex; align-items: center; gap: 5px;">
                                    <span style="color: #374151; font-size: 0.85rem;">🏆 ${file.round}</span>
                                </div>
                                <div style="display: flex; align-items: center; gap: 5px;">
                                    <span style="color: #6b7280; font-size: 0.8rem;">📏 ${fileSize}</span>
                                </div>
                            </div>
                            <div style="color: #9ca3af; font-size: 0.8rem; display: flex; align-items: center; gap: 5px;">
                                <span>🕒 ${formatTime}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
    }

    // 格式化文件大小
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    // 获取分数对应的颜色
    function getScoreColor(score) {
        if (score >= 60) return '#10b981'; // 绿色
        if (score >= 40) return '#f59e0b'; // 橙色
        return '#ef4444'; // 红色
    }

    // 显示历史记录文件内容
    async function showHistoryFileContent(folderName, category, filename, candidateName) {
        try {
            const response = await fetch(`${API_BASE_URL}/api/history/${encodeURIComponent(folderName)}/${encodeURIComponent(category)}/${encodeURIComponent(filename)}`);

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    const md = data.data.content || '';
                    showSingleMarkdownWithFilters(candidateName, md);
                } else {
                    showAlert('加载文件内容失败: ' + data.message, 'error');
                }
            } else {
                showAlert('加载文件内容失败，请检查网络连接', 'error');
            }
        } catch (error) {
            console.error('加载文件内容时发生错误:', error);
            showAlert('加载文件内容失败: ' + error.message, 'error');
        }
    }

    // 工具函数：标准化轮次
    function normalizeRound(roundInfo) {
        if (typeof roundInfo === 'number') return roundInfo;
        if (!roundInfo) return 3;
        const m = String(roundInfo).match(/第\s*([0-9]+(?:\.[0-9])?)\s*轮/);
        if (m) {
            return m[1].includes('.') ? parseFloat(m[1]) : parseInt(m[1], 10);
        }
        if (/画像/.test(String(roundInfo))) return 2;
        return 3;
    }

    function roundOrder(r) {
        if (r === 1) return 1;
        if (r === 2 || r === 2.5 || (typeof r === 'string' && r.includes('画像'))) return 2;
        return 3;
    }

    // 新：左右分栏显示（左：简历；右：评估报告，支持勾选初/二/三筛，多选纵向排列）
    async function showCandidateCombinedReport(folderName, candidateName) {
        try {
            console.log('🔍 开始加载候选人详情:', {folderName, candidateName});

            // 左侧简历内容（读取简历文件：图片或PDF）
            const resumeResp = await fetch(`${API_BASE_URL}/api/resume_file/${encodeURIComponent(candidateName)}`);
            let resumeFileUrl = '';
            let resumeFileType = '';
            if (resumeResp.ok) {
                const r = await resumeResp.json();
                if (r.success) {
                    resumeFileUrl = r.file_url || '';
                    resumeFileType = r.file_type || 'image';
                    console.log('✅ 简历文件获取成功:', r.filename, '类型:', resumeFileType);
                } else {
                    console.log('⚠️ 简历文件获取失败:', r.message);
                }
            } else {
                console.log('❌ 简历文件接口请求失败:', resumeResp.status);
            }

            // 右侧评估报告（来自当前批次的文件，确保一致性）
            const reportResp = await fetch(`${API_BASE_URL}/api/history/${encodeURIComponent(folderName)}`);
            let reports = [];
            if (reportResp.ok) {
                const dr = await reportResp.json();
                console.log('📊 历史详情响应:', dr);
                if (dr.success && dr.data && dr.data.categories) {
                    const cats = dr.data.categories;
                    const entries = [];
                    Object.keys(cats).forEach(cat => {
                        (cats[cat] || []).forEach(f => {
                            if ((f.candidate_name || '') === candidateName) {
                                entries.push({category: cat, filename: f.filename, round: f.round});
                            }
                        });
                    });
                    console.log('📋 找到的文件条目:', entries);
                    for (const ent of entries) {
                        try {
                            const resp = await fetch(`${API_BASE_URL}/api/history/${encodeURIComponent(folderName)}/${encodeURIComponent(ent.category)}/${encodeURIComponent(ent.filename)}`);
                            if (resp.ok) {
                                const d = await resp.json();
                                if (d.success) {
                                    const normalizedRound = normalizeRound(ent.round);
                                    const originalRound = ent.round; // 保存原始轮次信息
                                    console.log('✅ 加载报告成功:', ent.filename, '分类:', ent.category, '原始轮次:', originalRound, '标准化轮次:', normalizedRound);
                                    reports.push({
                                        round: normalizedRound,
                                        originalRound: originalRound,
                                        filename: ent.filename,
                                        category: ent.category,
                                        content: d.data.content || ''
                                    });
                                } else {
                                    console.log('⚠️ 文件内容获取失败:', ent.filename, d.message);
                                }
                            } else {
                                console.log('❌ 文件请求失败:', ent.filename, resp.status);
                            }
                        } catch {
                        }
                    }
                    reports.sort((a, b) => roundOrder(a.round) - roundOrder(b.round));
                    console.log('📝 最终报告列表:', reports.map(r => ({
                        round: r.round,
                        originalRound: r.originalRound,
                        filename: r.filename,
                        category: r.category,
                        contentLength: r.content.length
                    })));

                    // 按分类统计报告数量
                    const categoryStats = {};
                    reports.forEach(r => {
                        const cat = r.category || '未分类';
                        categoryStats[cat] = (categoryStats[cat] || 0) + 1;
                    });
                    console.log('📊 分类统计:', categoryStats);
                } else {
                    console.log('❌ 历史详情数据无效');
                }
            } else {
                console.log('❌ 历史详情请求失败:', reportResp.status);
            }

            // 构建单选UI
            const controls = `
                    <div style="display:flex; gap:12px; align-items:center; padding:10px 12px; background:#f8fafc; border:1px solid #e5e7eb; border-radius:8px;">
                        <label style="font-size:14px; font-weight:500; color:#1f2937; margin-right:8px;">显示轮次:</label>
                        <label style="display:flex; align-items:center; gap:6px; font-size:13px; color:#334155;"><input type="radio" name="filterRound" id="filterR1" value="1" checked> 初筛(1)</label>
                        <label style="display:flex; align-items:center; gap:6px; font-size:13px; color:#334155;"><input type="radio" name="filterRound" id="filterR2" value="2"> 二筛(2)</label>
                        <label style="display:flex; align-items:center; gap:6px; font-size:13px; color:#334155;"><input type="radio" name="filterRound" id="filterPortrait" value="portrait"> 画像分析</label>
                        <label style="display:flex; align-items:center; gap:6px; font-size:13px; color:#334155;"><input type="radio" name="filterRound" id="filterR3" value="3"> 终筛(3)</label>
                    </div>`;

            // 构建左侧简历显示内容
            let leftHtml;
            if (resumeFileUrl) {
                if (resumeFileType === 'pdf') {
                    // PDF文件显示
                    leftHtml = `<div style="padding:10px; height:100%; display:flex; flex-direction:column;">
                            <h3 style="margin-top:0; margin-bottom:15px;">📝 简历内容 </h3>
                            <div style="flex:1; border:1px solid #e5e7eb; border-radius:8px; overflow:hidden;">
                                <iframe src="${API_BASE_URL}${resumeFileUrl}" 
                                        style="width:100%; height:100%; border:none;" 
                                        title="简历PDF">
                                </iframe>
                            </div>
                        </div>`;
                } else {
                    // 图片文件显示
                    leftHtml = `<div style="padding:10px; height:100%; overflow:auto; display:flex; flex-direction:column;">
                            <h3 style="margin-top:0; margin-bottom:15px;">📝 简历内容</h3>
                            <div style="flex:1; display:flex; justify-content:center; align-items:flex-start; overflow:auto;">
                                <img src="${API_BASE_URL}${resumeFileUrl}" alt="简历图片" style="max-width:100%; height:auto; border:1px solid #e5e7eb; border-radius:8px; box-shadow:0 2px 4px rgba(0,0,0,0.1);" />
                            </div>
                        </div>`;
                }
            } else {
                const debugInfo = `暂无简历文件\n\n调试信息：\n- 候选人: ${candidateName}\n- 文件接口状态: ${resumeResp.status}\n- 请检查 简历文件/ 目录下是否有对应的图片或PDF文件`;
                leftHtml = `<div style="padding:10px; height:100%; overflow:auto;">
                        <h3 style="margin-top:0;">📝 简历内容</h3>
                        <pre style="white-space:pre-wrap; word-break:break-word; font-size:12px; color:#64748b;">${debugInfo.replace(/[&<>]/g, s => ({
                    '&': '&amp;',
                    '<': '&lt;',
                    '>': '&gt;'
                }[s]))}</pre>
                    </div>`;
            }

            function renderReports() {
                console.log('🔄 开始渲染报告，当前报告数量:', reports.length);

                // 获取选中的单选框值
                const selectedRadio = document.querySelector('input[name="filterRound"]:checked');
                if (!selectedRadio) {
                    console.log('❌ 没有选中的单选框');
                    return `<div style="padding:20px; text-align:center; color:#64748b;">请选择一个轮次</div>`;
                }

                const selectedValue = selectedRadio.value;
                console.log('✅ 选中的筛选值:', selectedValue);

                // 根据选中的轮次筛选报告
                const selected = reports.filter((rep, index) => {
                    const round = rep.round;
                    const originalRound = rep.originalRound || rep.round;
                    const originalRoundStr = String(originalRound);
                    const filename = rep.filename || '';
                    const category = rep.category || '';

                    console.log(`🔍 筛选报告 ${index + 1}:`, {
                        filename,
                        category,
                        round,
                        originalRound,
                        selectedValue
                    });

                    if (selectedValue === '1' && round === 1) {
                        console.log('✅ 匹配初筛');
                        return true;
                    }

                    // 二筛判断：主要基于category分类
                    if (selectedValue === '2') {
                        const match = (category.includes('二筛') || category.includes('二轮')) &&
                            !category.includes('画像') &&
                            !filename.includes('画像');
                        console.log('🔍 二筛判断:', {
                            category_has_二筛: category.includes('二筛'),
                            category_has_二轮: category.includes('二轮'),
                            category_has_画像: category.includes('画像'),
                            filename_has_画像: filename.includes('画像'),
                            match
                        });
                        return match;
                    }

                    // 画像分析判断：主要基于category或filename
                    if (selectedValue === 'portrait') {
                        const match = category.includes('画像') ||
                            filename.includes('画像') ||
                            originalRoundStr.includes('画像');
                        console.log('🔍 画像分析判断:', {
                            category_has_画像: category.includes('画像'),
                            filename_has_画像: filename.includes('画像'),
                            originalRound_has_画像: originalRoundStr.includes('画像'),
                            match
                        });
                        return match;
                    }

                    if (selectedValue === '3' && round === 3) {
                        console.log('✅ 匹配终筛');
                        return true;
                    }

                    console.log('❌ 无匹配');
                    return false;
                });

                console.log('📋 筛选结果: 找到', selected.length, '个匹配的报告');

                // 检查是否有报告
                if (selected.length === 0) {
                    const roundName = selectedValue === '1' ? '初筛' :
                        selectedValue === '2' ? '二筛' :
                            selectedValue === 'portrait' ? '画像分析' : '终筛';

                    // 添加更详细的调试信息
                    let debugInfo = `总报告${reports.length}个`;
                    if (reports.length > 0) {
                        const roundTypes = reports.map(r => {
                            const orig = r.originalRound || '无';
                            const filename = r.filename || '无文件名';
                            const category = r.category || '无分类';
                            return `轮次:${r.round}, 原始:${orig}, 文件:${filename}, 分类:${category}`;
                        }).join('<br>');
                        debugInfo += `<br>详细信息: <br>${roundTypes}`;
                    }

                    return `<div style="padding:20px; text-align:center; color:#64748b;">暂无${roundName}评估报告<br><small>调试信息: ${debugInfo}</small></div>`;
                }

                // 显示选中轮次的报告
                const items = selected.map(rep => {
                    const formatted = formatMarkdownContent(rep.content || '');
                    const wrapper = document.createElement('div');
                    wrapper.innerHTML = `<div class="markdown-content">${formatted}</div>`;

                    let roundName;
                    const originalRound = rep.originalRound || rep.round;
                    const originalRoundStr = String(originalRound);
                    const filename = rep.filename || '';
                    const category = rep.category || '';

                    if (rep.round === 1) {
                        roundName = '初筛';
                    } else if (rep.round === 2) {
                        // 判断是二筛还是画像分析
                        if (originalRoundStr.includes('画像') || filename.includes('画像') || category.includes('画像')) {
                            roundName = '画像分析';
                        } else if (category.includes('二筛') || filename.includes('评估报告')) {
                            roundName = '二筛';
                        } else {
                            roundName = '第2轮';
                        }
                    } else if (rep.round === 3) {
                        roundName = '终筛';
                    } else {
                        roundName = `轮次${rep.round}`;
                    }

                    return `<div style="border:1px solid #e5e7eb; border-radius:8px; padding:12px; background:white;">` +
                        `<div style="font-weight:600; color:#1f2937; margin-bottom:8px;">${roundName}评估</div>` +
                        `${wrapper.innerHTML}` +
                        `</div>`;
                });

                return `<div style="display:flex; flex-direction:column; gap:12px;">${items.join('')}</div>`;
            }

            const layout = `
                    ${controls}
                    <div style="display:grid; grid-template-columns: 1fr 1fr; gap:16px; height:calc(95vh - 120px);">
                        <div style="border:1px solid #e5e7eb; border-radius:8px; background:white; overflow:auto;">${leftHtml}</div>
                        <div id="rightPanel" style="border:1px solid #e5e7eb; border-radius:8px; background:#f8fafc; overflow:auto; padding:10px;"></div>
                    </div>`;

            showModalRaw(`${candidateName} - 简历与评估报告`, layout);

            ['filterR1', 'filterR2', 'filterPortrait', 'filterR3'].forEach(id => {
                const el = document.getElementById(id);
                console.log('🎯 绑定事件监听器:', id, el ? '成功' : '失败');
                if (el) {
                    el.addEventListener('change', () => {
                        console.log('🔄 筛选选项改变:', id, 'checked:', el.checked);
                        const panel = document.getElementById('rightPanel');
                        if (panel) {
                            console.log('📝 更新右侧面板内容');
                            panel.innerHTML = renderReports();
                        } else {
                            console.log('❌ 右侧面板不存在');
                        }
                    });
                }
            });

            const panel = document.getElementById('rightPanel');
            if (panel) {
                console.log('🎨 进行初始渲染');
                panel.innerHTML = renderReports();
            } else {
                console.log('❌ 初始渲染失败：右侧面板不存在');
            }

        } catch (e) {
            console.error(e);
            showAlert('加载候选人详情失败: ' + e.message, 'error');
        }
    }

    // 渲染空历史记录状态
    function renderEmptyHistoryState() {
        console.log('📭 渲染空历史记录状态');

        const historyGrid = document.getElementById('historyGrid');
        const emptyState = document.getElementById('emptyHistoryState');

        console.log('🔍 空状态DOM检查:');
        console.log('  - historyGrid:', historyGrid);
        console.log('  - emptyState:', emptyState);

        if (historyGrid) {
            historyGrid.style.display = 'none';
            console.log('🙈 隐藏历史记录网格');
        }

        if (emptyState) {
            emptyState.style.display = 'block';
            console.log('👁️ 显示空状态');
        } else {
            console.error('❌ emptyState 元素未找到');
            // 如果找不到空状态元素，在historyGrid中显示
            if (historyGrid) {
                historyGrid.style.display = 'block';
                historyGrid.innerHTML = `
                        <div style="text-align: center; padding: 60px 20px; color: #64748b;">
                            <div style="font-size: 4rem; margin-bottom: 20px;">📚</div>
                            <div style="font-size: 1.5rem; margin-bottom: 10px; font-weight: 600;">暂无历史记录</div>
                            <div style="font-size: 1rem;">开始评估简历后，历史记录将在这里显示</div>
                        </div>
                    `;
                console.log('📝 在historyGrid中显示空状态消息');
            }
        }
    }

    // 调试历史记录API
    async function debugHistoryAPI() {
        console.log('=== 开始调试历史记录API ===');

        try {
            // 1. 测试基础连接
            console.log('1. 测试基础连接...');
            const statusResponse = await fetch(`${API_BASE_URL}/status`);
            console.log('状态API响应:', {
                status: statusResponse.status,
                ok: statusResponse.ok,
                url: statusResponse.url
            });

            // 2. 测试调试端点
            console.log('2. 测试调试端点...');
            const debugResponse = await fetch(`${API_BASE_URL}/api/debug/result-path`);
            console.log('调试API响应状态:', debugResponse.status);

            if (debugResponse.ok) {
                const debugData = await debugResponse.json();
                console.log('调试信息:', debugData);

                if (debugData.success) {
                    const info = debugData.debug_info;
                    console.log('Result文件夹信息:', {
                        路径: info.result_path,
                        存在: info.path_exists,
                        当前工作目录: info.current_working_dir,
                        脚本目录: info.script_dir,
                        文件夹总数: info.total_folders
                    });

                    if (info.folders && info.folders.length > 0) {
                        console.log('前10个文件夹:', info.folders);
                    } else {
                        console.warn('Result文件夹为空或不存在');
                    }
                }
            } else {
                console.error('调试API请求失败:', debugResponse.status);
            }

            // 3. 测试历史记录API
            console.log('3. 测试历史记录API...');
            const historyResponse = await fetch(`${API_BASE_URL}/api/history`);
            console.log('历史记录API响应状态:', historyResponse.status);

            if (historyResponse.ok) {
                const historyData = await historyResponse.json();
                console.log('历史记录数据:', historyData);

                if (historyData.success) {
                    console.log(`找到 ${historyData.data.length} 条历史记录`);
                    if (historyData.data.length > 0) {
                        console.log('第一条记录示例:', historyData.data[0]);
                    }
                } else {
                    console.error('历史记录API返回错误:', historyData.message);
                }
            } else {
                console.error('历史记录API请求失败:', historyResponse.status);
                const errorText = await historyResponse.text();
                console.error('错误详情:', errorText);
            }

            // 显示调试报告
            showDebugReport();

        } catch (error) {
            console.error('调试过程中发生错误:', error);
            showAlert('调试失败: ' + error.message, 'error');
        }

        console.log('=== 调试完成 ===');
    }

    // 显示调试报告
    function showDebugReport() {
        // 创建可视化调试面板
        const existingPanel = document.getElementById('debugPanel');
        if (existingPanel) {
            existingPanel.remove();
        }

        const debugPanel = document.createElement('div');
        debugPanel.id = 'debugPanel';
        debugPanel.innerHTML = `
                <div style="position: fixed; top: 50px; right: 20px; width: 450px; background: white; border: 2px solid #3b82f6; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.3); z-index: 10000; max-height: 80vh; overflow-y: auto; font-family: system-ui;">
                    <div style="background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; padding: 15px; font-weight: bold; display: flex; justify-content: space-between; align-items: center;">
                        <span>🔧 历史记录调试报告</span>
                        <button onclick="document.getElementById('debugPanel').remove()" style="background: rgba(255,255,255,0.2); border: none; color: white; font-size: 18px; cursor: pointer; border-radius: 4px; padding: 4px 8px;">&times;</button>
                    </div>
                    <div style="padding: 15px; max-height: 60vh; overflow-y: auto;">
                        <div style="margin-bottom: 15px;">
                            <h4 style="margin: 0 0 10px 0; color: #1f2937;">🔍 快速检查</h4>
                            <div id="quickChecks"></div>
                        </div>
                        
                        <div style="margin-bottom: 15px;">
                            <h4 style="margin: 0 0 10px 0; color: #1f2937;">💡 解决建议</h4>
                            <div id="suggestions"></div>
                        </div>
                        
                        <div>
                            <h4 style="margin: 0 0 10px 0; color: #1f2937;">🛠️ 快速操作</h4>
                            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                                <button onclick="location.reload()" style="background: #10b981; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 12px;">🔄 刷新页面</button>
                                <button onclick="showHistoryList()" style="background: #3b82f6; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 12px;">🏠 重新加载历史</button>
                                <button onclick="window.open('${API_BASE_URL}/api/debug/result-path', '_blank')" style="background: #8b5cf6; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 12px;">🔗 查看调试API</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        document.body.appendChild(debugPanel);

        // 填充检查结果
        const quickChecks = document.getElementById('quickChecks');
        const suggestions = document.getElementById('suggestions');

        // DOM检查
        const historyGrid = document.getElementById('historyGrid');
        const historyListSection = document.getElementById('historyListSection');

        let checks = '';
        let suggestionsList = [];

        // 检查DOM元素
        if (historyGrid) {
            const styles = window.getComputedStyle(historyGrid);
            checks += `<div style="margin: 5px 0; padding: 8px; background: #dcfce7; border-left: 3px solid #10b981; border-radius: 4px; font-size: 13px;"><strong>✅ DOM元素:</strong> historyGrid 存在</div>`;

            if (styles.display === 'none') {
                checks += `<div style="margin: 5px 0; padding: 8px; background: #fef3c7; border-left: 3px solid #f59e0b; border-radius: 4px; font-size: 13px;"><strong>⚠️ 显示状态:</strong> historyGrid 被隐藏</div>`;
                suggestionsList.push('historyGrid 元素被隐藏，可能是CSS样式问题或JavaScript逻辑问题');
            }

            if (historyGrid.children.length === 0) {
                checks += `<div style="margin: 5px 0; padding: 8px; background: #fef3c7; border-left: 3px solid #f59e0b; border-radius: 4px; font-size: 13px;"><strong>⚠️ 内容状态:</strong> historyGrid 无子元素</div>`;
                suggestionsList.push('historyGrid 没有子元素，可能是数据加载失败或渲染逻辑有问题');
            }
        } else {
            checks += `<div style="margin: 5px 0; padding: 8px; background: #fee2e2; border-left: 3px solid #ef4444; border-radius: 4px; font-size: 13px;"><strong>❌ DOM元素:</strong> historyGrid 不存在</div>`;
            suggestionsList.push('historyGrid 元素不存在，请检查HTML结构');
        }

        // 检查数据
        if (allHistory && allHistory.length > 0) {
            checks += `<div style="margin: 5px 0; padding: 8px; background: #dcfce7; border-left: 3px solid #10b981; border-radius: 4px; font-size: 13px;"><strong>✅ 数据状态:</strong> 有 ${allHistory.length} 条历史记录</div>`;
        } else {
            checks += `<div style="margin: 5px 0; padding: 8px; background: #fef3c7; border-left: 3px solid #f59e0b; border-radius: 4px; font-size: 13px;"><strong>⚠️ 数据状态:</strong> 无历史记录数据</div>`;
            suggestionsList.push('没有历史记录数据，检查后端API是否返回了正确的数据');
        }

        quickChecks.innerHTML = checks;

        // 填充建议
        if (suggestionsList.length === 0) {
            suggestionsList.push('所有检查都通过了！如果仍有问题，请查看浏览器控制台获取更详细的信息。');
        }

        suggestions.innerHTML = suggestionsList.map((suggestion, index) =>
            `<div style="margin: 5px 0; padding: 8px; background: #f0f9ff; border-left: 3px solid #0ea5e9; border-radius: 4px; font-size: 13px;"><strong>${index + 1}.</strong> ${suggestion}</div>`
        ).join('');

        console.log('📋 调试报告已显示');
        showAlert('调试报告已生成，请查看右侧面板', 'info');
    }

    // 快速测试历史记录功能
    function testHistoryDisplay() {
        console.log('🧪 ========== 开始测试历史记录显示功能 ==========');

        // 1. 测试页面切换
        console.log('1️⃣ 测试页面切换功能...');
        showSection('historyListSection');

        setTimeout(() => {
            const historySection = document.getElementById('historyListSection');
            const historyGrid = document.getElementById('historyGrid');

            console.log('📊 页面切换结果:');
            console.log('  - historyListSection显示:', historySection ? window.getComputedStyle(historySection).display : '元素不存在');
            console.log('  - historyGrid显示:', historyGrid ? window.getComputedStyle(historyGrid).display : '元素不存在');

            // 2. 测试模拟数据渲染
            console.log('2️⃣ 测试模拟数据渲染...');
            if (historyGrid) {
                // 创建测试数据
                const testData = [{
                    folder_name: '测试文件夹_20250101_120000',
                    position_info: '测试岗位',
                    date: '1.1',
                    time: '12:00',
                    total_candidates: 5,
                    stats: {'最终通过': 2, '最终不合格': 3}
                }];

                console.log('📦 设置测试数据:', testData);
                allHistory = testData;

                try {
                    renderHistoryGrid();

                    setTimeout(() => {
                        console.log('📊 渲染测试结果:');
                        console.log('  - historyGrid子元素数量:', historyGrid.children.length);
                        console.log('  - historyGrid HTML内容长度:', historyGrid.innerHTML.length);

                        if (historyGrid.children.length > 0) {
                            console.log('✅ 测试成功：模拟数据能够正常渲染');
                            showAlert('✅ 测试成功：页面能够正常显示历史记录', 'success');

                            // 恢复加载真实数据
                            setTimeout(() => {
                                console.log('3️⃣ 重新加载真实数据...');
                                loadAllHistory();
                            }, 2000);
                        } else {
                            console.log('❌ 测试失败：模拟数据无法渲染');
                            showAlert('❌ 测试失败：页面渲染存在问题', 'error');
                        }
                    }, 500);

                } catch (error) {
                    console.error('💥 渲染测试失败:', error);
                    showAlert('💥 渲染测试失败: ' + error.message, 'error');
                }
            } else {
                console.error('❌ 测试失败：historyGrid元素不存在');
                showAlert('❌ 测试失败：关键DOM元素缺失', 'error');
            }
        }, 200);
    }

    // 测试历史记录点击功能
    function testHistoryClickFunction() {
        console.log('🎯 ========== 测试历史记录点击功能 ==========');

        showAlert('开始测试历史记录点击功能...', 'info');

        // 首先确保在历史记录页面
        showSection('historyListSection');

        setTimeout(() => {
            console.log('1️⃣ 检查历史记录卡片是否存在...');

            const historyGrid = document.getElementById('historyGrid');
            if (!historyGrid) {
                console.error('❌ historyGrid 元素不存在');
                showAlert('❌ 测试失败：historyGrid 元素不存在', 'error');
                return;
            }

            const historyCards = historyGrid.querySelectorAll('.clickable-history-card');
            console.log(`📊 找到 ${historyCards.length} 个历史记录卡片`);

            if (historyCards.length === 0) {
                console.log('2️⃣ 没有历史记录卡片，创建测试卡片...');

                // 创建一个测试卡片
                const testHistory = {
                    folder_name: '测试历史记录_20250101_120000',
                    position_info: '测试岗位 - 点击功能验证',
                    date: '1.1',
                    time: '12:00',
                    total_candidates: 10,
                    stats: {'最终通过': 3, '最终不合格': 7}
                };

                const testCard = createHistoryCard(testHistory);
                if (testCard) {
                    historyGrid.appendChild(testCard);
                    console.log('✅ 测试卡片创建成功');

                    // 添加明显的测试标识
                    testCard.style.border = '3px solid #10b981';
                    testCard.style.backgroundColor = '#f0fdf4';

                    // 添加测试提示
                    const testLabel = document.createElement('div');
                    testLabel.innerHTML = '🧪 这是测试卡片，请点击验证功能！';
                    testLabel.style.cssText = 'position: absolute; top: -10px; right: 10px; background: #10b981; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; z-index: 10;';
                    testCard.style.position = 'relative';
                    testCard.appendChild(testLabel);

                    // 添加自动点击测试按钮
                    const autoTestBtn = document.createElement('button');
                    autoTestBtn.innerHTML = '🎯 自动测试点击';
                    autoTestBtn.style.cssText = 'position: absolute; bottom: 10px; right: 10px; background: #3b82f6; color: white; border: none; padding: 6px 12px; border-radius: 4px; font-size: 11px; cursor: pointer; z-index: 11;';
                    autoTestBtn.onclick = (e) => {
                        e.stopPropagation();
                        console.log('🎯 执行自动点击测试...');
                        showAlert('🎯 3秒后自动点击测试卡片...', 'info');
                        setTimeout(() => {
                            testCard.click();
                        }, 3000);
                    };
                    testCard.appendChild(autoTestBtn);

                    showAlert('✅ 测试卡片已创建！可以点击卡片或"🎯 自动测试点击"按钮！', 'success');

                    // 5秒后自动清理测试卡片
                    setTimeout(() => {
                        if (testCard.parentNode) {
                            testCard.remove();
                            console.log('🧹 测试卡片已自动清理');
                        }
                    }, 10000);

                } else {
                    console.error('❌ 测试卡片创建失败');
                    showAlert('❌ 测试卡片创建失败', 'error');
                }
            } else {
                console.log('3️⃣ 验证现有卡片的点击功能...');

                let clickableCount = 0;
                historyCards.forEach((card, index) => {
                    if (card.onclick && card.style.cursor === 'pointer') {
                        clickableCount++;
                    }

                    // 添加测试高亮
                    card.style.outline = '2px solid #3b82f6';
                    card.style.outlineOffset = '2px';

                    setTimeout(() => {
                        card.style.outline = '';
                        card.style.outlineOffset = '';
                    }, 3000);
                });

                console.log(`📊 可点击的卡片数量: ${clickableCount}/${historyCards.length}`);

                if (clickableCount === historyCards.length) {
                    showAlert(`✅ 点击功能验证通过！找到 ${clickableCount} 个可点击的历史记录卡片`, 'success');
                } else {
                    showAlert(`⚠️ 部分卡片可能无法点击：${clickableCount}/${historyCards.length}`, 'warning');
                }
            }

            console.log('4️⃣ 测试完成！');

        }, 500);
    }

    // 模拟历史记录点击（直接页面跳转）
    function simulateHistoryClick() {
        console.log('📄 模拟历史记录点击');

        // 创建模拟的历史记录数据
        const mockHistory = {
            folder_name: '演示文件夹_' + new Date().getFullYear() + (new Date().getMonth() + 1) + new Date().getDate() + '_120000',
            position_info: 'AI产品经理 - 演示数据',
            date: '12.15',
            time: '12:00',
            total_candidates: 15,
            stats: {
                '最终通过': 5,
                '最终不合格': 3,
                '二筛合格': 4,
                '初筛不合格': 3
            }
        };

        // 直接跳转到详情页面
        showHistoryDetail(mockHistory);
    }

    // 加载所有任务
    async function loadAllTasks() {
        console.log('开始加载任务列表...');
        try {
            const response = await fetch(`${API_BASE_URL}/get_all_tasks`);
            console.log('API响应状态:', response.status);

            if (response.ok) {
                const data = await response.json();
                console.log('API返回数据:', data);

                if (data.success) {
                    allTasks = data.tasks || [];
                    console.log('成功加载任务数量:', allTasks.length);
                } else {
                    allTasks = [];
                    console.error('获取任务失败:', data.message);
                    showAlert('获取任务失败: ' + data.message, 'error');
                }
            } else {
                allTasks = [];
                console.error('API请求失败，状态码:', response.status);
                showAlert(`API请求失败，状态码: ${response.status}`, 'error');
            }
            displayTaskGrid();
        } catch (error) {
            console.error('加载任务失败:', error);
            allTasks = [];
            showAlert('加载任务失败: ' + error.message, 'error');
            displayTaskGrid();
        }
    }


    // 显示任务网格
    function displayTaskGrid() {
        const taskGrid = document.getElementById('taskGrid');
        const emptyState = document.getElementById('emptyTaskState');

        if (!taskGrid || !emptyState) {
            console.error('任务网格元素未找到');
            return;
        }

        if (allTasks.length === 0) {
            taskGrid.style.display = 'none';
            emptyState.style.display = 'block';
            return;
        }

        taskGrid.style.display = 'grid';
        emptyState.style.display = 'none';
        taskGrid.innerHTML = '';

        // 按创建时间倒序排列，确保最新任务在前面
        const sortedTasks = [...allTasks].sort((a, b) => {
            const timeA = new Date(a.created_at || '1970-01-01').getTime();
            const timeB = new Date(b.created_at || '1970-01-01').getTime();
            return timeB - timeA;
        });

        sortedTasks.forEach(task => {
            const taskCard = createTaskCard(task);
            taskGrid.appendChild(taskCard);
        });

        console.log(`已显示 ${sortedTasks.length} 个任务`);
    }

    // 创建任务卡片
    function createTaskCard(task) {
        const card = document.createElement('div');
        card.className = 'task-card';
        card.onclick = () => showTaskDetail(task);

        const statusText = {
            'completed': '已完成',
            'running': '进行中',
            'starting': '启动中',
            'failed': '失败'
        }[task.status] || '未知';

        const positionTitle = task.position_title || '未知岗位';
        const candidatesCount = task.candidatesCount || 0;
        const passedCount = task.passedCount || 0;
        const failedCount = task.failedCount || 0;
        const averageScore = task.averageScore || 0;
        const completedEvaluations = passedCount + failedCount;

        // 计算进度信息
        const totalCount = task.total || candidatesCount || 0;
        const completedCount = task.completed || 0;
        const progressPercent = totalCount > 0 ? Math.round((completedCount / totalCount) * 100) : 0;

        // 构建进度条HTML
        let progressHTML = '';
        // 显示进度条的条件：正在运行、启动中，或者已完成且有进度数据
        if ((task.status === 'running' || task.status === 'starting') ||
            (task.status === 'completed' && totalCount > 0)) {

            let progressText = '';
            if (task.status === 'starting') {
                progressText = '准备中...';
            } else if (totalCount === 0) {
                progressText = '等待数据...';
            } else {
                progressText = `${completedCount}/${totalCount} (${progressPercent}%)`;
            }

            progressHTML = `
                    <div class="task-progress">
                        <div class="progress-info">
                            <span>📊 检测进度</span>
                            <span>${progressText}</span>
                        </div>
                        <div class="progress-bar-bg">
                            <div class="progress-bar-fill ${task.status}" style="width: ${Math.max(progressPercent, 2)}%"></div>
                        </div>
                    </div>
                `;
        }

        card.innerHTML = `
                <div class="task-header">
                    <div>
                        <div class="task-title">${task.title || positionTitle + '评估任务'}</div>
                        <div class="task-meta">
                            <span>📅 ${task.createTime}</span>
                        </div>
                    </div>
                    <div class="task-status ${task.status}">${statusText}</div>
                </div>
                
                <div class="task-info">
                    <div class="task-meta">
                        <span>💼 ${positionTitle}</span>
                        <span>🆔 ${task.task_id ? task.task_id.substring(0, 8) : 'N/A'}</span>
                    </div>
                    
                    ${progressHTML}
                    
                    <div class="task-stats">
                        <div class="stat-item">
                            <span class="stat-number">${candidatesCount}</span>
                            <span class="stat-label">总候选人</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">${completedEvaluations}</span>
                            <span class="stat-label">已评估</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">${passedCount}</span>
                            <span class="stat-label">通过人数</span>
                        </div>
                    </div>
                </div>
            `;

        return card;
    }

    // 显示任务详情
    function showTaskDetail(task) {
        currentTaskDetail = task;
        showSection('taskDetailSection');

        const statusText = {
            'completed': '✅ 已完成',
            'running': '🔄 进行中',
            'starting': '🚀 启动中',
            'failed': '❌ 失败'
        }[task.status] || '❓ 未知状态';

        const candidatesCount = task.candidatesCount || 0;
        const passedCount = task.passedCount || 0;
        const failedCount = task.failedCount || 0;
        const completedEvaluations = passedCount + failedCount;

        // 通过率基于已完成的评估计算，而不是总候选人数
        const passRate = completedEvaluations > 0 ? Math.round(passedCount / completedEvaluations * 100) : 0;

        // 计算进度信息
        const totalCount = task.total || candidatesCount;
        const completedCount = task.completed || 0;
        const progressPercent = totalCount > 0 ? Math.round((completedCount / totalCount) * 100) : 0;

        // 构建进度条HTML（详情页面版本）
        let detailProgressHTML = '';
        if (task.status === 'running' || task.status === 'starting' || (task.status === 'completed' && totalCount > 0)) {
            detailProgressHTML = `
                    <div class="detail-meta-item" style="grid-column: 1 / -1;">
                        <div class="detail-meta-label">检测进度</div>
                        <div style="margin-top: 8px;">
                            <div class="progress-info" style="color: white; margin-bottom: 6px;">
                                <span>已完成 ${completedCount} / ${totalCount}</span>
                                <span>${progressPercent}%</span>
                            </div>
                            <div class="progress-bar-bg">
                                <div class="progress-bar-fill ${task.status}" style="width: ${progressPercent}%"></div>
                            </div>
                        </div>
                    </div>
                `;
        }

        // 更新任务详情头部
        const header = document.getElementById('taskDetailHeader');
        header.innerHTML = `
                <div class="task-detail-title">${task.title || (task.position_title + '评估任务')}</div>
                <div class="task-detail-meta">
                    <div class="detail-meta-item">
                        <div class="detail-meta-label">创建时间</div>
                        <div class="detail-meta-value">${task.createTime}</div>
                    </div>
                    <div class="detail-meta-item">
                        <div class="detail-meta-label">评估状态</div>
                        <div class="detail-meta-value">${statusText}</div>
                    </div>
                    <div class="detail-meta-item">
                        <div class="detail-meta-label">候选人总数</div>
                        <div class="detail-meta-value">${candidatesCount}人</div>
                    </div>
                    <div class="detail-meta-item">
                        <div class="detail-meta-label">通过率</div>
                        <div class="detail-meta-value">${passRate}% (${passedCount}/${completedEvaluations})</div>
                    </div>
                    <div class="detail-meta-item">
                        <div class="detail-meta-label">平均分数</div>
                        <div class="detail-meta-value">${task.averageScore || 0}分</div>
                    </div>
                    <div class="detail-meta-item">
                        <div class="detail-meta-label">任务ID</div>
                        <div class="detail-meta-value">${task.task_id ? task.task_id.substring(0, 12) : 'N/A'}</div>
                    </div>
                    ${detailProgressHTML}
                </div>
            `;

        // 显示候选人列表
        const candidates = task.details || [];
        displayTaskCandidates(candidates);
    }

    // 显示任务的候选人列表
    function displayTaskCandidates(candidates) {
        const container = document.getElementById('candidateDetailList');
        container.innerHTML = '';

        if (!candidates || candidates.length === 0) {
            container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-state-icon">👥</div>
                        <div class="empty-state-title">暂无候选人数据</div>
                        <div class="empty-state-desc">该任务还没有完成的候选人评估结果</div>
                    </div>
                `;
            return;
        }

        candidates.forEach(candidate => {
            const candidateCard = document.createElement('div');
            candidateCard.className = 'candidate-card';

            const statusText = candidate.qualified ? '通过' : '未通过';
            const statusColor = candidate.qualified ? '#10b981' : '#ef4444';
            const scoreClass = getScoreClass(candidate.final_score || 0);
            const candidateName = candidate.candidate_name || '未知候选人';

            candidateCard.innerHTML = `
                    <div class="candidate-header">
                        <div class="candidate-name">${candidateName}</div>
                        <div class="candidate-score ${scoreClass}">${candidate.final_score || 0}分</div>
                    </div>
                    <div class="candidate-info">
                        <div class="info-row">
                            <span class="info-label">最终轮次</span>
                            <span class="info-value">第${candidate.final_round || 1}轮</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">评估状态</span>
                            <span class="info-value" style="color: ${statusColor}">${statusText}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">评估结果</span>
                            <span class="info-value">${candidate.success ? '✅ 成功' : '❌ 失败'}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">备注信息</span>
                            <span class="info-value">${candidate.message || '无'}</span>
                        </div>
                    </div>
                    <div class="candidate-actions">
                        <button class="btn-card btn-resume" onclick="viewResume('${candidateName}')">
                            📄 查看简历
                        </button>
                        <button class="btn-card btn-report" onclick="viewReports('${candidateName}')">
                            📊 评估报告
                        </button>
                        <button class="btn-card btn-detail" onclick="showCandidateDetail('${candidateName}')">
                            🔍 详细分析
                        </button>
                    </div>
                `;

            container.appendChild(candidateCard);
        });
    }

    // 显示候选人详细分析
    function showCandidateDetail(candidateName) {
        // 可以扩展为显示更详细的候选人分析页面
        alert(`查看 ${candidateName} 的详细分析功能开发中...`);
    }

    // 修改显示指定部分函数，支持新的界面
    function showSection(sectionId) {
        const sections = ['uploadSection', 'progressSection', 'resultsSection', 'tasksSection', 'taskListSection', 'taskDetailSection', 'historyListSection', 'historyDetailSection', 'talentPoolSection'];

        sections.forEach(id => {
            const section = document.getElementById(id);
            if (section) {
                if (id === sectionId) {
                    section.style.display = 'block';
                } else {
                    section.style.display = 'none';
                }
            }
        });
    }

    // 页面加载时初始化任务数据
    document.addEventListener('DOMContentLoaded', function () {
        // 延迟加载任务数据，避免影响页面初始化
        setTimeout(() => {
            // 只有在服务器运行时才加载任务数据
            if (window.location.protocol !== 'file:') {
                loadAllTasks();
            }
        }, 1000);

        // 初始化人才库示例数据（如果为空）
        initializeTalentPoolSampleData();
    });

    // 页面卸载时清理定时器
    window.addEventListener('beforeunload', function () {
        stopTaskRefresh();
    });

    // 页面失去焦点时暂停刷新，获得焦点时恢复
    document.addEventListener('visibilitychange', function () {
        if (document.hidden) {
            stopTaskRefresh();
        } else {
            const currentSection = getCurrentSection();
            if (currentSection === 'taskListSection' || currentSection === 'taskDetailSection') {
                startTaskRefresh();
            }
        }
    });

    // 已移除JD路径管理功能

    // 开始任务刷新定时器
    function startTaskRefresh() {
        // 清除现有定时器
        if (taskRefreshInterval) {
            clearInterval(taskRefreshInterval);
        }

        // 每5秒刷新一次任务状态
        taskRefreshInterval = setInterval(() => {
            const currentSection = getCurrentSection();
            if (currentSection === 'taskListSection' || currentSection === 'taskDetailSection') {
                refreshTaskData();
            }
        }, 5000);
    }

    // 停止任务刷新定时器
    function stopTaskRefresh() {
        if (taskRefreshInterval) {
            clearInterval(taskRefreshInterval);
            taskRefreshInterval = null;
        }
    }

    // 获取当前显示的界面
    function getCurrentSection() {
        const sections = ['uploadSection', 'progressSection', 'resultsSection', 'tasksSection', 'taskListSection', 'taskDetailSection', 'historyListSection', 'historyDetailSection', 'talentPoolSection'];
        for (const sectionId of sections) {
            const section = document.getElementById(sectionId);
            if (section && section.style.display !== 'none') {
                return sectionId;
            }
        }
        return null;
    }

    // 刷新任务数据
    async function refreshTaskData() {
        try {
            const response = await fetch(`${API_BASE_URL}/get_all_tasks`);
            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    const newTasks = data.tasks || [];
                    updateTasksWithNewData(newTasks);
                }
            }
        } catch (error) {
            console.error('刷新任务数据失败:', error);
        }
    }

    // 更新任务数据
    function updateTasksWithNewData(newTasks) {
        const currentSection = getCurrentSection();

        // 更新全局任务数据
        allTasks = newTasks;

        if (currentSection === 'taskListSection') {
            // 更新任务列表中的进度条
            updateTaskCardsProgress();
        } else if (currentSection === 'taskDetailSection' && currentTaskDetail) {
            // 更新任务详情页面
            const updatedTask = newTasks.find(t => t.task_id === currentTaskDetail.task_id);
            if (updatedTask) {
                currentTaskDetail = updatedTask;
                showTaskDetail(updatedTask);
            }
        }
    }

    // 更新任务卡片的进度
    function updateTaskCardsProgress() {
        const taskCards = document.querySelectorAll('.task-card');
        taskCards.forEach((card, index) => {
            if (index < allTasks.length) {
                const task = allTasks[index];
                const progressBar = card.querySelector('.progress-bar-fill');
                const progressInfo = card.querySelector('.progress-info span:last-child');

                if (progressBar && progressInfo) {
                    const totalCount = task.total || task.candidatesCount || 0;
                    const completedCount = task.completed || 0;
                    const progressPercent = totalCount > 0 ? Math.round((completedCount / totalCount) * 100) : 0;

                    // 更新进度条宽度，至少显示2%以便看到
                    progressBar.style.width = Math.max(progressPercent, 2) + '%';
                    progressBar.className = `progress-bar-fill ${task.status}`;

                    // 更新进度文本
                    let progressText = '';
                    if (task.status === 'starting') {
                        progressText = '准备中...';
                    } else if (totalCount === 0) {
                        progressText = '等待数据...';
                    } else {
                        progressText = `${completedCount}/${totalCount} (${progressPercent}%)`;
                    }
                    progressInfo.textContent = progressText;
                }

                // 更新任务状态
                const statusElement = card.querySelector('.task-status');
                if (statusElement) {
                    const statusText = {
                        'completed': '已完成',
                        'running': '进行中',
                        'starting': '启动中',
                        'failed': '失败'
                    }[task.status] || '未知';

                    statusElement.textContent = statusText;
                    statusElement.className = `task-status ${task.status}`;
                }

                // 更新统计数据
                const statNumbers = card.querySelectorAll('.stat-number');
                if (statNumbers.length >= 3) {
                    const completedCount = (task.passedCount || 0) + (task.failedCount || 0);
                    statNumbers[0].textContent = task.candidatesCount || 0; // 总候选人
                    statNumbers[1].textContent = completedCount; // 已评估
                    statNumbers[2].textContent = task.passedCount || 0; // 通过人数
                }
            }
        });
    }

    // 修改显示指定部分函数，处理定时器
    function showSection(sectionId) {
        const sections = ['uploadSection', 'progressSection', 'resultsSection', 'tasksSection', 'taskListSection', 'taskDetailSection', 'historyListSection', 'historyDetailSection'];

        sections.forEach(id => {
            const section = document.getElementById(id);
            if (section) {
                if (id === sectionId) {
                    section.style.display = 'block';
                } else {
                    section.style.display = 'none';
                }
            }
        });

        // 根据界面决定是否启动定时器
        if (sectionId === 'taskListSection' || sectionId === 'taskDetailSection') {
            startTaskRefresh();
        } else {
            stopTaskRefresh();
        }
    }

    // 导出结果
    function exportResults() {
        alert('导出功能开发中...');
    }

    // ==================== 人才库功能 ====================
    
    // 隐藏所有界面
    function hideAllSections() {
        const sections = ['uploadSection', 'progressSection', 'resultsSection', 'tasksSection', 'taskListSection', 'taskDetailSection', 'historyListSection', 'historyDetailSection', 'talentPoolSection'];
        sections.forEach(id => {
            const section = document.getElementById(id);
            if (section) {
                section.style.display = 'none';
            }
        });
    }

    // 显示人才库界面
    function showTalentPool() {
        hideAllSections();
        document.getElementById('talentPoolSection').style.display = 'block';
        loadTalentPoolData();
    }

    // 加载人才库数据
    async function loadTalentPoolData() {
        try {
            // 从localStorage获取人才库数据
            const talentPoolData = JSON.parse(localStorage.getItem('talentPool') || '{}');
            
            if (Object.keys(talentPoolData).length === 0) {
                showEmptyTalentPool();
                return;
            }

            // 更新统计信息
            updateTalentPoolStats(talentPoolData);
            
            // 更新职位筛选器
            updatePositionFilter(talentPoolData);
            
            // 显示职位列表
            displayPositions(talentPoolData);
            
        } catch (error) {
            console.error('加载人才库数据失败:', error);
            showEmptyTalentPool();
        }
    }

    // 更新人才库统计信息
    function updateTalentPoolStats(talentPoolData) {
        let totalCandidates = 0;
        let contactedCandidates = 0;
        let pendingCandidates = 0;
        const positions = Object.keys(talentPoolData);

        positions.forEach(position => {
            const candidates = talentPoolData[position];
            totalCandidates += candidates.length;
            candidates.forEach(candidate => {
                if (candidate.contactStatus === 'contacted') {
                    contactedCandidates++;
                } else {
                    pendingCandidates++;
                }
            });
        });

        document.getElementById('totalCandidates').textContent = totalCandidates;
        document.getElementById('contactedCandidates').textContent = contactedCandidates;
        document.getElementById('pendingCandidates').textContent = pendingCandidates;
        document.getElementById('totalPositions').textContent = positions.length;
    }

    // 更新职位筛选器
    function updatePositionFilter(talentPoolData) {
        const positionFilter = document.getElementById('positionFilter');
        const positions = Object.keys(talentPoolData);
        
        // 清空现有选项
        positionFilter.innerHTML = '<option value="">全部职位</option>';
        
        // 添加职位选项
        positions.forEach(position => {
            const option = document.createElement('option');
            option.value = position;
            option.textContent = position;
            positionFilter.appendChild(option);
        });
    }

    // 显示职位列表
    function displayPositions(talentPoolData) {
        const container = document.getElementById('positionsContainer');
        container.innerHTML = '';

        Object.keys(talentPoolData).forEach(position => {
            const candidates = talentPoolData[position];
            const positionCard = createPositionCard(position, candidates);
            container.appendChild(positionCard);
        });
    }

    // 创建职位卡片
    function createPositionCard(position, candidates) {
        const positionCard = document.createElement('div');
        positionCard.className = 'position-card';
        positionCard.style.cssText = `
            background: white;
            border-radius: 16px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border: 2px solid #e5e7eb;
        `;

        const contactedCount = candidates.filter(c => c.contactStatus === 'contacted').length;
        const pendingCount = candidates.filter(c => c.contactStatus === 'pending').length;

        positionCard.innerHTML = `
            <div class="position-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <div>
                    <h3 style="font-size: 1.5rem; font-weight: 600; color: #1e293b; margin-bottom: 5px;">${position}</h3>
                    <div style="display: flex; gap: 20px; color: #64748b;">
                        <span>👥 候选人: ${candidates.length}</span>
                        <span>✅ 已联系: ${contactedCount}</span>
                        <span>⏳ 待联系: ${pendingCount}</span>
                    </div>
                </div>
                <button class="btn btn-secondary" onclick="togglePositionCandidates(this, '${position}')" style="min-width: 100px;">
                    📋 展开
                </button>
            </div>
            <div class="candidates-container" id="candidates_${position}" style="display: none;">
                <div class="candidates-list">
                    ${candidates.map(candidate => createCandidateRow(candidate)).join('')}
                </div>
            </div>
        `;

        return positionCard;
    }

    // 创建候选人行
    function createCandidateRow(candidate) {
        const contactStatus = candidate.contactStatus || 'pending';
        const statusText = contactStatus === 'contacted' ? '已联系' : '待联系';
        const statusClass = contactStatus === 'contacted' ? 'status-contacted' : 'status-pending';
        
        return `
            <div class="candidate-row" style="
                display: flex; 
                justify-content: space-between; 
                align-items: center; 
                padding: 15px; 
                margin: 10px 0; 
                background: #f8fafc; 
                border-radius: 8px; 
                border-left: 4px solid ${contactStatus === 'contacted' ? '#10b981' : '#f59e0b'};
            ">
                <div class="candidate-info" style="flex: 1;">
                    <div style="font-weight: 600; color: #1e293b; margin-bottom: 5px;">${candidate.name}</div>
                    <div style="color: #64748b; font-size: 0.9rem;">
                        ${candidate.age} | ${candidate.experience} | ${candidate.education}
                    </div>
                </div>
                <div class="candidate-score" style="text-align: center; margin: 0 20px;">
                    <div style="font-size: 1.2rem; font-weight: 600; color: #1e293b;">${candidate.score}</div>
                    <div style="color: #64748b; font-size: 0.8rem;">分数</div>
                </div>
                <div class="candidate-status" style="margin: 0 20px;">
                    <span class="${statusClass}" style="
                        padding: 4px 12px; 
                        border-radius: 20px; 
                        font-size: 0.8rem; 
                        font-weight: 500;
                        background: ${contactStatus === 'contacted' ? '#d1fae5' : '#fef3c7'};
                        color: ${contactStatus === 'contacted' ? '#065f46' : '#92400e'};
                    ">${statusText}</span>
                </div>
                <div class="candidate-actions">
                    <button class="btn btn-sm btn-primary" onclick="toggleContactStatus('${candidate.id}', '${candidate.name}')" style="margin-right: 10px;">
                        ${contactStatus === 'contacted' ? '取消联系' : '标记已联系'}
                    </button>
                    <button class="btn btn-sm btn-secondary" onclick="viewCandidateDetail('${candidate.id}')">
                        查看详情
                    </button>
                </div>
            </div>
        `;
    }

    // 切换职位候选人显示
    function togglePositionCandidates(button, position) {
        const container = document.getElementById(`candidates_${position}`);
        const isVisible = container.style.display !== 'none';
        
        if (isVisible) {
            container.style.display = 'none';
            button.textContent = '📋 展开';
        } else {
            container.style.display = 'block';
            button.textContent = '📋 收起';
        }
    }

    // 切换联系状态
    function toggleContactStatus(candidateId, candidateName) {
        try {
            const talentPoolData = JSON.parse(localStorage.getItem('talentPool') || '{}');
            
            // 找到候选人并更新状态
            Object.keys(talentPoolData).forEach(position => {
                const candidates = talentPoolData[position];
                const candidate = candidates.find(c => c.id === candidateId);
                if (candidate) {
                    candidate.contactStatus = candidate.contactStatus === 'contacted' ? 'pending' : 'contacted';
                    localStorage.setItem('talentPool', JSON.stringify(talentPoolData));
                    
                    // 刷新显示
                    loadTalentPoolData();
                    
                    showAlert(`候选人 ${candidateName} 状态已更新`, 'success');
                    return;
                }
            });
        } catch (error) {
            console.error('更新联系状态失败:', error);
            showAlert('更新状态失败', 'error');
        }
    }

    // 查看候选人详情
    function viewCandidateDetail(candidateId) {
        // 这里可以实现查看候选人详细信息的逻辑
        showAlert('候选人详情功能开发中...', 'info');
    }

    // 筛选人才库
    function filterTalentPool() {
        const positionFilter = document.getElementById('positionFilter').value;
        const contactFilter = document.getElementById('contactFilter').value;
        const searchInput = document.getElementById('talentSearchInput').value.toLowerCase();
        
        try {
            const talentPoolData = JSON.parse(localStorage.getItem('talentPool') || '{}');
            let filteredData = {};

            Object.keys(talentPoolData).forEach(position => {
                if (positionFilter && position !== positionFilter) {
                    return;
                }

                const candidates = talentPoolData[position].filter(candidate => {
                    // 联系状态筛选
                    if (contactFilter && candidate.contactStatus !== contactFilter) {
                        return false;
                    }
                    
                    // 姓名搜索
                    if (searchInput && !candidate.name.toLowerCase().includes(searchInput)) {
                        return false;
                    }
                    return true;
                });

                if (candidates.length > 0) {
                    filteredData[position] = candidates;
                }
            });

            // 更新显示
            if (Object.keys(filteredData).length === 0) {
                showEmptyTalentPool();
            } else {
                updateTalentPoolStats(filteredData);
                displayPositions(filteredData);
            }
        } catch (error) {
            console.error('筛选人才库失败:', error);
        }
    }

    // 显示空的人才库状态
    function showEmptyTalentPool() {
        document.getElementById('emptyTalentPoolState').style.display = 'block';
        document.getElementById('positionsContainer').innerHTML = '';
        
        // 重置统计信息
        document.getElementById('totalCandidates').textContent = '0';
        document.getElementById('contactedCandidates').textContent = '0';
        document.getElementById('pendingCandidates').textContent = '0';
        document.getElementById('totalPositions').textContent = '0';
    }

    // 添加候选人到人才库
    function addCandidateToTalentPool(candidate, position, score) {
        try {
            const talentPoolData = JSON.parse(localStorage.getItem('talentPool') || '{}');
            
            if (!talentPoolData[position]) {
                talentPoolData[position] = [];
            }

            // 检查是否已存在
            const existingIndex = talentPoolData[position].findIndex(c => c.id === candidate.id);
            if (existingIndex !== -1) {
                // 更新现有候选人信息
                talentPoolData[position][existingIndex] = {
                    ...candidate,
                    score: score,
                    contactStatus: talentPoolData[position][existingIndex].contactStatus || 'pending'
                };
            } else {
                // 添加新候选人
                talentPoolData[position].push({
                    ...candidate,
                    score: score,
                    contactStatus: 'pending'
                });
            }

            localStorage.setItem('talentPool', JSON.stringify(talentPoolData));
            console.log(`候选人 ${candidate.name} 已添加到人才库职位 ${position}`);
        } catch (error) {
            console.error('添加候选人到人才库失败:', error);
        }
    }

    // 生成候选人唯一ID
    function generateCandidateId(name) {
        return `candidate_${name}_${Date.now()}`;
    }

    // 获取当前JD职位名称
    function getCurrentPositionName() {
        // 尝试从JD文件名获取职位名称
        const jdFileInput = document.getElementById('jdFileInput');
        if (jdFileInput && jdFileInput.files && jdFileInput.files.length > 0) {
            const fileName = jdFileInput.files[0].name;
            // 移除文件扩展名
            const positionName = fileName.replace(/\.(txt|md|json)$/i, '');
            return positionName;
        }
        
        // 如果没有JD文件，尝试从页面标题或其他地方获取
        const headerTitle = document.querySelector('.header h1');
        if (headerTitle) {
            return headerTitle.textContent.replace('智能简历评估系统', '').trim() || '未知职位';
        }
        
        return '未知职位';
    }

    // 导出人才库数据
    function exportTalentPool() {
        try {
            const talentPoolData = JSON.parse(localStorage.getItem('talentPool') || '{}');
            const dataStr = JSON.stringify(talentPoolData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `人才库数据_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            
            showAlert('人才库数据导出成功', 'success');
        } catch (error) {
            console.error('导出人才库数据失败:', error);
            showAlert('导出失败', 'error');
        }
    }

    // 导入人才库数据
    function importTalentPool() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const data = JSON.parse(e.target.result);
                        localStorage.setItem('talentPool', JSON.stringify(data));
                        showAlert('人才库数据导入成功', 'success');
                        
                        // 如果当前在人才库页面，刷新显示
                        if (document.getElementById('talentPoolSection').style.display !== 'none') {
                            loadTalentPoolData();
                        }
                    } catch (error) {
                        showAlert('导入失败：文件格式错误', 'error');
                    }
                };
                reader.readAsText(file);
            }
        };
        input.click();
    }

    // 清空人才库数据
    function clearTalentPool() {
        if (confirm('确定要清空所有人才库数据吗？此操作不可恢复！')) {
            localStorage.removeItem('talentPool');
            showAlert('人才库数据已清空', 'success');
            
            // 如果当前在人才库页面，刷新显示
            if (document.getElementById('talentPoolSection').style.display !== 'none') {
                loadTalentPoolData();
            }
        }
    }

    // 初始化人才库示例数据
    function initializeTalentPoolSampleData() {
        try {
            const existingData = localStorage.getItem('talentPool');
            if (!existingData || existingData === '{}') {
                const sampleData = {
                    'AI产品经理': [
                        {
                            id: 'candidate_张三_001',
                            name: '张三',
                            age: '28',
                            experience: '5年',
                            education: '本科',
                            score: 88,
                            contactStatus: 'contacted'
                        },
                        {
                            id: 'candidate_李四_002',
                            name: '李四',
                            age: '30',
                            experience: '7年',
                            education: '硕士',
                            score: 92,
                            contactStatus: 'pending'
                        }
                    ],
                    '软件工程师': [
                        {
                            id: 'candidate_王五_003',
                            name: '王五',
                            age: '26',
                            experience: '3年',
                            education: '本科',
                            score: 85,
                            contactStatus: 'pending'
                        }
                    ],
                    '人力资源主管': [
                        {
                            id: 'candidate_赵六_004',
                            name: '赵六',
                            age: '32',
                            experience: '8年',
                            education: '本科',
                            score: 90,
                            contactStatus: 'contacted'
                        }
                    ]
                };
                
                localStorage.setItem('talentPool', JSON.stringify(sampleData));
                console.log('人才库示例数据初始化完成');
            }
        } catch (error) {
            console.error('初始化人才库示例数据失败:', error);
        }
    }
</script>
</body>
</html>