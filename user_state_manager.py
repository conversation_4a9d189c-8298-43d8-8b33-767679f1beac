#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一用户状态管理器
负责管理所有用户的登录状态和信息
"""

import json
import os
from datetime import datetime
from typing import Optional, Dict, Any, List
from path_manager import path_manager

class UserStateManager:
    """统一用户状态管理器"""
    
    def __init__(self):
        self.current_user = None
        self.user_sessions = {}
        self.session_file = "user_sessions.json"
        self.load_sessions()
        self.jd = {}
        self.jd_count = 0
        self.login_type = None

    def set_current_user(self, user_info: Dict[str, Any]):
        """设置当前用户"""
        self.current_user = user_info
        user_id = user_info.get('姓名', '未知用户')
        
        # 保存到会话
        self.user_sessions[user_id] = {
            'user_info': user_info,
            'login_time': datetime.now().isoformat(),
            'last_active': datetime.now().isoformat(),
            'jd': {},
            'jd_count': 0
        }

        self.load_user_jds(user_id)



        
        self.save_sessions()
        print(f"✅ 用户 {user_id} 已登录")
    
    def get_current_user(self) -> Optional[Dict[str, Any]]:
        """获取当前用户信息"""
        return self.current_user
    
    def get_current_user_name(self) -> str:
        """获取当前用户名"""
        if self.current_user:
            return self.current_user.get('姓名', '未知用户')
        return '未知用户'
    
    def clear_current_user(self):
        """清除当前用户"""
        self.current_user = None
        self.login_type = None
        self.jd = {}
        self.jd_count = 0
    
    def update_user_info(self, user_info: Dict[str, Any]):
        """更新用户信息"""
        if self.current_user:
            self.current_user.update(user_info)
            user_id = self.current_user.get('姓名', '未知用户')
            if user_id in self.user_sessions:
                self.user_sessions[user_id]['user_info'] = self.current_user
                self.user_sessions[user_id]['last_active'] = datetime.now().isoformat()
                self.save_sessions()
    

# ===================================================================
# jd管理
# ===================================================================

    def load_user_jds(self, user_id: str):
        """加载用户职位库"""
        # 统一使用 'jd' 目录，与系统其他部分保持一致
        jd_dir = os.path.join('user', user_id, 'jd')
        if not os.path.exists(jd_dir):
            return 

        jd_list = {}
        for file in os.listdir(jd_dir):
            if file.endswith('.md'):
                with open(os.path.join(jd_dir, file), 'r', encoding='utf-8') as f:
                    file_content = f.read()
                    job_name = file.replace('.md', '')
                    jd_list[job_name] = file_content
        if user_id in self.user_sessions:
            self.user_sessions[user_id]['jd'] = jd_list
            self.user_sessions[user_id]['jd_count'] = len(jd_list)
            self.save_sessions()

    def get_user_jds(self, user_id: str = None) -> Dict[str, str]:
        """获取用户职位库"""
        if not user_id: 
            user_id = self.get_current_user_name()

        if user_id in self.user_sessions:
            print(self.user_sessions[user_id].get('jd',{}))
            return self.user_sessions[user_id].get('jd',{})
        return {}


    def get_jd_content(self, job_name: str, user_id: str = None) -> Optional[str]:
        """获取指定JD的内容"""
        if not user_id:
            user_id = self.get_current_user_name()
        
        try:
            jd_dir = os.path.join('user', user_id, 'jd')
            file_path = os.path.join(jd_dir, job_name + '.md')
            
            if not os.path.exists(file_path):
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return content
            
        except Exception as e:
            print(f"读取JD内容失败: {e}")
            return None

    def save_jd(self, jd_content: dict, job_name: str, user_id: str = None, file_format: str = 'md') -> bool:
        if not user_id:
            user_id = self.get_current_user_name()
        
        try:
            # 确保JD目录存在
            jd_dir = os.path.join('user', user_id, 'jd')
            os.makedirs(jd_dir, exist_ok=True)
            
            safe_title = "".join(c for c in job_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            safe_title = safe_title.replace(' ', '_')
            
            if file_format == 'json':
                filename = f"{safe_title}.json"
            else:
                filename = f"{safe_title}.md"
            
            file_path = os.path.join(jd_dir, filename)
            
            jd_content = "\n".join([f"{key}: {value}" for key, value in jd_content.items()])
            # 保存文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(jd_content)
            
            # 更新JD列表
            self.load_user_jds(user_id)
            
            print(f"✅ JD已保存: {file_path}")
            return True
            
        except Exception as e:
            print(f"保存JD失败: {e}")
            return False
    

    def delete_jd(self, job_name: str, user_id: str = None) -> bool:
        """删除指定JD"""
        if not user_id:
            user_id = self.get_current_user_name()
        
        try:
            jd_dir = os.path.join('user', user_id, 'jd')
            file_path = os.path.join(jd_dir, job_name + '.md')
            
            if os.path.exists(file_path):
                os.remove(file_path)

                self.load_user_jds(user_id)
                print(f"✅ JD已删除: {file_path}")
                return True
            else:
                print(f"❌ JD不存在: {file_path}")
                return False
        except Exception as e:
            print(f"删除JD失败: {e}")
            return False



    def search_jds(self, keyword: str, user_id: str = None) -> List[Dict[str, Any]]:
        """搜索用户的JD"""
        if not user_id:
            user_id = self.get_current_user_name()
        
        jd_list = self.get_user_jds(user_id)
        if not keyword:
            return jd_list
        
        # 简单关键词搜索
        results = []
        keyword_lower = keyword.lower()
        
        for jd in jd_list:
            # 搜索标题
            if keyword_lower in jd['title'].lower():
                results.append(jd)
                continue
            
            # 搜索文件名
            if keyword_lower in jd['filename'].lower():
                results.append(jd)
                continue
            
            # 搜索内容（可选，性能较慢）
            try:
                content = self.get_jd_content(jd['id'], user_id)
                if content and keyword_lower in content.lower():
                    results.append(jd)
            except:
                continue
        
        return results
    



# ===================================================================
# 用户信息管理
# ===================================================================

    def get_user_info_path(self, user_id: str = None) -> str:
        """获取用户信息文件路径"""
        if not user_id:
            user_id = self.get_current_user_name()
        return os.path.join('user', user_id, 'info')

    def save_user_info(self, user_id: str = None) -> bool:
        """保存用户信息"""
        if not user_id:
            user_id = self.get_current_user_name()
        try:
            info_dir = self.get_user_info_path(user_id)
            os.makedirs(info_dir, exist_ok=True)


            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"user_info_{timestamp}.json"
            file_path = os.path.join(info_dir, filename)
            

            user_info = None
            if user_id in self.user_sessions:
                user_info = self.user_sessions[user_id]['user_info']
            
            if user_info:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(user_info, f, ensure_ascii=False, indent=2)
                self.log_manager.info(f"用户信息已保存: {file_path}")
                return True
            else:
                self.log_manager.error(f"没有找到用户信息: {user_id}")
                return False
        except Exception as e:
            print(f"保存用户信息失败: {e}")
            return False
        
    def load_user_info_from_file(self, user_id: str = None) -> Optional[Dict[str, Any]]:
        """从文件加载用户信息"""
        if not user_id:
            user_id = self.get_current_user_name()
        
        try:
            info_dir = self.get_user_info_path(user_id)
            if not os.path.exists(info_dir):
                return None
            
            info_files = []
            for file in os.listdir(info_dir):
                if file.startswith('user_info_') and file.endswith('.json'):
                    file_path = os.path.join(info_dir, file)
                    file_stat = os.stat(file_path)
                    info_files.append((file_path, file_stat.st_mtime))

            if not info_files:
                return None
            
            user_info_path = info_files[0]

            
            # 按修改时间排序，获取最新的
            info_files.sort(key=lambda x: x[1], reverse=True)
            latest_file = info_files[0][0]
            
            with open(latest_file, 'r', encoding='utf-8') as f:
                user_info = json.load(f)
            
            return user_info

        except Exception as e:
            print(f"加载用户信息失败: {e}")
            return None


#==================================================================
# 用户会话管理
#==================================================================


    def load_sessions(self):
        """加载用户会话"""
        try:
            jd_dir = os.path.join(path_manager.get_data_path(), 'jd')
            if os.path.exists(self.session_file):
                with open(self.session_file, 'r', encoding='utf-8') as f:
                    self.user_sessions = json.load(f)
        except Exception as e:
            print(f"加载用户会话失败: {e}")
            self.user_sessions = {}
    
    def save_sessions(self):
        """保存用户会话"""
        try:
            with open(self.session_file, 'w', encoding='utf-8') as f:
                json.dump(self.user_sessions, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存用户会话失败: {e}")


user_state = UserStateManager()