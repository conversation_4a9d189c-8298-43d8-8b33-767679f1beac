2025-08-25 00:02:22 - INFO - 🚀 启动智能简历评估系统
2025-08-25 00:02:22 - INFO - 📋 系统功能:
2025-08-25 00:02:22 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 00:02:22 - INFO -   - 多线程并发处理
2025-08-25 00:02:22 - INFO -   - 实时进度监控
2025-08-25 00:02:22 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 00:02:22 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 00:02:23 - INFO - 多进程任务管理已初始化
2025-08-25 00:02:27 - INFO - 🔐 Boss直聘登录请求: login_boss
2025-08-25 00:02:27 - INFO - 日志系统初始化成功
2025-08-25 00:02:27 - INFO - 开始运行BOSS直聘自动化程序 V2.0...
2025-08-25 00:02:27 - INFO - ====== WebDriver manager ======
2025-08-25 00:02:30 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-25 00:02:31 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-25 00:02:32 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-25 00:02:34 - INFO - WebDriver version 139.0.7258.138 selected
2025-08-25 00:02:34 - INFO - Modern chrome version https://storage.googleapis.com/chrome-for-testing-public/139.0.7258.138/win32/chromedriver-win32.zip
2025-08-25 00:02:34 - INFO - About to download new driver from https://storage.googleapis.com/chrome-for-testing-public/139.0.7258.138/win32/chromedriver-win32.zip
2025-08-25 00:02:36 - INFO - Driver downloading response is 200
2025-08-25 00:02:40 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-25 00:02:40 - INFO - Driver has been saved in cache [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\139.0.7258.138]
2025-08-25 00:02:42 - INFO - 使用自动下载的chromedriver
2025-08-25 00:02:42 - INFO - Chrome浏览器驱动设置成功
2025-08-25 00:02:42 - INFO - 访问登录页面: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:02:44 - INFO - 🔐 开始获取Boss直聘登录二维码...
2025-08-25 00:02:44 - INFO - current_url: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:02:46 - INFO - ✅ 成功获取二维码URL
2025-08-25 00:02:46 - INFO - ✅ 成功获取Boss直聘登录二维码
2025-08-25 00:02:46 - INFO - 等待用户登录（超时时间：300秒）...
2025-08-25 00:02:46 - INFO - 请在浏览器中完成登录操作...
2025-08-25 00:02:51 - INFO - 二维码过期检测: False
2025-08-25 00:02:51 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:02:56 - INFO - 二维码过期检测: False
2025-08-25 00:02:56 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:03:01 - INFO - 二维码过期检测: False
2025-08-25 00:03:01 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:03:07 - INFO - 二维码过期检测: False
2025-08-25 00:03:07 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:03:12 - INFO - 二维码过期检测: False
2025-08-25 00:03:12 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:03:17 - INFO - 二维码过期检测: True
2025-08-25 00:03:17 - INFO - 🔄 检测到二维码过期，开始自动刷新...
2025-08-25 00:03:17 - INFO - ✅ 成功点击刷新按钮
2025-08-25 00:03:19 - INFO - 🔐 开始获取Boss直聘登录二维码...
2025-08-25 00:03:19 - INFO - current_url: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:03:19 - ERROR - ❌ 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:03:19 - ERROR - ❌ 自动刷新二维码失败: 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:03:19 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:03:21 - INFO - 二维码过期检测: False
2025-08-25 00:03:21 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:03:26 - INFO - 二维码过期检测: False
2025-08-25 00:03:26 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:03:31 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-08-25 00:03:31 - INFO - 二维码过期检测: False
2025-08-25 00:03:31 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:03:36 - INFO - 二维码过期检测: False
2025-08-25 00:03:36 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:03:42 - INFO - 二维码过期检测: False
2025-08-25 00:03:42 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:03:47 - INFO - 二维码过期检测: False
2025-08-25 00:03:47 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:03:52 - INFO - 二维码过期检测: True
2025-08-25 00:03:52 - INFO - 🔄 检测到二维码过期，开始自动刷新...
2025-08-25 00:03:52 - INFO - ✅ 成功点击刷新按钮
2025-08-25 00:03:54 - INFO - 🔐 开始获取Boss直聘登录二维码...
2025-08-25 00:03:54 - INFO - current_url: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:03:54 - ERROR - ❌ 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:03:54 - ERROR - ❌ 自动刷新二维码失败: 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:03:54 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:03:57 - INFO - 二维码过期检测: False
2025-08-25 00:03:57 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:04:01 - INFO - 二维码过期检测: False
2025-08-25 00:04:01 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:04:07 - INFO - 二维码过期检测: False
2025-08-25 00:04:07 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:04:12 - INFO - 二维码过期检测: False
2025-08-25 00:04:12 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:04:17 - INFO - 二维码过期检测: False
2025-08-25 00:04:17 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:04:22 - INFO - 二维码过期检测: False
2025-08-25 00:04:22 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:04:27 - INFO - 二维码过期检测: True
2025-08-25 00:04:27 - INFO - 🔄 检测到二维码过期，开始自动刷新...
2025-08-25 00:04:27 - INFO - ✅ 成功点击刷新按钮
2025-08-25 00:04:29 - INFO - 🔐 开始获取Boss直聘登录二维码...
2025-08-25 00:04:29 - INFO - current_url: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:04:29 - ERROR - ❌ 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:04:29 - ERROR - ❌ 自动刷新二维码失败: 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:04:29 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:04:32 - INFO - 二维码过期检测: False
2025-08-25 00:04:32 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:04:37 - INFO - 二维码过期检测: False
2025-08-25 00:04:37 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:04:42 - INFO - 二维码过期检测: False
2025-08-25 00:04:42 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:04:46 - INFO - 二维码过期检测: False
2025-08-25 00:04:46 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:04:50 - ERROR - 等待用户登录时发生错误: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=139.0.7258.128)
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x7238e0]
	(No symbol) [0x0x741709]
	(No symbol) [0x0x7a7e4c]
	(No symbol) [0x0x7c24d9]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:04:50 - ERROR - 用户登录失败或超时
2025-08-25 00:04:50 - INFO - 自动化程序执行完成，浏览器将保持打开状态
2025-08-25 00:04:50 - INFO - 您可以手动查看页面内容，完成后请关闭浏览器
2025-08-25 00:06:08 - INFO - 🚀 启动智能简历评估系统
2025-08-25 00:06:08 - INFO - 📋 系统功能:
2025-08-25 00:06:08 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 00:06:08 - INFO -   - 多线程并发处理
2025-08-25 00:06:08 - INFO -   - 实时进度监控
2025-08-25 00:06:08 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 00:06:08 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 00:06:09 - INFO - 多进程任务管理已初始化
2025-08-25 00:06:16 - INFO - 🔐 Boss直聘登录请求: login_boss
2025-08-25 00:06:16 - INFO - 日志系统初始化成功
2025-08-25 00:06:16 - INFO - 开始运行BOSS直聘自动化程序 V2.0...
2025-08-25 00:06:16 - INFO - ====== WebDriver manager ======
2025-08-25 00:06:20 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-25 00:06:20 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-25 00:06:21 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\139.0.7258.138\chromedriver-win32/chromedriver.exe] found in cache
2025-08-25 00:06:23 - INFO - 使用自动下载的chromedriver
2025-08-25 00:06:23 - INFO - Chrome浏览器驱动设置成功
2025-08-25 00:06:23 - INFO - 访问登录页面: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:06:24 - INFO - 🔐 开始获取Boss直聘登录二维码...
2025-08-25 00:06:25 - INFO - current_url: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:06:26 - INFO - ✅ 成功获取二维码URL
2025-08-25 00:06:26 - INFO - ✅ 成功获取Boss直聘登录二维码
2025-08-25 00:06:26 - INFO - 等待用户登录（超时时间：300秒）...
2025-08-25 00:06:26 - INFO - 请在浏览器中完成登录操作...
2025-08-25 00:06:32 - INFO - 二维码过期检测: False
2025-08-25 00:06:32 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:06:32 - INFO - 返回登录状态检测结果:
2025-08-25 00:06:32 - INFO -   - qr_expired: False
2025-08-25 00:06:32 - INFO -   - in_personal_center: False
2025-08-25 00:06:32 - INFO -   - auto_refresh_needed: False
2025-08-25 00:06:32 - INFO -   - new_qr_code: None
2025-08-25 00:06:32 - INFO -   - is_refreshing: False
2025-08-25 00:06:37 - INFO - 二维码过期检测: False
2025-08-25 00:06:37 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:06:37 - INFO - 返回登录状态检测结果:
2025-08-25 00:06:37 - INFO -   - qr_expired: False
2025-08-25 00:06:37 - INFO -   - in_personal_center: False
2025-08-25 00:06:37 - INFO -   - auto_refresh_needed: False
2025-08-25 00:06:37 - INFO -   - new_qr_code: None
2025-08-25 00:06:37 - INFO -   - is_refreshing: False
2025-08-25 00:06:41 - INFO - 二维码过期检测: False
2025-08-25 00:06:41 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:06:41 - INFO - 返回登录状态检测结果:
2025-08-25 00:06:41 - INFO -   - qr_expired: False
2025-08-25 00:06:41 - INFO -   - in_personal_center: False
2025-08-25 00:06:41 - INFO -   - auto_refresh_needed: False
2025-08-25 00:06:41 - INFO -   - new_qr_code: None
2025-08-25 00:06:41 - INFO -   - is_refreshing: False
2025-08-25 00:06:47 - INFO - 二维码过期检测: False
2025-08-25 00:06:47 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:06:47 - INFO - 返回登录状态检测结果:
2025-08-25 00:06:47 - INFO -   - qr_expired: False
2025-08-25 00:06:47 - INFO -   - in_personal_center: False
2025-08-25 00:06:47 - INFO -   - auto_refresh_needed: False
2025-08-25 00:06:47 - INFO -   - new_qr_code: None
2025-08-25 00:06:47 - INFO -   - is_refreshing: False
2025-08-25 00:06:52 - INFO - 二维码过期检测: False
2025-08-25 00:06:52 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:06:52 - INFO - 返回登录状态检测结果:
2025-08-25 00:06:52 - INFO -   - qr_expired: False
2025-08-25 00:06:52 - INFO -   - in_personal_center: False
2025-08-25 00:06:52 - INFO -   - auto_refresh_needed: False
2025-08-25 00:06:52 - INFO -   - new_qr_code: None
2025-08-25 00:06:52 - INFO -   - is_refreshing: False
2025-08-25 00:06:57 - INFO - 二维码过期检测: True
2025-08-25 00:06:57 - INFO - 🔄 检测到二维码过期，开始自动刷新...
2025-08-25 00:06:57 - INFO - ✅ 成功点击刷新按钮
2025-08-25 00:06:59 - INFO - 🔐 开始获取Boss直聘登录二维码...
2025-08-25 00:06:59 - INFO - current_url: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:06:59 - ERROR - ❌ 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:06:59 - ERROR - ❌ 自动刷新二维码失败: 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:06:59 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:06:59 - INFO - 返回登录状态检测结果:
2025-08-25 00:06:59 - INFO -   - qr_expired: False
2025-08-25 00:06:59 - INFO -   - in_personal_center: False
2025-08-25 00:06:59 - INFO -   - auto_refresh_needed: False
2025-08-25 00:06:59 - INFO -   - new_qr_code: None
2025-08-25 00:06:59 - INFO -   - is_refreshing: False
2025-08-25 00:07:01 - INFO - 二维码过期检测: False
2025-08-25 00:07:01 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:07:01 - INFO - 返回登录状态检测结果:
2025-08-25 00:07:01 - INFO -   - qr_expired: False
2025-08-25 00:07:01 - INFO -   - in_personal_center: False
2025-08-25 00:07:01 - INFO -   - auto_refresh_needed: False
2025-08-25 00:07:01 - INFO -   - new_qr_code: None
2025-08-25 00:07:01 - INFO -   - is_refreshing: False
2025-08-25 00:07:07 - INFO - 二维码过期检测: False
2025-08-25 00:07:07 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:07:07 - INFO - 返回登录状态检测结果:
2025-08-25 00:07:07 - INFO -   - qr_expired: False
2025-08-25 00:07:07 - INFO -   - in_personal_center: False
2025-08-25 00:07:07 - INFO -   - auto_refresh_needed: False
2025-08-25 00:07:07 - INFO -   - new_qr_code: None
2025-08-25 00:07:07 - INFO -   - is_refreshing: False
2025-08-25 00:07:11 - INFO - 二维码过期检测: False
2025-08-25 00:07:11 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:07:11 - INFO - 返回登录状态检测结果:
2025-08-25 00:07:11 - INFO -   - qr_expired: False
2025-08-25 00:07:11 - INFO -   - in_personal_center: False
2025-08-25 00:07:11 - INFO -   - auto_refresh_needed: False
2025-08-25 00:07:11 - INFO -   - new_qr_code: None
2025-08-25 00:07:11 - INFO -   - is_refreshing: False
2025-08-25 00:07:16 - INFO - 二维码过期检测: False
2025-08-25 00:07:16 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:07:16 - INFO - 返回登录状态检测结果:
2025-08-25 00:07:16 - INFO -   - qr_expired: False
2025-08-25 00:07:16 - INFO -   - in_personal_center: False
2025-08-25 00:07:16 - INFO -   - auto_refresh_needed: False
2025-08-25 00:07:16 - INFO -   - new_qr_code: None
2025-08-25 00:07:16 - INFO -   - is_refreshing: False
2025-08-25 00:07:21 - INFO - 二维码过期检测: False
2025-08-25 00:07:21 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:07:21 - INFO - 返回登录状态检测结果:
2025-08-25 00:07:21 - INFO -   - qr_expired: False
2025-08-25 00:07:21 - INFO -   - in_personal_center: False
2025-08-25 00:07:21 - INFO -   - auto_refresh_needed: False
2025-08-25 00:07:21 - INFO -   - new_qr_code: None
2025-08-25 00:07:21 - INFO -   - is_refreshing: False
2025-08-25 00:07:26 - INFO - 二维码过期检测: False
2025-08-25 00:07:26 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:07:26 - INFO - 返回登录状态检测结果:
2025-08-25 00:07:26 - INFO -   - qr_expired: False
2025-08-25 00:07:26 - INFO -   - in_personal_center: False
2025-08-25 00:07:26 - INFO -   - auto_refresh_needed: False
2025-08-25 00:07:26 - INFO -   - new_qr_code: None
2025-08-25 00:07:26 - INFO -   - is_refreshing: False
2025-08-25 00:07:32 - INFO - 二维码过期检测: True
2025-08-25 00:07:32 - INFO - 🔄 检测到二维码过期，开始自动刷新...
2025-08-25 00:07:32 - INFO - ✅ 成功点击刷新按钮
2025-08-25 00:07:34 - INFO - 🔐 开始获取Boss直聘登录二维码...
2025-08-25 00:07:35 - INFO - current_url: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:07:35 - ERROR - ❌ 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:07:35 - ERROR - ❌ 自动刷新二维码失败: 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:07:35 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:07:35 - INFO - 返回登录状态检测结果:
2025-08-25 00:07:35 - INFO -   - qr_expired: False
2025-08-25 00:07:35 - INFO -   - in_personal_center: False
2025-08-25 00:07:35 - INFO -   - auto_refresh_needed: False
2025-08-25 00:07:35 - INFO -   - new_qr_code: None
2025-08-25 00:07:35 - INFO -   - is_refreshing: False
2025-08-25 00:07:37 - INFO - 二维码过期检测: False
2025-08-25 00:07:37 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:07:37 - INFO - 返回登录状态检测结果:
2025-08-25 00:07:37 - INFO -   - qr_expired: False
2025-08-25 00:07:37 - INFO -   - in_personal_center: False
2025-08-25 00:07:37 - INFO -   - auto_refresh_needed: False
2025-08-25 00:07:37 - INFO -   - new_qr_code: None
2025-08-25 00:07:37 - INFO -   - is_refreshing: False
2025-08-25 00:07:42 - INFO - 二维码过期检测: False
2025-08-25 00:07:42 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:07:42 - INFO - 返回登录状态检测结果:
2025-08-25 00:07:42 - INFO -   - qr_expired: False
2025-08-25 00:07:42 - INFO -   - in_personal_center: False
2025-08-25 00:07:42 - INFO -   - auto_refresh_needed: False
2025-08-25 00:07:42 - INFO -   - new_qr_code: None
2025-08-25 00:07:42 - INFO -   - is_refreshing: False
2025-08-25 00:07:47 - INFO - 二维码过期检测: False
2025-08-25 00:07:47 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:07:47 - INFO - 返回登录状态检测结果:
2025-08-25 00:07:47 - INFO -   - qr_expired: False
2025-08-25 00:07:47 - INFO -   - in_personal_center: False
2025-08-25 00:07:47 - INFO -   - auto_refresh_needed: False
2025-08-25 00:07:47 - INFO -   - new_qr_code: None
2025-08-25 00:07:47 - INFO -   - is_refreshing: False
2025-08-25 00:07:52 - INFO - 二维码过期检测: False
2025-08-25 00:07:52 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:07:52 - INFO - 返回登录状态检测结果:
2025-08-25 00:07:52 - INFO -   - qr_expired: False
2025-08-25 00:07:52 - INFO -   - in_personal_center: False
2025-08-25 00:07:52 - INFO -   - auto_refresh_needed: False
2025-08-25 00:07:52 - INFO -   - new_qr_code: None
2025-08-25 00:07:52 - INFO -   - is_refreshing: False
2025-08-25 00:07:57 - INFO - 二维码过期检测: False
2025-08-25 00:07:57 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:07:57 - INFO - 返回登录状态检测结果:
2025-08-25 00:07:57 - INFO -   - qr_expired: False
2025-08-25 00:07:57 - INFO -   - in_personal_center: False
2025-08-25 00:07:57 - INFO -   - auto_refresh_needed: False
2025-08-25 00:07:57 - INFO -   - new_qr_code: None
2025-08-25 00:07:57 - INFO -   - is_refreshing: False
2025-08-25 00:08:02 - INFO - 二维码过期检测: False
2025-08-25 00:08:02 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:08:02 - INFO - 返回登录状态检测结果:
2025-08-25 00:08:02 - INFO -   - qr_expired: False
2025-08-25 00:08:02 - INFO -   - in_personal_center: False
2025-08-25 00:08:02 - INFO -   - auto_refresh_needed: False
2025-08-25 00:08:02 - INFO -   - new_qr_code: None
2025-08-25 00:08:02 - INFO -   - is_refreshing: False
2025-08-25 00:08:07 - INFO - 二维码过期检测: True
2025-08-25 00:08:07 - INFO - 🔄 检测到二维码过期，开始自动刷新...
2025-08-25 00:08:07 - INFO - ✅ 成功点击刷新按钮
2025-08-25 00:08:09 - INFO - 🔐 开始获取Boss直聘登录二维码...
2025-08-25 00:08:10 - INFO - current_url: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:08:10 - ERROR - ❌ 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:08:10 - ERROR - ❌ 自动刷新二维码失败: 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:08:10 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:08:10 - INFO - 返回登录状态检测结果:
2025-08-25 00:08:10 - INFO -   - qr_expired: False
2025-08-25 00:08:10 - INFO -   - in_personal_center: False
2025-08-25 00:08:10 - INFO -   - auto_refresh_needed: False
2025-08-25 00:08:10 - INFO -   - new_qr_code: None
2025-08-25 00:08:10 - INFO -   - is_refreshing: False
2025-08-25 00:08:12 - INFO - 二维码过期检测: False
2025-08-25 00:08:12 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:08:12 - INFO - 返回登录状态检测结果:
2025-08-25 00:08:12 - INFO -   - qr_expired: False
2025-08-25 00:08:12 - INFO -   - in_personal_center: False
2025-08-25 00:08:12 - INFO -   - auto_refresh_needed: False
2025-08-25 00:08:12 - INFO -   - new_qr_code: None
2025-08-25 00:08:12 - INFO -   - is_refreshing: False
2025-08-25 00:08:17 - INFO - 二维码过期检测: False
2025-08-25 00:08:17 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:08:17 - INFO - 返回登录状态检测结果:
2025-08-25 00:08:17 - INFO -   - qr_expired: False
2025-08-25 00:08:17 - INFO -   - in_personal_center: False
2025-08-25 00:08:17 - INFO -   - auto_refresh_needed: False
2025-08-25 00:08:17 - INFO -   - new_qr_code: None
2025-08-25 00:08:17 - INFO -   - is_refreshing: False
2025-08-25 00:08:22 - INFO - 二维码过期检测: False
2025-08-25 00:08:22 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:08:22 - INFO - 返回登录状态检测结果:
2025-08-25 00:08:22 - INFO -   - qr_expired: False
2025-08-25 00:08:22 - INFO -   - in_personal_center: False
2025-08-25 00:08:22 - INFO -   - auto_refresh_needed: False
2025-08-25 00:08:22 - INFO -   - new_qr_code: None
2025-08-25 00:08:22 - INFO -   - is_refreshing: False
2025-08-25 00:08:27 - INFO - 二维码过期检测: False
2025-08-25 00:08:27 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:08:27 - INFO - 返回登录状态检测结果:
2025-08-25 00:08:27 - INFO -   - qr_expired: False
2025-08-25 00:08:27 - INFO -   - in_personal_center: False
2025-08-25 00:08:27 - INFO -   - auto_refresh_needed: False
2025-08-25 00:08:27 - INFO -   - new_qr_code: None
2025-08-25 00:08:27 - INFO -   - is_refreshing: False
2025-08-25 00:08:32 - INFO - 二维码过期检测: False
2025-08-25 00:08:32 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:08:32 - INFO - 返回登录状态检测结果:
2025-08-25 00:08:32 - INFO -   - qr_expired: False
2025-08-25 00:08:32 - INFO -   - in_personal_center: False
2025-08-25 00:08:32 - INFO -   - auto_refresh_needed: False
2025-08-25 00:08:32 - INFO -   - new_qr_code: None
2025-08-25 00:08:32 - INFO -   - is_refreshing: False
2025-08-25 00:08:37 - INFO - 二维码过期检测: False
2025-08-25 00:08:37 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:08:37 - INFO - 返回登录状态检测结果:
2025-08-25 00:08:37 - INFO -   - qr_expired: False
2025-08-25 00:08:37 - INFO -   - in_personal_center: False
2025-08-25 00:08:37 - INFO -   - auto_refresh_needed: False
2025-08-25 00:08:37 - INFO -   - new_qr_code: None
2025-08-25 00:08:37 - INFO -   - is_refreshing: False
2025-08-25 00:08:50 - INFO - 二维码过期检测: True
2025-08-25 00:08:50 - INFO - 🔄 检测到二维码过期，开始自动刷新...
2025-08-25 00:08:50 - INFO - ✅ 成功点击刷新按钮
2025-08-25 00:08:52 - INFO - 🔐 开始获取Boss直聘登录二维码...
2025-08-25 00:08:52 - INFO - current_url: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:08:52 - ERROR - ❌ 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:08:52 - ERROR - ❌ 自动刷新二维码失败: 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:08:52 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:08:52 - INFO - 返回登录状态检测结果:
2025-08-25 00:08:52 - INFO -   - qr_expired: False
2025-08-25 00:08:52 - INFO -   - in_personal_center: False
2025-08-25 00:08:52 - INFO -   - auto_refresh_needed: False
2025-08-25 00:08:52 - INFO -   - new_qr_code: None
2025-08-25 00:08:52 - INFO -   - is_refreshing: False
2025-08-25 00:09:36 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001A754570BE0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/bed3568fd516e9309e56d87658d68b1a/url
2025-08-25 00:09:41 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001A7544F3280>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/bed3568fd516e9309e56d87658d68b1a/url
2025-08-25 00:10:27 - INFO - 🚀 启动智能简历评估系统
2025-08-25 00:10:27 - INFO - 📋 系统功能:
2025-08-25 00:10:27 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 00:10:27 - INFO -   - 多线程并发处理
2025-08-25 00:10:27 - INFO -   - 实时进度监控
2025-08-25 00:10:27 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 00:10:27 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 00:10:28 - INFO - 多进程任务管理已初始化
2025-08-25 00:10:33 - INFO - 🔐 Boss直聘登录请求: login_boss
2025-08-25 00:10:33 - INFO - 日志系统初始化成功
2025-08-25 00:10:33 - INFO - 开始运行BOSS直聘自动化程序 V2.0...
2025-08-25 00:10:33 - INFO - ====== WebDriver manager ======
2025-08-25 00:10:37 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-25 00:10:37 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-25 00:10:38 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\139.0.7258.138\chromedriver-win32/chromedriver.exe] found in cache
2025-08-25 00:10:40 - INFO - 使用自动下载的chromedriver
2025-08-25 00:10:40 - INFO - Chrome浏览器驱动设置成功
2025-08-25 00:10:40 - INFO - 访问登录页面: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:10:41 - INFO - 🔐 开始获取Boss直聘登录二维码...
2025-08-25 00:10:42 - INFO - current_url: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:10:43 - INFO - ✅ 成功获取二维码URL
2025-08-25 00:10:43 - INFO - ✅ 成功获取Boss直聘登录二维码
2025-08-25 00:10:43 - INFO - 等待用户登录（超时时间：300秒）...
2025-08-25 00:10:43 - INFO - 请在浏览器中完成登录操作...
2025-08-25 00:10:49 - INFO - 二维码过期检测: False
2025-08-25 00:10:49 - INFO - 个人中心界面检测: False
2025-08-25 00:10:49 - INFO - 返回登录状态检测结果:
2025-08-25 00:10:49 - INFO -   - qr_expired: False
2025-08-25 00:10:49 - INFO -   - in_personal_center: False
2025-08-25 00:10:49 - INFO -   - auto_refresh_needed: False
2025-08-25 00:10:49 - INFO -   - new_qr_code: None
2025-08-25 00:10:49 - INFO -   - is_refreshing: False
2025-08-25 00:10:54 - INFO - 二维码过期检测: False
2025-08-25 00:10:54 - INFO - 个人中心界面检测: False
2025-08-25 00:10:54 - INFO - 返回登录状态检测结果:
2025-08-25 00:10:54 - INFO -   - qr_expired: False
2025-08-25 00:10:54 - INFO -   - in_personal_center: False
2025-08-25 00:10:54 - INFO -   - auto_refresh_needed: False
2025-08-25 00:10:54 - INFO -   - new_qr_code: None
2025-08-25 00:10:54 - INFO -   - is_refreshing: False
2025-08-25 00:10:59 - INFO - 二维码过期检测: False
2025-08-25 00:10:59 - INFO - 个人中心界面检测: False
2025-08-25 00:10:59 - INFO - 返回登录状态检测结果:
2025-08-25 00:10:59 - INFO -   - qr_expired: False
2025-08-25 00:10:59 - INFO -   - in_personal_center: False
2025-08-25 00:10:59 - INFO -   - auto_refresh_needed: False
2025-08-25 00:10:59 - INFO -   - new_qr_code: None
2025-08-25 00:10:59 - INFO -   - is_refreshing: False
2025-08-25 00:11:04 - INFO - 二维码过期检测: False
2025-08-25 00:11:04 - INFO - 个人中心界面检测: False
2025-08-25 00:11:04 - INFO - 返回登录状态检测结果:
2025-08-25 00:11:04 - INFO -   - qr_expired: False
2025-08-25 00:11:04 - INFO -   - in_personal_center: False
2025-08-25 00:11:04 - INFO -   - auto_refresh_needed: False
2025-08-25 00:11:04 - INFO -   - new_qr_code: None
2025-08-25 00:11:04 - INFO -   - is_refreshing: False
2025-08-25 00:11:09 - INFO - 二维码过期检测: False
2025-08-25 00:11:09 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-08-25 00:11:09 - INFO - 个人中心界面检测: False
2025-08-25 00:11:09 - INFO - 返回登录状态检测结果:
2025-08-25 00:11:09 - INFO -   - qr_expired: False
2025-08-25 00:11:09 - INFO -   - in_personal_center: False
2025-08-25 00:11:09 - INFO -   - auto_refresh_needed: False
2025-08-25 00:11:09 - INFO -   - new_qr_code: None
2025-08-25 00:11:09 - INFO -   - is_refreshing: False
2025-08-25 00:11:14 - INFO - 二维码过期检测: True
2025-08-25 00:11:14 - INFO - 🔄 检测到二维码过期，开始自动刷新...
2025-08-25 00:11:14 - INFO - ✅ 成功点击刷新按钮
2025-08-25 00:11:16 - INFO - 🔐 开始获取Boss直聘登录二维码...
2025-08-25 00:11:16 - INFO - current_url: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:11:16 - ERROR - ❌ 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:11:16 - ERROR - ❌ 自动刷新二维码失败: 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:11:16 - INFO - 个人中心界面检测: False
2025-08-25 00:11:16 - INFO - 返回登录状态检测结果:
2025-08-25 00:11:16 - INFO -   - qr_expired: True
2025-08-25 00:11:16 - INFO -   - in_personal_center: False
2025-08-25 00:11:16 - INFO -   - auto_refresh_needed: False
2025-08-25 00:11:16 - INFO -   - new_qr_code: None
2025-08-25 00:11:16 - INFO -   - is_refreshing: False
2025-08-25 00:11:19 - INFO - 二维码过期检测: False
2025-08-25 00:11:19 - INFO - 个人中心界面检测: False
2025-08-25 00:11:19 - INFO - 返回登录状态检测结果:
2025-08-25 00:11:19 - INFO -   - qr_expired: False
2025-08-25 00:11:19 - INFO -   - in_personal_center: False
2025-08-25 00:11:19 - INFO -   - auto_refresh_needed: False
2025-08-25 00:11:19 - INFO -   - new_qr_code: None
2025-08-25 00:11:19 - INFO -   - is_refreshing: False
2025-08-25 00:11:24 - INFO - 二维码过期检测: False
2025-08-25 00:11:24 - INFO - 个人中心界面检测: False
2025-08-25 00:11:24 - INFO - 返回登录状态检测结果:
2025-08-25 00:11:24 - INFO -   - qr_expired: False
2025-08-25 00:11:24 - INFO -   - in_personal_center: False
2025-08-25 00:11:24 - INFO -   - auto_refresh_needed: False
2025-08-25 00:11:24 - INFO -   - new_qr_code: None
2025-08-25 00:11:24 - INFO -   - is_refreshing: False
2025-08-25 00:11:29 - INFO - 二维码过期检测: False
2025-08-25 00:11:29 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-08-25 00:11:29 - INFO - 个人中心界面检测: False
2025-08-25 00:11:29 - INFO - 返回登录状态检测结果:
2025-08-25 00:11:29 - INFO -   - qr_expired: False
2025-08-25 00:11:29 - INFO -   - in_personal_center: False
2025-08-25 00:11:29 - INFO -   - auto_refresh_needed: False
2025-08-25 00:11:29 - INFO -   - new_qr_code: None
2025-08-25 00:11:29 - INFO -   - is_refreshing: False
2025-08-25 00:11:34 - INFO - 二维码过期检测: False
2025-08-25 00:11:34 - INFO - 个人中心界面检测: False
2025-08-25 00:11:34 - INFO - 返回登录状态检测结果:
2025-08-25 00:11:34 - INFO -   - qr_expired: False
2025-08-25 00:11:34 - INFO -   - in_personal_center: False
2025-08-25 00:11:34 - INFO -   - auto_refresh_needed: False
2025-08-25 00:11:34 - INFO -   - new_qr_code: None
2025-08-25 00:11:34 - INFO -   - is_refreshing: False
2025-08-25 00:11:39 - INFO - 二维码过期检测: False
2025-08-25 00:11:39 - INFO - 个人中心界面检测: False
2025-08-25 00:11:39 - INFO - 返回登录状态检测结果:
2025-08-25 00:11:39 - INFO -   - qr_expired: False
2025-08-25 00:11:39 - INFO -   - in_personal_center: False
2025-08-25 00:11:39 - INFO -   - auto_refresh_needed: False
2025-08-25 00:11:39 - INFO -   - new_qr_code: None
2025-08-25 00:11:39 - INFO -   - is_refreshing: False
2025-08-25 00:11:44 - INFO - 二维码过期检测: False
2025-08-25 00:11:44 - INFO - 个人中心界面检测: False
2025-08-25 00:11:44 - INFO - 返回登录状态检测结果:
2025-08-25 00:11:44 - INFO -   - qr_expired: False
2025-08-25 00:11:44 - INFO -   - in_personal_center: False
2025-08-25 00:11:44 - INFO -   - auto_refresh_needed: False
2025-08-25 00:11:44 - INFO -   - new_qr_code: None
2025-08-25 00:11:44 - INFO -   - is_refreshing: False
2025-08-25 00:11:49 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-08-25 00:11:49 - INFO - 二维码过期检测: True
2025-08-25 00:11:49 - INFO - 🔄 检测到二维码过期，开始自动刷新...
2025-08-25 00:11:49 - INFO - ✅ 成功点击刷新按钮
2025-08-25 00:11:51 - INFO - 🔐 开始获取Boss直聘登录二维码...
2025-08-25 00:11:51 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-08-25 00:11:51 - INFO - current_url: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:11:51 - ERROR - ❌ 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:11:51 - ERROR - ❌ 自动刷新二维码失败: 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:11:51 - INFO - 个人中心界面检测: False
2025-08-25 00:11:51 - INFO - 返回登录状态检测结果:
2025-08-25 00:11:51 - INFO -   - qr_expired: True
2025-08-25 00:11:51 - INFO -   - in_personal_center: False
2025-08-25 00:11:51 - INFO -   - auto_refresh_needed: False
2025-08-25 00:11:51 - INFO -   - new_qr_code: None
2025-08-25 00:11:51 - INFO -   - is_refreshing: False
2025-08-25 00:12:18 - INFO - 二维码过期检测: False
2025-08-25 00:12:18 - INFO - 个人中心界面检测: False
2025-08-25 00:12:18 - INFO - 返回登录状态检测结果:
2025-08-25 00:12:18 - INFO -   - qr_expired: False
2025-08-25 00:12:18 - INFO -   - in_personal_center: False
2025-08-25 00:12:18 - INFO -   - auto_refresh_needed: False
2025-08-25 00:12:18 - INFO -   - new_qr_code: None
2025-08-25 00:12:18 - INFO -   - is_refreshing: False
2025-08-25 00:12:24 - INFO - 二维码过期检测: True
2025-08-25 00:12:24 - INFO - 🔄 检测到二维码过期，开始自动刷新...
2025-08-25 00:12:24 - INFO - ✅ 成功点击刷新按钮
2025-08-25 00:12:24 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-08-25 00:12:26 - INFO - 🔐 开始获取Boss直聘登录二维码...
2025-08-25 00:12:27 - INFO - current_url: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:12:27 - ERROR - ❌ 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:12:27 - ERROR - ❌ 自动刷新二维码失败: 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:12:27 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-08-25 00:12:27 - INFO - 个人中心界面检测: False
2025-08-25 00:12:27 - INFO - 返回登录状态检测结果:
2025-08-25 00:12:27 - INFO -   - qr_expired: True
2025-08-25 00:12:27 - INFO -   - in_personal_center: False
2025-08-25 00:12:27 - INFO -   - auto_refresh_needed: False
2025-08-25 00:12:27 - INFO -   - new_qr_code: None
2025-08-25 00:12:27 - INFO -   - is_refreshing: False
2025-08-25 00:12:29 - INFO - 二维码过期检测: False
2025-08-25 00:12:29 - INFO - 个人中心界面检测: False
2025-08-25 00:12:29 - INFO - 返回登录状态检测结果:
2025-08-25 00:12:29 - INFO -   - qr_expired: False
2025-08-25 00:12:29 - INFO -   - in_personal_center: False
2025-08-25 00:12:29 - INFO -   - auto_refresh_needed: False
2025-08-25 00:12:29 - INFO -   - new_qr_code: None
2025-08-25 00:12:29 - INFO -   - is_refreshing: False
2025-08-25 00:12:34 - INFO - 二维码过期检测: False
2025-08-25 00:12:34 - INFO - 个人中心界面检测: False
2025-08-25 00:12:34 - INFO - 返回登录状态检测结果:
2025-08-25 00:12:34 - INFO -   - qr_expired: False
2025-08-25 00:12:34 - INFO -   - in_personal_center: False
2025-08-25 00:12:34 - INFO -   - auto_refresh_needed: False
2025-08-25 00:12:34 - INFO -   - new_qr_code: None
2025-08-25 00:12:34 - INFO -   - is_refreshing: False
2025-08-25 00:12:39 - INFO - 二维码过期检测: False
2025-08-25 00:12:39 - INFO - 个人中心界面检测: False
2025-08-25 00:12:39 - INFO - 返回登录状态检测结果:
2025-08-25 00:12:39 - INFO -   - qr_expired: False
2025-08-25 00:12:39 - INFO -   - in_personal_center: False
2025-08-25 00:12:39 - INFO -   - auto_refresh_needed: False
2025-08-25 00:12:39 - INFO -   - new_qr_code: None
2025-08-25 00:12:39 - INFO -   - is_refreshing: False
2025-08-25 00:12:44 - INFO - 二维码过期检测: False
2025-08-25 00:12:44 - INFO - 个人中心界面检测: False
2025-08-25 00:12:44 - INFO - 返回登录状态检测结果:
2025-08-25 00:12:44 - INFO -   - qr_expired: False
2025-08-25 00:12:44 - INFO -   - in_personal_center: False
2025-08-25 00:12:44 - INFO -   - auto_refresh_needed: False
2025-08-25 00:12:44 - INFO -   - new_qr_code: None
2025-08-25 00:12:44 - INFO -   - is_refreshing: False
2025-08-25 00:12:49 - INFO - 二维码过期检测: False
2025-08-25 00:12:49 - INFO - 个人中心界面检测: False
2025-08-25 00:12:49 - INFO - 返回登录状态检测结果:
2025-08-25 00:12:49 - INFO -   - qr_expired: False
2025-08-25 00:12:49 - INFO -   - in_personal_center: False
2025-08-25 00:12:49 - INFO -   - auto_refresh_needed: False
2025-08-25 00:12:49 - INFO -   - new_qr_code: None
2025-08-25 00:12:49 - INFO -   - is_refreshing: False
2025-08-25 00:12:54 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-08-25 00:12:54 - INFO - 二维码过期检测: False
2025-08-25 00:12:54 - INFO - 个人中心界面检测: False
2025-08-25 00:12:54 - INFO - 返回登录状态检测结果:
2025-08-25 00:12:54 - INFO -   - qr_expired: False
2025-08-25 00:12:54 - INFO -   - in_personal_center: False
2025-08-25 00:12:54 - INFO -   - auto_refresh_needed: False
2025-08-25 00:12:54 - INFO -   - new_qr_code: None
2025-08-25 00:12:54 - INFO -   - is_refreshing: False
2025-08-25 00:12:59 - INFO - 二维码过期检测: True
2025-08-25 00:12:59 - INFO - 🔄 检测到二维码过期，开始自动刷新...
2025-08-25 00:12:59 - INFO - ✅ 成功点击刷新按钮
2025-08-25 00:13:01 - INFO - 🔐 开始获取Boss直聘登录二维码...
2025-08-25 00:13:01 - INFO - current_url: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:13:01 - ERROR - ❌ 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:13:01 - ERROR - ❌ 自动刷新二维码失败: 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:13:01 - INFO - 个人中心界面检测: False
2025-08-25 00:13:01 - INFO - 返回登录状态检测结果:
2025-08-25 00:13:01 - INFO -   - qr_expired: True
2025-08-25 00:13:01 - INFO -   - in_personal_center: False
2025-08-25 00:13:01 - INFO -   - auto_refresh_needed: False
2025-08-25 00:13:01 - INFO -   - new_qr_code: None
2025-08-25 00:13:01 - INFO -   - is_refreshing: False
2025-08-25 00:13:04 - INFO - 二维码过期检测: False
2025-08-25 00:13:04 - INFO - 个人中心界面检测: False
2025-08-25 00:13:04 - INFO - 返回登录状态检测结果:
2025-08-25 00:13:04 - INFO -   - qr_expired: False
2025-08-25 00:13:04 - INFO -   - in_personal_center: False
2025-08-25 00:13:04 - INFO -   - auto_refresh_needed: False
2025-08-25 00:13:04 - INFO -   - new_qr_code: None
2025-08-25 00:13:04 - INFO -   - is_refreshing: False
2025-08-25 00:13:09 - INFO - 二维码过期检测: False
2025-08-25 00:13:09 - INFO - 个人中心界面检测: False
2025-08-25 00:13:09 - INFO - 返回登录状态检测结果:
2025-08-25 00:13:09 - INFO -   - qr_expired: False
2025-08-25 00:13:09 - INFO -   - in_personal_center: False
2025-08-25 00:13:09 - INFO -   - auto_refresh_needed: False
2025-08-25 00:13:09 - INFO -   - new_qr_code: None
2025-08-25 00:13:09 - INFO -   - is_refreshing: False
2025-08-25 00:13:14 - INFO - 二维码过期检测: False
2025-08-25 00:13:14 - INFO - 个人中心界面检测: False
2025-08-25 00:13:14 - INFO - 返回登录状态检测结果:
2025-08-25 00:13:14 - INFO -   - qr_expired: False
2025-08-25 00:13:14 - INFO -   - in_personal_center: False
2025-08-25 00:13:14 - INFO -   - auto_refresh_needed: False
2025-08-25 00:13:14 - INFO -   - new_qr_code: None
2025-08-25 00:13:14 - INFO -   - is_refreshing: False
2025-08-25 00:13:19 - INFO - 二维码过期检测: False
2025-08-25 00:13:19 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-08-25 00:13:19 - INFO - 个人中心界面检测: False
2025-08-25 00:13:19 - INFO - 返回登录状态检测结果:
2025-08-25 00:13:19 - INFO -   - qr_expired: False
2025-08-25 00:13:19 - INFO -   - in_personal_center: False
2025-08-25 00:13:19 - INFO -   - auto_refresh_needed: False
2025-08-25 00:13:19 - INFO -   - new_qr_code: None
2025-08-25 00:13:19 - INFO -   - is_refreshing: False
2025-08-25 00:13:50 - INFO - 二维码过期检测: True
2025-08-25 00:13:50 - INFO - 🔄 检测到二维码过期，开始自动刷新...
2025-08-25 00:13:50 - INFO - ✅ 成功点击刷新按钮
2025-08-25 00:13:52 - INFO - 🔐 开始获取Boss直聘登录二维码...
2025-08-25 00:13:52 - INFO - current_url: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:13:52 - ERROR - ❌ 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:13:52 - ERROR - ❌ 自动刷新二维码失败: 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:13:52 - INFO - 个人中心界面检测: False
2025-08-25 00:13:52 - INFO - 返回登录状态检测结果:
2025-08-25 00:13:52 - INFO -   - qr_expired: True
2025-08-25 00:13:52 - INFO -   - in_personal_center: False
2025-08-25 00:13:52 - INFO -   - auto_refresh_needed: False
2025-08-25 00:13:52 - INFO -   - new_qr_code: None
2025-08-25 00:13:52 - INFO -   - is_refreshing: False
2025-08-25 00:14:50 - INFO - 二维码过期检测: True
2025-08-25 00:14:50 - INFO - 🔄 检测到二维码过期，开始自动刷新...
2025-08-25 00:14:50 - INFO - ✅ 成功点击刷新按钮
2025-08-25 00:14:52 - INFO - 🔐 开始获取Boss直聘登录二维码...
2025-08-25 00:14:53 - INFO - current_url: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:14:53 - ERROR - ❌ 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:14:53 - ERROR - ❌ 自动刷新二维码失败: 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:14:53 - INFO - 个人中心界面检测: False
2025-08-25 00:14:53 - INFO - 返回登录状态检测结果:
2025-08-25 00:14:53 - INFO -   - qr_expired: True
2025-08-25 00:14:53 - INFO -   - in_personal_center: False
2025-08-25 00:14:53 - INFO -   - auto_refresh_needed: False
2025-08-25 00:14:53 - INFO -   - new_qr_code: None
2025-08-25 00:14:53 - INFO -   - is_refreshing: False
2025-08-25 00:15:44 - ERROR - 等待用户登录超时（300秒）
2025-08-25 00:15:44 - ERROR - 用户登录失败或超时
2025-08-25 00:15:44 - INFO - 自动化程序执行完成，浏览器将保持打开状态
2025-08-25 00:15:44 - INFO - 您可以手动查看页面内容，完成后请关闭浏览器
2025-08-25 00:15:50 - INFO - 二维码过期检测: True
2025-08-25 00:15:50 - INFO - 🔄 检测到二维码过期，开始自动刷新...
2025-08-25 00:15:50 - INFO - ✅ 成功点击刷新按钮
2025-08-25 00:15:52 - INFO - 🔐 开始获取Boss直聘登录二维码...
2025-08-25 00:15:52 - INFO - current_url: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:15:52 - ERROR - ❌ 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:15:52 - ERROR - ❌ 自动刷新二维码失败: 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:15:52 - INFO - 个人中心界面检测: False
2025-08-25 00:15:52 - INFO - 返回登录状态检测结果:
2025-08-25 00:15:52 - INFO -   - qr_expired: True
2025-08-25 00:15:52 - INFO -   - in_personal_center: False
2025-08-25 00:15:52 - INFO -   - auto_refresh_needed: False
2025-08-25 00:15:52 - INFO -   - new_qr_code: None
2025-08-25 00:15:52 - INFO -   - is_refreshing: False
2025-08-25 00:16:50 - INFO - 二维码过期检测: True
2025-08-25 00:16:50 - INFO - 🔄 检测到二维码过期，开始自动刷新...
2025-08-25 00:16:50 - INFO - ✅ 成功点击刷新按钮
2025-08-25 00:16:52 - INFO - 🔐 开始获取Boss直聘登录二维码...
2025-08-25 00:16:53 - INFO - current_url: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:16:53 - ERROR - ❌ 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:16:53 - ERROR - ❌ 自动刷新二维码失败: 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:16:53 - INFO - 个人中心界面检测: False
2025-08-25 00:16:53 - INFO - 返回登录状态检测结果:
2025-08-25 00:16:53 - INFO -   - qr_expired: True
2025-08-25 00:16:53 - INFO -   - in_personal_center: False
2025-08-25 00:16:53 - INFO -   - auto_refresh_needed: False
2025-08-25 00:16:53 - INFO -   - new_qr_code: None
2025-08-25 00:16:53 - INFO -   - is_refreshing: False
2025-08-25 00:16:53 - INFO - 二维码过期检测: False
2025-08-25 00:16:53 - INFO - 个人中心界面检测: False
2025-08-25 00:16:53 - INFO - 返回登录状态检测结果:
2025-08-25 00:16:53 - INFO -   - qr_expired: False
2025-08-25 00:16:53 - INFO -   - in_personal_center: False
2025-08-25 00:16:53 - INFO -   - auto_refresh_needed: False
2025-08-25 00:16:53 - INFO -   - new_qr_code: None
2025-08-25 00:16:53 - INFO -   - is_refreshing: False
2025-08-25 00:16:58 - INFO - 二维码过期检测: False
2025-08-25 00:16:58 - INFO - 个人中心界面检测: False
2025-08-25 00:16:58 - INFO - 返回登录状态检测结果:
2025-08-25 00:16:58 - INFO -   - qr_expired: False
2025-08-25 00:16:58 - INFO -   - in_personal_center: False
2025-08-25 00:16:58 - INFO -   - auto_refresh_needed: False
2025-08-25 00:16:58 - INFO -   - new_qr_code: None
2025-08-25 00:16:58 - INFO -   - is_refreshing: False
2025-08-25 00:17:03 - INFO - 二维码过期检测: False
2025-08-25 00:17:03 - INFO - 个人中心界面检测: False
2025-08-25 00:17:03 - INFO - 返回登录状态检测结果:
2025-08-25 00:17:03 - INFO -   - qr_expired: False
2025-08-25 00:17:03 - INFO -   - in_personal_center: False
2025-08-25 00:17:03 - INFO -   - auto_refresh_needed: False
2025-08-25 00:17:03 - INFO -   - new_qr_code: None
2025-08-25 00:17:03 - INFO -   - is_refreshing: False
2025-08-25 00:17:09 - INFO - 二维码过期检测: False
2025-08-25 00:17:09 - INFO - 个人中心界面检测: False
2025-08-25 00:17:09 - INFO - 返回登录状态检测结果:
2025-08-25 00:17:09 - INFO -   - qr_expired: False
2025-08-25 00:17:09 - INFO -   - in_personal_center: False
2025-08-25 00:17:09 - INFO -   - auto_refresh_needed: False
2025-08-25 00:17:09 - INFO -   - new_qr_code: None
2025-08-25 00:17:09 - INFO -   - is_refreshing: False
2025-08-25 00:17:14 - INFO - 二维码过期检测: False
2025-08-25 00:17:14 - INFO - 个人中心界面检测: False
2025-08-25 00:17:14 - INFO - 返回登录状态检测结果:
2025-08-25 00:17:14 - INFO -   - qr_expired: False
2025-08-25 00:17:14 - INFO -   - in_personal_center: False
2025-08-25 00:17:14 - INFO -   - auto_refresh_needed: False
2025-08-25 00:17:14 - INFO -   - new_qr_code: None
2025-08-25 00:17:14 - INFO -   - is_refreshing: False
2025-08-25 00:17:19 - INFO - 二维码过期检测: False
2025-08-25 00:17:19 - INFO - 个人中心界面检测: False
2025-08-25 00:17:19 - INFO - 返回登录状态检测结果:
2025-08-25 00:17:19 - INFO -   - qr_expired: False
2025-08-25 00:17:19 - INFO -   - in_personal_center: False
2025-08-25 00:17:19 - INFO -   - auto_refresh_needed: False
2025-08-25 00:17:19 - INFO -   - new_qr_code: None
2025-08-25 00:17:19 - INFO -   - is_refreshing: False
2025-08-25 00:17:24 - INFO - 二维码过期检测: True
2025-08-25 00:17:24 - INFO - 🔄 检测到二维码过期，开始自动刷新...
2025-08-25 00:17:24 - INFO - ✅ 成功点击刷新按钮
2025-08-25 00:17:26 - INFO - 🔐 开始获取Boss直聘登录二维码...
2025-08-25 00:17:30 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000024427D6F6A0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/8013a947c9c80d302b5573c7a78339f3/url
2025-08-25 00:17:45 - INFO - 🚀 启动智能简历评估系统
2025-08-25 00:17:45 - INFO - 📋 系统功能:
2025-08-25 00:17:45 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 00:17:45 - INFO -   - 多线程并发处理
2025-08-25 00:17:45 - INFO -   - 实时进度监控
2025-08-25 00:17:45 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 00:17:45 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 00:17:46 - INFO - 多进程任务管理已初始化
2025-08-25 00:17:51 - INFO - 🔐 Boss直聘登录请求: login_boss
2025-08-25 00:17:51 - INFO - 日志系统初始化成功
2025-08-25 00:17:51 - INFO - 开始运行BOSS直聘自动化程序 V2.0...
2025-08-25 00:17:51 - INFO - ====== WebDriver manager ======
2025-08-25 00:17:54 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-25 00:17:55 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-25 00:17:56 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\139.0.7258.138\chromedriver-win32/chromedriver.exe] found in cache
2025-08-25 00:17:58 - INFO - 使用自动下载的chromedriver
2025-08-25 00:17:58 - INFO - Chrome浏览器驱动设置成功
2025-08-25 00:17:58 - INFO - 访问登录页面: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:17:59 - INFO - 🔐 开始获取Boss直聘登录二维码...
2025-08-25 00:18:00 - INFO - current_url: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:18:01 - INFO - ✅ 成功获取二维码URL
2025-08-25 00:18:01 - INFO - ✅ 成功获取Boss直聘登录二维码
2025-08-25 00:18:01 - INFO - 等待用户登录（超时时间：300秒）...
2025-08-25 00:18:01 - INFO - 请在浏览器中完成登录操作...
2025-08-25 00:18:07 - INFO - 二维码过期检测: False
2025-08-25 00:18:07 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:18:12 - INFO - 二维码过期检测: False
2025-08-25 00:18:12 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:18:17 - INFO - 二维码过期检测: False
2025-08-25 00:18:17 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:18:22 - INFO - 二维码过期检测: False
2025-08-25 00:18:22 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:18:31 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002695287FB20>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/fadbdb6d96f485435f7351e4cdbe38fa/url
2025-08-25 00:18:35 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002695277CAC0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/fadbdb6d96f485435f7351e4cdbe38fa/url
2025-08-25 00:18:58 - INFO - 🚀 启动智能简历评估系统
2025-08-25 00:18:58 - INFO - 📋 系统功能:
2025-08-25 00:18:58 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 00:18:58 - INFO -   - 多线程并发处理
2025-08-25 00:18:58 - INFO -   - 实时进度监控
2025-08-25 00:18:58 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 00:18:58 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 00:18:59 - INFO - 多进程任务管理已初始化
2025-08-25 00:19:05 - INFO - 🔐 Boss直聘登录请求: login_boss
2025-08-25 00:19:05 - INFO - 日志系统初始化成功
2025-08-25 00:19:05 - INFO - 开始运行BOSS直聘自动化程序 V2.0...
2025-08-25 00:19:05 - INFO - ====== WebDriver manager ======
2025-08-25 00:19:08 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-25 00:19:09 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-25 00:19:09 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\139.0.7258.138\chromedriver-win32/chromedriver.exe] found in cache
2025-08-25 00:19:11 - INFO - 使用自动下载的chromedriver
2025-08-25 00:19:11 - INFO - Chrome浏览器驱动设置成功
2025-08-25 00:19:11 - INFO - 访问登录页面: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:19:13 - INFO - 🔐 开始获取Boss直聘登录二维码...
2025-08-25 00:19:13 - INFO - current_url: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:19:14 - INFO - ✅ 成功获取二维码URL
2025-08-25 00:19:14 - INFO - ✅ 成功获取Boss直聘登录二维码
2025-08-25 00:19:14 - INFO - 等待用户登录（超时时间：300秒）...
2025-08-25 00:19:14 - INFO - 请在浏览器中完成登录操作...
2025-08-25 00:19:20 - INFO - 二维码过期检测: False
2025-08-25 00:19:20 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:19:25 - INFO - 二维码过期检测: False
2025-08-25 00:19:25 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:19:30 - INFO - 二维码过期检测: False
2025-08-25 00:19:30 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:19:35 - INFO - 二维码过期检测: False
2025-08-25 00:19:35 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:19:40 - INFO - 二维码过期检测: False
2025-08-25 00:19:40 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:19:45 - INFO - 二维码过期检测: True
2025-08-25 00:19:45 - INFO - 🔄 检测到二维码过期，开始自动刷新...
2025-08-25 00:19:45 - INFO - ✅ 成功点击刷新按钮
2025-08-25 00:19:47 - INFO - 🔐 开始获取Boss直聘登录二维码...
2025-08-25 00:19:47 - INFO - current_url: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:19:47 - ERROR - ❌ 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:19:47 - ERROR - ❌ 自动刷新二维码失败: 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:19:47 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:19:50 - INFO - 二维码过期检测: False
2025-08-25 00:19:50 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:19:55 - INFO - 二维码过期检测: False
2025-08-25 00:19:55 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:20:03 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017C3050FAF0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/aa3d85ba7f000110cba064f0517d59a2/url
2025-08-25 00:20:10 - INFO - 🚀 启动智能简历评估系统
2025-08-25 00:20:10 - INFO - 📋 系统功能:
2025-08-25 00:20:10 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 00:20:10 - INFO -   - 多线程并发处理
2025-08-25 00:20:10 - INFO -   - 实时进度监控
2025-08-25 00:20:10 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 00:20:10 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 00:20:11 - INFO - 多进程任务管理已初始化
2025-08-25 00:20:16 - INFO - 🔐 Boss直聘登录请求: login_boss
2025-08-25 00:20:16 - INFO - 日志系统初始化成功
2025-08-25 00:20:16 - INFO - 开始运行BOSS直聘自动化程序 V2.0...
2025-08-25 00:20:16 - INFO - ====== WebDriver manager ======
2025-08-25 00:20:19 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-25 00:20:20 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-25 00:20:21 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\139.0.7258.138\chromedriver-win32/chromedriver.exe] found in cache
2025-08-25 00:20:22 - INFO - 使用自动下载的chromedriver
2025-08-25 00:20:22 - INFO - Chrome浏览器驱动设置成功
2025-08-25 00:20:22 - INFO - 访问登录页面: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:20:24 - INFO - 🔐 开始获取Boss直聘登录二维码...
2025-08-25 00:20:24 - INFO - current_url: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:20:26 - INFO - ✅ 成功获取二维码URL
2025-08-25 00:20:26 - INFO - ✅ 成功获取Boss直聘登录二维码
2025-08-25 00:20:26 - INFO - 等待用户登录（超时时间：300秒）...
2025-08-25 00:20:26 - INFO - 请在浏览器中完成登录操作...
2025-08-25 00:20:32 - INFO - 二维码过期检测: False
2025-08-25 00:20:32 - INFO - 个人中心界面检测: False
2025-08-25 00:20:37 - INFO - 二维码过期检测: False
2025-08-25 00:20:37 - INFO - 个人中心界面检测: False
2025-08-25 00:20:41 - INFO - 二维码过期检测: False
2025-08-25 00:20:41 - INFO - 个人中心界面检测: False
2025-08-25 00:20:46 - INFO - 二维码过期检测: False
2025-08-25 00:20:46 - INFO - 个人中心界面检测: False
2025-08-25 00:20:52 - INFO - 二维码过期检测: False
2025-08-25 00:20:52 - INFO - 个人中心界面检测: False
2025-08-25 00:20:56 - INFO - 二维码过期检测: True
2025-08-25 00:20:56 - INFO - 🔄 检测到二维码过期，开始自动刷新...
2025-08-25 00:20:56 - INFO - ✅ 成功点击刷新按钮
2025-08-25 00:20:58 - INFO - 🔐 开始获取Boss直聘登录二维码...
2025-08-25 00:20:58 - INFO - current_url: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:20:58 - ERROR - ❌ 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:20:58 - ERROR - ❌ 自动刷新二维码失败: 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:20:58 - INFO - 个人中心界面检测: False
2025-08-25 00:21:01 - INFO - 二维码过期检测: False
2025-08-25 00:21:01 - INFO - 个人中心界面检测: False
2025-08-25 00:21:06 - INFO - 二维码过期检测: False
2025-08-25 00:21:06 - INFO - 个人中心界面检测: False
2025-08-25 00:21:11 - INFO - 二维码过期检测: False
2025-08-25 00:21:11 - INFO - 个人中心界面检测: False
2025-08-25 00:21:17 - INFO - 二维码过期检测: False
2025-08-25 00:21:17 - INFO - 个人中心界面检测: False
2025-08-25 00:21:22 - INFO - 二维码过期检测: False
2025-08-25 00:21:22 - INFO - 个人中心界面检测: False
2025-08-25 00:21:26 - INFO - 二维码过期检测: False
2025-08-25 00:21:26 - INFO - 个人中心界面检测: False
2025-08-25 00:21:32 - INFO - 二维码过期检测: True
2025-08-25 00:21:32 - INFO - 🔄 检测到二维码过期，开始自动刷新...
2025-08-25 00:21:32 - INFO - ✅ 成功点击刷新按钮
2025-08-25 00:21:34 - INFO - 🔐 开始获取Boss直聘登录二维码...
2025-08-25 00:21:35 - INFO - current_url: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:21:35 - ERROR - ❌ 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:21:35 - ERROR - ❌ 自动刷新二维码失败: 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:21:35 - INFO - 个人中心界面检测: False
2025-08-25 00:21:37 - INFO - 二维码过期检测: False
2025-08-25 00:21:37 - INFO - 个人中心界面检测: False
2025-08-25 00:21:41 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000013B0EA3FAF0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/846d2f5cea2e94e0544ae3363b2363b3/url
2025-08-25 00:21:53 - INFO - 🚀 启动智能简历评估系统
2025-08-25 00:21:53 - INFO - 📋 系统功能:
2025-08-25 00:21:53 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 00:21:53 - INFO -   - 多线程并发处理
2025-08-25 00:21:53 - INFO -   - 实时进度监控
2025-08-25 00:21:53 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 00:21:53 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 00:21:54 - INFO - 多进程任务管理已初始化
2025-08-25 00:21:59 - INFO - 🔐 Boss直聘登录请求: login_boss
2025-08-25 00:21:59 - INFO - 日志系统初始化成功
2025-08-25 00:21:59 - INFO - 开始运行BOSS直聘自动化程序 V2.0...
2025-08-25 00:21:59 - INFO - ====== WebDriver manager ======
2025-08-25 00:22:02 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-25 00:22:02 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-25 00:22:03 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\139.0.7258.138\chromedriver-win32/chromedriver.exe] found in cache
2025-08-25 00:22:05 - INFO - 使用自动下载的chromedriver
2025-08-25 00:22:05 - INFO - Chrome浏览器驱动设置成功
2025-08-25 00:22:05 - INFO - 访问登录页面: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:22:06 - INFO - 🔐 开始获取Boss直聘登录二维码...
2025-08-25 00:22:07 - INFO - current_url: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:22:08 - INFO - ✅ 成功获取二维码URL
2025-08-25 00:22:08 - INFO - ✅ 成功获取Boss直聘登录二维码
2025-08-25 00:22:08 - INFO - 等待用户登录（超时时间：300秒）...
2025-08-25 00:22:08 - INFO - 请在浏览器中完成登录操作...
2025-08-25 00:22:14 - INFO - 二维码过期检测: False
2025-08-25 00:22:14 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:22:19 - INFO - 二维码过期检测: False
2025-08-25 00:22:19 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:22:24 - INFO - 二维码过期检测: False
2025-08-25 00:22:24 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:22:29 - INFO - 二维码过期检测: False
2025-08-25 00:22:29 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:22:34 - INFO - 二维码过期检测: False
2025-08-25 00:22:34 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:22:38 - INFO - 二维码过期检测: True
2025-08-25 00:22:38 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:22:44 - INFO - 二维码过期检测: True
2025-08-25 00:22:44 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:22:49 - INFO - 二维码过期检测: False
2025-08-25 00:22:49 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:22:54 - INFO - 二维码过期检测: False
2025-08-25 00:22:54 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:22:59 - INFO - 二维码过期检测: False
2025-08-25 00:22:59 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:23:04 - INFO - 二维码过期检测: False
2025-08-25 00:23:04 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:23:09 - INFO - 二维码过期检测: False
2025-08-25 00:23:09 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:23:14 - INFO - 二维码过期检测: False
2025-08-25 00:23:14 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:23:19 - INFO - 二维码过期检测: True
2025-08-25 00:23:19 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:23:24 - INFO - 二维码过期检测: True
2025-08-25 00:23:24 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:23:29 - INFO - 二维码过期检测: True
2025-08-25 00:23:29 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:23:34 - INFO - 二维码过期检测: True
2025-08-25 00:23:34 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:23:39 - INFO - 二维码过期检测: True
2025-08-25 00:23:39 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:23:44 - INFO - 二维码过期检测: True
2025-08-25 00:23:44 - WARNING - 检测页面状态失败: too many values to unpack (expected 2)
2025-08-25 00:23:51 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000208776130A0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/fa0a8e85153dace15edfabcefe2d81f8/url
2025-08-25 00:24:04 - INFO - 🚀 启动智能简历评估系统
2025-08-25 00:24:04 - INFO - 📋 系统功能:
2025-08-25 00:24:04 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 00:24:04 - INFO -   - 多线程并发处理
2025-08-25 00:24:04 - INFO -   - 实时进度监控
2025-08-25 00:24:04 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 00:24:04 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 00:24:05 - INFO - 多进程任务管理已初始化
2025-08-25 00:25:50 - INFO - 🚀 启动智能简历评估系统
2025-08-25 00:25:50 - INFO - 📋 系统功能:
2025-08-25 00:25:50 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 00:25:50 - INFO -   - 多线程并发处理
2025-08-25 00:25:50 - INFO -   - 实时进度监控
2025-08-25 00:25:50 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 00:25:50 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 00:25:51 - INFO - 多进程任务管理已初始化
2025-08-25 00:25:56 - INFO - 🔐 Boss直聘登录请求: login_boss
2025-08-25 00:25:56 - INFO - 日志系统初始化成功
2025-08-25 00:25:56 - INFO - 开始运行BOSS直聘自动化程序 V2.0...
2025-08-25 00:25:56 - INFO - ====== WebDriver manager ======
2025-08-25 00:25:59 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-25 00:26:00 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-25 00:26:01 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\139.0.7258.138\chromedriver-win32/chromedriver.exe] found in cache
2025-08-25 00:26:03 - INFO - 使用自动下载的chromedriver
2025-08-25 00:26:03 - INFO - Chrome浏览器驱动设置成功
2025-08-25 00:26:03 - INFO - 访问登录页面: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:26:05 - INFO - 🔐 开始获取Boss直聘登录二维码...
2025-08-25 00:26:05 - INFO - current_url: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:26:07 - INFO - ✅ 成功获取二维码URL
2025-08-25 00:26:07 - INFO - ✅ 成功获取Boss直聘登录二维码
2025-08-25 00:26:07 - INFO - 等待用户登录（超时时间：300秒）...
2025-08-25 00:26:07 - INFO - 请在浏览器中完成登录操作...
2025-08-25 00:26:37 - INFO - 检测到二维码过期按钮，自动点击刷新
2025-08-25 00:26:37 - INFO - 成功点击二维码刷新按钮
2025-08-25 00:26:39 - INFO - 🔐 开始获取Boss直聘登录二维码...
2025-08-25 00:26:39 - INFO - current_url: https://www.zhipin.com/web/user/?ka=header-login
2025-08-25 00:26:39 - ERROR - ❌ 获取登录二维码异常: Message: no such element: Unable to locate element: {"method":"css selector","selector":"#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div"}
  (Session info: chrome=139.0.7258.128); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x93ffc3+65331]
	GetHandleVerifier [0x0x940004+65396]
	(No symbol) [0x0x733f63]
	(No symbol) [0x0x77c99e]
	(No symbol) [0x0x77cd3b]
	(No symbol) [0x0x7c53d2]
	(No symbol) [0x0x7a1524]
	(No symbol) [0x0x7c2bcb]
	(No symbol) [0x0x7a12d6]
	(No symbol) [0x0x770910]
	(No symbol) [0x0x771784]
	GetHandleVerifier [0x0xb838b3+2439203]
	GetHandleVerifier [0x0xb7eae2+2419282]
	GetHandleVerifier [0x0x96712a+225434]
	GetHandleVerifier [0x0x956e08+159096]
	GetHandleVerifier [0x0x95dd5d+187597]
	GetHandleVerifier [0x0x947ad8+96840]
	GetHandleVerifier [0x0x947c62+97234]
	GetHandleVerifier [0x0x93277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-25 00:26:39 - WARNING - 获取新二维码失败
2025-08-25 00:27:12 - INFO - 检测到二维码过期按钮，自动点击刷新
2025-08-25 00:27:12 - INFO - 成功点击二维码刷新按钮
2025-08-25 00:27:14 - INFO - 🔐 开始获取Boss直聘登录二维码...
2025-08-25 00:27:16 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000241F0515AB0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/3a66ce8f4f4f057dba500eb076e056b8/url
2025-08-25 09:32:18 - INFO - 🚀 启动智能简历评估系统
2025-08-25 09:32:18 - INFO - 📋 系统功能:
2025-08-25 09:32:18 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 09:32:18 - INFO -   - 多线程并发处理
2025-08-25 09:32:18 - INFO -   - 实时进度监控
2025-08-25 09:32:18 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 09:32:18 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 09:32:20 - INFO - 多进程任务管理已初始化
2025-08-25 09:32:52 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 09:32:52 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 09:32:52 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 09:36:39 - INFO - 🚀 启动智能简历评估系统
2025-08-25 09:36:39 - INFO - 📋 系统功能:
2025-08-25 09:36:39 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 09:36:39 - INFO -   - 多线程并发处理
2025-08-25 09:36:39 - INFO -   - 实时进度监控
2025-08-25 09:36:39 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 09:36:39 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 09:36:41 - INFO - 多进程任务管理已初始化
2025-08-25 09:36:43 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 09:36:43 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 09:36:43 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 09:37:10 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 09:37:10 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/JD_md转json.md
2025-08-25 09:37:15 - INFO - ✅ 成功将MD格式JD转换为JSON格式并保存到用户目录: ./user/未知用户/jd\AI产品经理.json
2025-08-25 09:37:16 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 09:37:16 - INFO - ✅ 发现已存在的JSON文件: ./user/杨锐雄/jd\AI产品经理.json
2025-08-25 09:37:16 - INFO - ✅ JSON文件格式验证通过
2025-08-25 09:37:17 - INFO - ⚠️ 跳过同名候选人: 安佰丽，文件已存在
2025-08-25 09:37:17 - INFO - 开始处理简历: 安佰丽
2025-08-25 09:37:17 - INFO - 开始第1轮评估: 安佰丽
2025-08-25 09:37:17 - INFO - 岗位名称: AI产品经理
2025-08-25 09:37:17 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 09:37:17 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 09:37:45 - INFO - ✅ 第1轮评估完成: 安佰丽 | 分数: 0.0
2025-08-25 09:37:45 - INFO - 检测到不合格关键词: 不匹配
2025-08-25 09:37:45 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_09：37_AI产品经理_一二点五三轮筛选结果/初筛不合格\未通过_安佰丽_第1轮_评估报告.md
2025-08-25 09:37:45 - INFO - 开始提取候选人 安佰丽 的初筛报告信息
2025-08-25 09:37:45 - INFO - 找到优势文本: **优势:**  
    - 本科学历符合要求  
    - 具备良好的组织协调能力与流程管理经验（体现在人力资源管理中）

- 
2025-08-25 09:37:45 - INFO - 找到风险文本: **风险与不足:**  
    - **潜在风险：** 期望薪资略低于JD薪资范围下限，但整体处于边缘状态  
    - **核心职责匹配度低：** 完全缺乏AI产品管理所需经验与技能  
    - **无相关项目经验：** 简历中未体现任何AI、产品设计、市场分析、用户研究相关内容  
    - **职业路径不匹配：** 长期从事人力资源与行政工作，未体现转型或交叉能力

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。 → **不适用**
    2. 核心职责匹配度 评估结果为 "**低**"。 → ✅ 成立

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。 → ✅ 成立  
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。 → ❌ 不成立

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人虽满足学历要求，且无硬性“不匹配”项，但其**核心职责匹配度为低**，完全缺乏AI产品管理所需经验与能力，且职业路径与岗位要求严重不匹配。此外，薪资虽未超出上限，但已接近JD下限，标记为潜在风险。
2025-08-25 09:37:45 - INFO - 提取的优势和风险: 优势=本科学历符合要求  ；具备良好的组织协调能力与流程管理经验（体现在人力资源管理中）, 风险=**潜在风险：** 期望薪资略低于JD薪资范围下限，但整体处于边缘状态  ；**核心职责匹配度低：** 完全缺乏AI产品管理所需经验与技能  
2025-08-25 09:37:45 - INFO - 提取到姓名: 安伯丽
2025-08-25 09:37:45 - INFO - 提取到年龄: 40岁
2025-08-25 09:37:45 - INFO - 提取到工作经验: 超过8年人力资源及行政管理经验，未涉及AI或产品管理相关经历
2025-08-25 09:37:45 - INFO - 提取到学历: 本科（法学专业，非全日制）
2025-08-25 09:37:45 - INFO - 提取到当前职位: 人力资源部主管
2025-08-25 09:37:45 - INFO - 提取到期望薪资: 12-15K
2025-08-25 09:37:45 - INFO - 从初筛报告提取的基本信息: {'name': '安伯丽', 'age': '40岁', 'experience': '超过8年人力资源及行政管理经验，未涉及AI或产品管理相关经历', 'education': '本科（法学专业，非全日制）', 'current_position': '人力资源部主管', 'expected_salary': '12-15K'}
2025-08-25 09:37:45 - INFO - 候选人 安佰丽 初筛报告提取完成: advantages=本科学历符合要求  ；具备良好的组织协调能力与流程管理经验（体现在人力资源管理中）, risks=**潜在风险：** 期望薪资略低于JD薪资范围下限，但整体处于边缘状态  ；**核心职责匹配度低：** 完全缺乏AI产品管理所需经验与技能  , basic_info={'name': '安伯丽', 'age': '40岁', 'experience': '超过8年人力资源及行政管理经验，未涉及AI或产品管理相关经历', 'education': '本科（法学专业，非全日制）', 'current_position': '人力资源部主管', 'expected_salary': '12-15K'}
2025-08-25 09:37:45 - INFO - ❌ 安佰丽 未通过第1轮筛选，得分: 0.0
2025-08-25 09:37:45 - INFO - ⚠️ 跳过同名候选人: 曾杰，文件已存在
2025-08-25 09:37:45 - INFO - 开始处理简历: 曾杰
2025-08-25 09:37:45 - INFO - 开始第1轮评估: 曾杰
2025-08-25 09:37:45 - INFO - 岗位名称: AI产品经理
2025-08-25 09:37:45 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 09:37:45 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 09:38:19 - INFO - ✅ 第1轮评估完成: 曾杰 | 分数: 0.0
2025-08-25 09:38:19 - INFO - 检测到明确通过标识: ✅ 通过
2025-08-25 09:38:19 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_09：37_AI产品经理_一二点五三轮筛选结果/初筛合格\通过_曾杰_第1轮_评估报告.md
2025-08-25 09:38:19 - INFO - 开始提取候选人 曾杰 的初筛报告信息
2025-08-25 09:38:19 - INFO - 找到优势文本: **优势:**
    1. 拥有多个AI技术落地项目经验，涵盖无人机智能巡检、文档自动化处理等场景，具备扎实的AI产品设计能力。
    2. 具备跨部门协调经验，能够推动AI功能从需求到上线的全流程执行，符合岗位对项目执行能力的要求。
    3. 对市场与技术趋势敏感，具备产品迭代优化的系统性思维。

- 
2025-08-25 09:38:19 - INFO - 找到优势文本: 优势:**
    1. 拥有多个AI技术落地项目经验，涵盖无人机智能巡检、文档自动化处理等场景，具备扎实的AI产品设计能力。
    2. 具备跨部门协调经验，能够推动AI功能从需求到上线的全流程执行，符合岗位对项目执行能力的要求。
    3. 对市场与技术趋势敏感，具备产品迭代优化的系统性思维。

- **
2025-08-25 09:38:19 - INFO - 找到风险文本: **风险与不足:**
    - 期望薪资未提供，需在后续环节确认是否匹配。
    - 年龄38岁虽在合理范围内，但可能对快节奏创业环境适应性需进一步评估。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历、年龄、薪资均符合硬性门槛要求，且在AI技术应用、用户需求转化、跨部门协作及产品迭代优化方面具备丰富的项目经验与落地成果，核心职责匹配度评估为“高”。虽年龄偏大，但属于“潜在风险”范畴，不影响初筛通过。
2025-08-25 09:38:19 - INFO - 提取的优势和风险: 优势=**, 风险=期望薪资未提供，需在后续环节确认是否匹配。；年龄38岁虽在合理范围内，但可能对快节奏创业环境适应性需进一步评估。
2025-08-25 09:38:19 - INFO - 提取到姓名: 曾杰
2025-08-25 09:38:19 - INFO - 提取到年龄: 38岁
2025-08-25 09:38:19 - INFO - 提取到工作经验: 10年以上
2025-08-25 09:38:19 - INFO - 提取到学历: 硕士
2025-08-25 09:38:19 - INFO - 提取到当前职位: 产品经理
2025-08-25 09:38:19 - INFO - 提取到期望薪资: 未提供
2025-08-25 09:38:19 - INFO - 从初筛报告提取的基本信息: {'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提供'}
2025-08-25 09:38:19 - INFO - 候选人 曾杰 初筛报告提取完成: advantages=**, risks=期望薪资未提供，需在后续环节确认是否匹配。；年龄38岁虽在合理范围内，但可能对快节奏创业环境适应性需进一步评估。, basic_info={'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提供'}
2025-08-25 09:38:19 - INFO - 开始第2轮评估: 曾杰
2025-08-25 09:38:19 - INFO - 岗位名称: AI产品经理
2025-08-25 09:38:19 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/专业画像分析.md
2025-08-25 09:38:19 - INFO - 第2轮评估使用模型: qwen-plus
2025-08-25 09:38:48 - WARNING - 分数不一致: 总分=90.0, 计算总分=0.0 (A:0.0+B:0.0+C:0.0+D:0.0+E:0.0)
2025-08-25 09:38:48 - WARNING - 候选人曾杰分数不一致: 总分=90.0, 计算总分=0.0
2025-08-25 09:38:48 - INFO - ✅ 第2轮评估完成: 曾杰 | 分数: 90.0
2025-08-25 09:38:48 - INFO - 未检测到明确关键词，默认通过
2025-08-25 09:38:48 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_09：37_AI产品经理_一二点五三轮筛选结果/画像分析结果\曾杰_第2轮_画像分析.md
2025-08-25 09:38:48 - INFO - 开始第2.5轮评估: 曾杰
2025-08-25 09:38:48 - INFO - 岗位名称: AI产品经理
2025-08-25 09:38:48 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/专业画像匹配.md
2025-08-25 09:38:48 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/专业画像匹配.md
2025-08-25 09:38:48 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-08-25 09:38:51 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-08-25 09:38:54 - INFO - ✅ 第2.5轮评估完成: 曾杰 | 分数: 0.0
2025-08-25 09:38:54 - INFO - 第2.5轮检测到明确通过标识: ✅ 通过
2025-08-25 09:38:54 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_09：37_AI产品经理_一二点五三轮筛选结果/二筛合格\通过_曾杰_第2.5轮_评估报告.md
2025-08-25 09:38:54 - INFO - 开始第3轮评估: 曾杰
2025-08-25 09:38:54 - INFO - 岗位名称: AI产品经理
2025-08-25 09:38:54 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/终极筛选_打分规则.md
2025-08-25 09:38:54 - INFO - 第3轮评估使用模型: qwen-plus
2025-08-25 09:39:48 - INFO - ✅ 第3轮评估完成: 曾杰 | 分数: 93.0
2025-08-25 09:39:48 - INFO - 第3轮检测到合格关键词: 通过
2025-08-25 09:39:48 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_09：37_AI产品经理_一二点五三轮筛选结果/最终通过\93.0分_曾杰_第3轮_评估报告.md
2025-08-25 09:39:48 - INFO - ✅ 曾杰 完成三轮筛选，最终得分: 93.0, 是否通过: True
2025-08-25 09:39:48 - INFO - 候选人 曾杰 最终返回信息: advantages=**, risks=期望薪资未提供，需在后续环节确认是否匹配。；年龄38岁虽在合理范围内，但可能对快节奏创业环境适应性需进一步评估。, basic_info={'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提供'}
2025-08-25 09:44:07 - INFO - 🚀 启动智能简历评估系统
2025-08-25 09:44:07 - INFO - 📋 系统功能:
2025-08-25 09:44:07 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 09:44:07 - INFO -   - 多线程并发处理
2025-08-25 09:44:07 - INFO -   - 实时进度监控
2025-08-25 09:44:07 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 09:44:07 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 09:44:09 - INFO - 多进程任务管理已初始化
2025-08-25 09:44:12 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 09:44:12 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 09:44:12 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 09:44:21 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 09:44:21 - INFO - ✅ 发现已存在的JSON文件: ./user/未知用户/jd\AI产品经理.json
2025-08-25 09:44:21 - INFO - ✅ JSON文件格式验证通过
2025-08-25 09:44:21 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 09:44:21 - INFO - ✅ 发现已存在的JSON文件: ./user/杨锐雄/jd\AI产品经理.json
2025-08-25 09:44:21 - INFO - ✅ JSON文件格式验证通过
2025-08-25 09:44:23 - INFO - ⚠️ 跳过同名候选人: 安佰丽，文件已存在
2025-08-25 09:44:23 - INFO - 开始处理简历: 安佰丽
2025-08-25 09:44:23 - INFO - 开始第1轮评估: 安佰丽
2025-08-25 09:44:23 - INFO - 岗位名称: AI产品经理
2025-08-25 09:44:23 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 09:44:23 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 09:44:54 - INFO - ✅ 第1轮评估完成: 安佰丽 | 分数: 0.0
2025-08-25 09:44:54 - INFO - 检测到不合格关键词: 不匹配
2025-08-25 09:44:54 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_09：44_AI产品经理_一二点五三轮筛选结果/初筛不合格\未通过_安佰丽_第1轮_评估报告.md
2025-08-25 09:44:54 - INFO - 开始提取候选人 安佰丽 的初筛报告信息
2025-08-25 09:44:54 - INFO - 找到优势文本: **优势:**  
    - 拥有本科教育背景，符合学历门槛  
    - 具备较强的人事统筹与行政管理能力  

- 
2025-08-25 09:44:54 - INFO - 找到风险文本: **风险与不足:**  
    - 核心职责匹配度低，**完全缺乏AI、产品管理、技术应用、用户需求转化、跨部门项目推进等相关经验**  
    - 非全日制学历虽未被排除，但可能在后续评估中成为考虑因素  
    - 期望薪资略低于JD薪资范围下限，可能反映市场认知偏差或经验匹配度不足  

---

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。 → 无  
    2. 核心职责匹配度 评估结果为 "**低**"。 → 成立  

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。 → 满足  
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。 → 不满足  

**筛选结果：** ❌ 淘汰  
**筛选理由：** 尽管候选人满足学历和薪资要求，且年龄未设限，但其**完全缺乏AI产品经理岗位所需的核心职责经验与技能**，与岗位要求的AI技术应用、产品规划、用户需求转化、跨部门协作等能力无任何匹配，因此不建议进入下一轮面试。
2025-08-25 09:44:54 - INFO - 提取的优势和风险: 优势=拥有本科教育背景，符合学历门槛  ；具备较强的人事统筹与行政管理能力  , 风险=核心职责匹配度低，**完全缺乏AI、产品管理、技术应用、用户需求转化、跨部门项目推进等相关经验**  ；非全日制学历虽未被排除，但可能在后续评估中成为考虑因素  
2025-08-25 09:44:54 - INFO - 提取到姓名: 安伯丽
2025-08-25 09:44:54 - INFO - 提取到年龄: 40岁
2025-08-25 09:44:54 - INFO - 提取到工作经验: 近10年，主要集中在人力资源与行政管理领域
2025-08-25 09:44:54 - INFO - 提取到学历: 本科（中山开放大学，法学专业，非全日制）
2025-08-25 09:44:54 - INFO - 提取到当前职位: 人力资源主管 / 人事经理
2025-08-25 09:44:54 - INFO - 提取到期望薪资: 12-15K
2025-08-25 09:44:54 - INFO - 从初筛报告提取的基本信息: {'name': '安伯丽', 'age': '40岁', 'experience': '近10年，主要集中在人力资源与行政管理领域', 'education': '本科（中山开放大学，法学专业，非全日制）', 'current_position': '人力资源主管 / 人事经理', 'expected_salary': '12-15K'}
2025-08-25 09:44:54 - INFO - 候选人 安佰丽 初筛报告提取完成: advantages=拥有本科教育背景，符合学历门槛  ；具备较强的人事统筹与行政管理能力  , risks=核心职责匹配度低，**完全缺乏AI、产品管理、技术应用、用户需求转化、跨部门项目推进等相关经验**  ；非全日制学历虽未被排除，但可能在后续评估中成为考虑因素  , basic_info={'name': '安伯丽', 'age': '40岁', 'experience': '近10年，主要集中在人力资源与行政管理领域', 'education': '本科（中山开放大学，法学专业，非全日制）', 'current_position': '人力资源主管 / 人事经理', 'expected_salary': '12-15K'}
2025-08-25 09:44:54 - INFO - ❌ 安佰丽 未通过第1轮筛选，得分: 0.0
2025-08-25 09:53:44 - ERROR - 导出Excel失败: 'final_score'
2025-08-25 09:58:37 - INFO - 🚀 启动智能简历评估系统
2025-08-25 09:58:37 - INFO - 📋 系统功能:
2025-08-25 09:58:37 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 09:58:37 - INFO -   - 多线程并发处理
2025-08-25 09:58:37 - INFO -   - 实时进度监控
2025-08-25 09:58:37 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 09:58:37 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 09:58:40 - INFO - 多进程任务管理已初始化
2025-08-25 09:58:41 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 09:58:41 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 09:58:41 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 09:58:55 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 09:58:55 - INFO - ✅ 发现已存在的JSON文件: ./user/未知用户/jd\AI产品经理.json
2025-08-25 09:58:55 - INFO - ✅ JSON文件格式验证通过
2025-08-25 09:58:55 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 09:58:55 - INFO - ✅ 发现已存在的JSON文件: ./user/杨锐雄/jd\AI产品经理.json
2025-08-25 09:58:55 - INFO - ✅ JSON文件格式验证通过
2025-08-25 09:58:57 - INFO - ⚠️ 跳过同名候选人: 安佰丽，文件已存在
2025-08-25 09:58:57 - INFO - 开始处理简历: 安佰丽
2025-08-25 09:58:57 - INFO - 开始第1轮评估: 安佰丽
2025-08-25 09:58:57 - INFO - 岗位名称: AI产品经理
2025-08-25 09:58:57 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 09:58:57 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 09:59:24 - INFO - ✅ 第1轮评估完成: 安佰丽 | 分数: 0.0
2025-08-25 09:59:24 - INFO - 检测到不合格关键词: 不匹配
2025-08-25 09:59:24 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_09：58_AI产品经理_一二点五三轮筛选结果/初筛不合格\未通过_安佰丽_第1轮_评估报告.md
2025-08-25 09:59:24 - INFO - 开始提取候选人 安佰丽 的初筛报告信息
2025-08-25 09:59:24 - INFO - 找到优势文本: **优势:**  
  - 拥有稳定的职业经历，具备良好的组织协调与行政管理能力  
  - 期望薪资略低于岗位要求，具备一定成本优势（但非核心能力项）

- 
2025-08-25 09:59:24 - INFO - 找到风险文本: **风险与不足:**  
  - 简历中无任何与AI技术、产品管理、用户需求转化、跨部门项目推进等岗位核心职责相关经验  
  - 所有工作经历均为人力资源与行政管理，与AI产品经理岗位无直接关联  
  - 教育背景为法学专业，非技术或产品相关专业背景  
  - 缺乏对市场趋势、产品迭代等职责的任何经验支撑

**4. 初筛结论**

- **淘汰条件：**
    1. 核心职责匹配度 评估结果为 "**低**"。

- **通过条件：**
    1. 不符合通过条件

**筛选结果：** ❌ 淘汰  
**筛选理由：** 简历中未体现任何与AI产品经理岗位核心职责相关的经验或能力，所有工作经历均为人力资源及行政管理方向，与岗位要求严重不匹配。尽管硬性门槛全部匹配，但核心职责匹配度为“低”，不符合进入下一轮面试的资格。
2025-08-25 09:59:24 - INFO - 提取的优势和风险: 优势=拥有稳定的职业经历，具备良好的组织协调与行政管理能力  ；期望薪资略低于岗位要求，具备一定成本优势（但非核心能力项）, 风险=简历中无任何与AI技术、产品管理、用户需求转化、跨部门项目推进等岗位核心职责相关经验  ；所有工作经历均为人力资源与行政管理，与AI产品经理岗位无直接关联  
2025-08-25 09:59:24 - INFO - 提取到姓名: 安伯丽
2025-08-25 09:59:24 - INFO - 提取到年龄: 40岁
2025-08-25 09:59:24 - INFO - 提取到工作经验: 近10年人力资源及行政管理经验
2025-08-25 09:59:24 - INFO - 提取到学历: 本科（中山开放大学，法学专业，非全日制）
2025-08-25 09:59:24 - INFO - 提取到当前职位: 人力资源主管/经理
2025-08-25 09:59:24 - INFO - 提取到期望薪资: 12-15K
2025-08-25 09:59:24 - INFO - 从初筛报告提取的基本信息: {'name': '安伯丽', 'age': '40岁', 'experience': '近10年人力资源及行政管理经验', 'education': '本科（中山开放大学，法学专业，非全日制）', 'current_position': '人力资源主管/经理', 'expected_salary': '12-15K'}
2025-08-25 09:59:24 - INFO - 候选人 安佰丽 初筛报告提取完成: advantages=拥有稳定的职业经历，具备良好的组织协调与行政管理能力  ；期望薪资略低于岗位要求，具备一定成本优势（但非核心能力项）, risks=简历中无任何与AI技术、产品管理、用户需求转化、跨部门项目推进等岗位核心职责相关经验  ；所有工作经历均为人力资源与行政管理，与AI产品经理岗位无直接关联  , basic_info={'name': '安伯丽', 'age': '40岁', 'experience': '近10年人力资源及行政管理经验', 'education': '本科（中山开放大学，法学专业，非全日制）', 'current_position': '人力资源主管/经理', 'expected_salary': '12-15K'}
2025-08-25 09:59:24 - INFO - ❌ 安佰丽 未通过第1轮筛选，得分: 0.0
2025-08-25 09:59:24 - INFO - ⚠️ 跳过同名候选人: 曾杰，文件已存在
2025-08-25 09:59:24 - INFO - 开始处理简历: 曾杰
2025-08-25 09:59:24 - INFO - 开始第1轮评估: 曾杰
2025-08-25 09:59:24 - INFO - 岗位名称: AI产品经理
2025-08-25 09:59:24 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 09:59:24 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 09:59:50 - INFO - ✅ 第1轮评估完成: 曾杰 | 分数: 0.0
2025-08-25 09:59:50 - INFO - 检测到明确通过标识: ✅ 通过
2025-08-25 09:59:50 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_09：58_AI产品经理_一二点五三轮筛选结果/初筛合格\通过_曾杰_第1轮_评估报告.md
2025-08-25 09:59:50 - INFO - 开始提取候选人 曾杰 的初筛报告信息
2025-08-25 09:59:50 - INFO - 找到优势文本: **优势:**
    1. 具备多个AI相关项目经验，涵盖无人机巡检、文档自动化处理等复杂场景，具备扎实的AI产品设计能力。
    2. 拥有丰富的跨部门协作经验，能够有效协调资源推动AI功能全流程开发与落地。
    3. 具备从需求洞察、产品设计、技术落地到市场推广的全周期产品管理能力。

- 
2025-08-25 09:59:50 - INFO - 找到优势文本: 优势:**
    1. 具备多个AI相关项目经验，涵盖无人机巡检、文档自动化处理等复杂场景，具备扎实的AI产品设计能力。
    2. 拥有丰富的跨部门协作经验，能够有效协调资源推动AI功能全流程开发与落地。
    3. 具备从需求洞察、产品设计、技术落地到市场推广的全周期产品管理能力。

- **
2025-08-25 09:59:50 - INFO - 找到风险文本: **风险与不足:**
    - 无“潜在风险”项。
    - 无“不匹配”项。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。（❌ 未发生）
    2. 核心职责匹配度 评估结果为 "**低**"。（❌ 未发生）

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。（✅ 满足）
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。（✅ 满足）

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历、年龄、薪资均符合岗位要求，且在简历中展示了多个AI产品设计与落地经验，覆盖核心技术应用、用户需求转化、跨部门协作、产品迭代优化等核心职责要求，匹配度高。建议进入下一轮面试。
2025-08-25 09:59:50 - INFO - 提取的优势和风险: 优势=**, 风险=无“潜在风险”项。；无“不匹配”项。
2025-08-25 09:59:50 - INFO - 提取到姓名: 曾杰
2025-08-25 09:59:50 - INFO - 提取到年龄: 38岁
2025-08-25 09:59:50 - INFO - 提取到工作经验: 10年以上
2025-08-25 09:59:50 - INFO - 提取到学历: 硕士
2025-08-25 09:59:50 - INFO - 提取到当前职位: 产品经理
2025-08-25 09:59:50 - INFO - 提取到期望薪资: 未提及
2025-08-25 09:59:50 - INFO - 从初筛报告提取的基本信息: {'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提及'}
2025-08-25 09:59:50 - INFO - 候选人 曾杰 初筛报告提取完成: advantages=**, risks=无“潜在风险”项。；无“不匹配”项。, basic_info={'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提及'}
2025-08-25 09:59:50 - INFO - 开始第2轮评估: 曾杰
2025-08-25 09:59:50 - INFO - 岗位名称: AI产品经理
2025-08-25 09:59:50 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/专业画像分析.md
2025-08-25 09:59:50 - INFO - 第2轮评估使用模型: qwen-plus
2025-08-25 10:00:06 - WARNING - 分数不一致: 总分=87.0, 计算总分=0.0 (A:0.0+B:0.0+C:0.0+D:0.0+E:0.0)
2025-08-25 10:00:06 - WARNING - 候选人曾杰分数不一致: 总分=87.0, 计算总分=0.0
2025-08-25 10:00:06 - INFO - ✅ 第2轮评估完成: 曾杰 | 分数: 87.0
2025-08-25 10:00:06 - INFO - 未检测到明确关键词，默认通过
2025-08-25 10:00:06 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_09：58_AI产品经理_一二点五三轮筛选结果/画像分析结果\曾杰_第2轮_画像分析.md
2025-08-25 10:00:06 - INFO - 开始第2.5轮评估: 曾杰
2025-08-25 10:00:06 - INFO - 岗位名称: AI产品经理
2025-08-25 10:00:06 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/专业画像匹配.md
2025-08-25 10:00:06 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/专业画像匹配.md
2025-08-25 10:00:06 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-08-25 10:00:08 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-08-25 10:00:11 - INFO - ✅ 第2.5轮评估完成: 曾杰 | 分数: 0.0
2025-08-25 10:00:11 - INFO - 第2.5轮检测到明确通过标识: ✅ 通过
2025-08-25 10:00:11 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_09：58_AI产品经理_一二点五三轮筛选结果/二筛合格\通过_曾杰_第2.5轮_评估报告.md
2025-08-25 10:00:11 - INFO - 开始第3轮评估: 曾杰
2025-08-25 10:00:11 - INFO - 岗位名称: AI产品经理
2025-08-25 10:00:11 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/终极筛选_打分规则.md
2025-08-25 10:00:11 - INFO - 第3轮评估使用模型: qwen-plus
2025-08-25 10:01:05 - INFO - ✅ 第3轮评估完成: 曾杰 | 分数: 94.0
2025-08-25 10:01:05 - INFO - 第3轮检测到合格关键词: 通过
2025-08-25 10:01:05 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_09：58_AI产品经理_一二点五三轮筛选结果/最终通过\94.0分_曾杰_第3轮_评估报告.md
2025-08-25 10:01:05 - INFO - ✅ 曾杰 完成三轮筛选，最终得分: 94.0, 是否通过: True
2025-08-25 10:01:05 - INFO - 候选人 曾杰 最终返回信息: advantages=**, risks=无“潜在风险”项。；无“不匹配”项。, basic_info={'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提及'}
2025-08-25 10:01:28 - ERROR - 导出Excel失败: 'final_score'
2025-08-25 10:03:51 - INFO - 🚀 启动智能简历评估系统
2025-08-25 10:03:51 - INFO - 📋 系统功能:
2025-08-25 10:03:51 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 10:03:51 - INFO -   - 多线程并发处理
2025-08-25 10:03:51 - INFO -   - 实时进度监控
2025-08-25 10:03:51 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 10:03:51 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 10:03:53 - INFO - 多进程任务管理已初始化
2025-08-25 10:03:54 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 10:03:54 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 10:03:54 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 10:04:04 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 10:04:04 - INFO - ✅ 发现已存在的JSON文件: ./user/未知用户/jd\AI产品经理.json
2025-08-25 10:04:04 - INFO - ✅ JSON文件格式验证通过
2025-08-25 10:04:05 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 10:04:05 - INFO - ✅ 发现已存在的JSON文件: ./user/杨锐雄/jd\AI产品经理.json
2025-08-25 10:04:05 - INFO - ✅ JSON文件格式验证通过
2025-08-25 10:04:06 - INFO - ⚠️ 跳过同名候选人: 曾杰，文件已存在
2025-08-25 10:04:06 - INFO - 开始处理简历: 曾杰
2025-08-25 10:04:06 - INFO - 开始第1轮评估: 曾杰
2025-08-25 10:04:06 - INFO - 岗位名称: AI产品经理
2025-08-25 10:04:06 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 10:04:06 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 10:04:30 - INFO - ✅ 第1轮评估完成: 曾杰 | 分数: 0.0
2025-08-25 10:04:30 - INFO - 检测到明确通过标识: ✅ 通过
2025-08-25 10:04:30 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_10：04_AI产品经理_一二点五三轮筛选结果/初筛合格\通过_曾杰_第1轮_评估报告.md
2025-08-25 10:04:30 - INFO - 开始提取候选人 曾杰 的初筛报告信息
2025-08-25 10:04:30 - INFO - 找到优势文本: **优势:**
    - 多年AI产品设计经验，涵盖图像识别、自然语言处理、自动化流程等AI技术应用。
    - 拥有多个从0到1的AI产品项目经验，具备完整的产品生命周期管理能力。
    - 擅长跨部门协作，具备良好的项目推进与落地执行能力。
    - 在户外场景（无人机巡检）中有深度AI应用经验，与JD“AI在户外产品中的应用”高度契合。

- 
2025-08-25 10:04:30 - INFO - 找到风险文本: **风险与不足:**
    - 期望薪资未提供，需在后续沟通中确认是否匹配JD薪资范围。
    - 未明确展示对户外用品市场整体趋势的把握，但有具体场景落地经验。

**4. 初筛结论**

- **淘汰条件：** 无  
- **通过条件：** 全部满足  

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历、年龄、薪资均符合硬性门槛要求。在核心职责方面，拥有多个AI产品设计与落地项目，尤其在户外场景（无人机巡检）中表现突出，与岗位JD高度契合。整体匹配度为“高”，符合进入下一轮面试的标准。
2025-08-25 10:04:30 - INFO - 提取的优势和风险: 优势=多年AI产品设计经验，涵盖图像识别、自然语言处理、自动化流程等AI技术应用。；拥有多个从0到1的AI产品项目经验，具备完整的产品生命周期管理能力。；擅长跨部门协作，具备良好的项目推进与落地执行能力。, 风险=期望薪资未提供，需在后续沟通中确认是否匹配JD薪资范围。；未明确展示对户外用品市场整体趋势的把握，但有具体场景落地经验。
2025-08-25 10:04:30 - INFO - 提取到姓名: 曾杰
2025-08-25 10:04:30 - INFO - 提取到年龄: 38岁
2025-08-25 10:04:30 - INFO - 提取到工作经验: 10年以上
2025-08-25 10:04:30 - INFO - 提取到学历: 硕士
2025-08-25 10:04:30 - INFO - 提取到当前职位: 产品经理
2025-08-25 10:04:30 - INFO - 提取到期望薪资: 未提供
2025-08-25 10:04:30 - INFO - 从初筛报告提取的基本信息: {'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提供'}
2025-08-25 10:04:30 - INFO - 候选人 曾杰 初筛报告提取完成: advantages=多年AI产品设计经验，涵盖图像识别、自然语言处理、自动化流程等AI技术应用。；拥有多个从0到1的AI产品项目经验，具备完整的产品生命周期管理能力。；擅长跨部门协作，具备良好的项目推进与落地执行能力。, risks=期望薪资未提供，需在后续沟通中确认是否匹配JD薪资范围。；未明确展示对户外用品市场整体趋势的把握，但有具体场景落地经验。, basic_info={'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提供'}
2025-08-25 10:04:30 - INFO - 开始第2轮评估: 曾杰
2025-08-25 10:04:30 - INFO - 岗位名称: AI产品经理
2025-08-25 10:04:30 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/专业画像分析.md
2025-08-25 10:04:30 - INFO - 第2轮评估使用模型: qwen-plus
2025-08-25 10:04:44 - WARNING - 分数不一致: 总分=88.0, 计算总分=0.0 (A:0.0+B:0.0+C:0.0+D:0.0+E:0.0)
2025-08-25 10:04:44 - WARNING - 候选人曾杰分数不一致: 总分=88.0, 计算总分=0.0
2025-08-25 10:04:44 - INFO - ✅ 第2轮评估完成: 曾杰 | 分数: 88.0
2025-08-25 10:04:44 - INFO - 未检测到明确关键词，默认通过
2025-08-25 10:04:44 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_10：04_AI产品经理_一二点五三轮筛选结果/画像分析结果\曾杰_第2轮_画像分析.md
2025-08-25 10:04:44 - INFO - 开始第2.5轮评估: 曾杰
2025-08-25 10:04:44 - INFO - 岗位名称: AI产品经理
2025-08-25 10:04:44 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/专业画像匹配.md
2025-08-25 10:04:44 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/专业画像匹配.md
2025-08-25 10:04:44 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-08-25 10:04:46 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-08-25 10:04:49 - INFO - ✅ 第2.5轮评估完成: 曾杰 | 分数: 0.0
2025-08-25 10:04:49 - INFO - 第2.5轮检测到明确通过标识: ✅ 通过
2025-08-25 10:04:49 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_10：04_AI产品经理_一二点五三轮筛选结果/二筛合格\通过_曾杰_第2.5轮_评估报告.md
2025-08-25 10:04:49 - INFO - 开始第3轮评估: 曾杰
2025-08-25 10:04:49 - INFO - 岗位名称: AI产品经理
2025-08-25 10:04:49 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/终极筛选_打分规则.md
2025-08-25 10:04:49 - INFO - 第3轮评估使用模型: qwen-plus
2025-08-25 10:05:40 - INFO - ✅ 第3轮评估完成: 曾杰 | 分数: 90.0
2025-08-25 10:05:40 - INFO - 第3轮检测到合格关键词: 通过
2025-08-25 10:05:40 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_10：04_AI产品经理_一二点五三轮筛选结果/最终通过\90.0分_曾杰_第3轮_评估报告.md
2025-08-25 10:05:40 - INFO - ✅ 曾杰 完成三轮筛选，最终得分: 90.0, 是否通过: True
2025-08-25 10:05:40 - INFO - 候选人 曾杰 最终返回信息: advantages=多年AI产品设计经验，涵盖图像识别、自然语言处理、自动化流程等AI技术应用。；拥有多个从0到1的AI产品项目经验，具备完整的产品生命周期管理能力。；擅长跨部门协作，具备良好的项目推进与落地执行能力。, risks=期望薪资未提供，需在后续沟通中确认是否匹配JD薪资范围。；未明确展示对户外用品市场整体趋势的把握，但有具体场景落地经验。, basic_info={'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提供'}
2025-08-25 10:05:59 - ERROR - 导出Excel失败: 'final_score'
2025-08-25 10:06:28 - INFO - 🚀 启动智能简历评估系统
2025-08-25 10:06:28 - INFO - 📋 系统功能:
2025-08-25 10:06:28 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 10:06:28 - INFO -   - 多线程并发处理
2025-08-25 10:06:28 - INFO -   - 实时进度监控
2025-08-25 10:06:28 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 10:06:28 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 10:06:31 - INFO - 多进程任务管理已初始化
2025-08-25 10:06:35 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 10:06:35 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 10:06:35 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 10:06:42 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 10:06:42 - INFO - ✅ 发现已存在的JSON文件: ./user/未知用户/jd\AI产品经理.json
2025-08-25 10:06:42 - INFO - ✅ JSON文件格式验证通过
2025-08-25 10:06:42 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 10:06:42 - INFO - ✅ 发现已存在的JSON文件: ./user/杨锐雄/jd\AI产品经理.json
2025-08-25 10:06:42 - INFO - ✅ JSON文件格式验证通过
2025-08-25 10:06:45 - INFO - ⚠️ 跳过同名候选人: 曾杰，文件已存在
2025-08-25 10:06:45 - INFO - 开始处理简历: 曾杰
2025-08-25 10:06:45 - INFO - 开始第1轮评估: 曾杰
2025-08-25 10:06:45 - INFO - 岗位名称: AI产品经理
2025-08-25 10:06:45 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 10:06:45 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 10:07:14 - INFO - ✅ 第1轮评估完成: 曾杰 | 分数: 0.0
2025-08-25 10:07:14 - INFO - 检测到明确通过标识: ✅ 通过
2025-08-25 10:07:14 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_10：06_AI产品经理_一二点五三轮筛选结果/初筛合格\通过_曾杰_第1轮_评估报告.md
2025-08-25 10:07:14 - INFO - 开始提取候选人 曾杰 的初筛报告信息
2025-08-25 10:07:14 - INFO - 找到优势文本: **优势:**
    - 具备多个AI产品设计与落地经验，涵盖图像识别、自然语言处理、自动化流程等方向。
    - 有丰富的户外智能装备相关项目经验（无人机巡检），符合岗位对“户外产品AI应用”的重点要求。
    - 拥有PMP与NPDP认证，具备完整的产品开发与项目管理能力。

- 
2025-08-25 10:07:14 - INFO - 找到风险文本: **风险与不足:**
    - 无明显风险项。
    - 薪资期望未提供，后续需确认是否符合预算范围。

**4. 初筛结论**

- **淘汰条件：**
    1. 无硬性门槛不匹配项。
    2. 核心职责匹配度为“高”，不符合淘汰条件。

- **通过条件：**
    1. 硬性门槛审查无“不匹配”项。
    2. 核心职责匹配度为“高”。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历高于JD要求，年龄无限制，薪资未提供但未超出范围。核心职责匹配度为“高”，具备多个AI产品设计与落地经验，尤其在户外智能装备领域有深度实践，符合岗位对AI技术应用与产品创新能力的核心要求。
2025-08-25 10:07:14 - INFO - 提取的优势和风险: 优势=具备多个AI产品设计与落地经验，涵盖图像识别、自然语言处理、自动化流程等方向。；有丰富的户外智能装备相关项目经验（无人机巡检），符合岗位对“户外产品AI应用”的重点要求。；拥有PMP与NPDP认证，具备完整的产品开发与项目管理能力。, 风险=无明显风险项。；薪资期望未提供，后续需确认是否符合预算范围。
2025-08-25 10:07:14 - INFO - 提取到姓名: 曾杰
2025-08-25 10:07:14 - INFO - 提取到年龄: 38岁
2025-08-25 10:07:14 - INFO - 提取到工作经验: 10年以上
2025-08-25 10:07:14 - INFO - 提取到学历: 硕士
2025-08-25 10:07:14 - INFO - 提取到当前职位: 产品经理
2025-08-25 10:07:14 - INFO - 提取到期望薪资: 未提供
2025-08-25 10:07:14 - INFO - 从初筛报告提取的基本信息: {'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提供'}
2025-08-25 10:07:14 - INFO - 候选人 曾杰 初筛报告提取完成: advantages=具备多个AI产品设计与落地经验，涵盖图像识别、自然语言处理、自动化流程等方向。；有丰富的户外智能装备相关项目经验（无人机巡检），符合岗位对“户外产品AI应用”的重点要求。；拥有PMP与NPDP认证，具备完整的产品开发与项目管理能力。, risks=无明显风险项。；薪资期望未提供，后续需确认是否符合预算范围。, basic_info={'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提供'}
2025-08-25 10:07:14 - INFO - 开始第2轮评估: 曾杰
2025-08-25 10:07:14 - INFO - 岗位名称: AI产品经理
2025-08-25 10:07:14 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/专业画像分析.md
2025-08-25 10:07:14 - INFO - 第2轮评估使用模型: qwen-plus
2025-08-25 10:07:28 - WARNING - 分数不一致: 总分=85.0, 计算总分=0.0 (A:0.0+B:0.0+C:0.0+D:0.0+E:0.0)
2025-08-25 10:07:28 - WARNING - 候选人曾杰分数不一致: 总分=85.0, 计算总分=0.0
2025-08-25 10:07:28 - INFO - ✅ 第2轮评估完成: 曾杰 | 分数: 85.0
2025-08-25 10:07:28 - INFO - 未检测到明确关键词，默认通过
2025-08-25 10:07:28 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_10：06_AI产品经理_一二点五三轮筛选结果/画像分析结果\曾杰_第2轮_画像分析.md
2025-08-25 10:07:28 - INFO - 开始第2.5轮评估: 曾杰
2025-08-25 10:07:28 - INFO - 岗位名称: AI产品经理
2025-08-25 10:07:28 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/专业画像匹配.md
2025-08-25 10:07:28 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/专业画像匹配.md
2025-08-25 10:07:28 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-08-25 10:07:33 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-08-25 10:07:36 - INFO - ✅ 第2.5轮评估完成: 曾杰 | 分数: 0.0
2025-08-25 10:07:36 - INFO - 第2.5轮检测到明确通过标识: ✅ 通过
2025-08-25 10:07:36 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_10：06_AI产品经理_一二点五三轮筛选结果/二筛合格\通过_曾杰_第2.5轮_评估报告.md
2025-08-25 10:07:36 - INFO - 开始第3轮评估: 曾杰
2025-08-25 10:07:36 - INFO - 岗位名称: AI产品经理
2025-08-25 10:07:36 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/终极筛选_打分规则.md
2025-08-25 10:07:36 - INFO - 第3轮评估使用模型: qwen-plus
2025-08-25 10:08:33 - INFO - ✅ 第3轮评估完成: 曾杰 | 分数: 91.0
2025-08-25 10:08:33 - INFO - 第3轮检测到合格关键词: 通过
2025-08-25 10:08:33 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_10：06_AI产品经理_一二点五三轮筛选结果/最终通过\91.0分_曾杰_第3轮_评估报告.md
2025-08-25 10:08:33 - INFO - ✅ 曾杰 完成三轮筛选，最终得分: 91.0, 是否通过: True
2025-08-25 10:08:33 - INFO - 候选人 曾杰 最终返回信息: advantages=具备多个AI产品设计与落地经验，涵盖图像识别、自然语言处理、自动化流程等方向。；有丰富的户外智能装备相关项目经验（无人机巡检），符合岗位对“户外产品AI应用”的重点要求。；拥有PMP与NPDP认证，具备完整的产品开发与项目管理能力。, risks=无明显风险项。；薪资期望未提供，后续需确认是否符合预算范围。, basic_info={'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提供'}
2025-08-25 10:09:13 - ERROR - 导出Excel失败: 'final_score'
2025-08-25 10:13:35 - ERROR - Exception in callback BaseProactorEventLoop._start_serving.<locals>.loop(<_OverlappedF...0.1', 52291))>) at C:\Users\<USER>\miniconda3\envs\nano\lib\asyncio\proactor_events.py:822
handle: <Handle BaseProactorEventLoop._start_serving.<locals>.loop(<_OverlappedF...0.1', 52291))>) at C:\Users\<USER>\miniconda3\envs\nano\lib\asyncio\proactor_events.py:822>
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\envs\nano\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\miniconda3\envs\nano\lib\asyncio\proactor_events.py", line 836, in loop
    self._make_socket_transport(
  File "C:\Users\<USER>\miniconda3\envs\nano\lib\asyncio\proactor_events.py", line 643, in _make_socket_transport
    return _ProactorSocketTransport(self, sock, protocol, waiter,
  File "C:\Users\<USER>\miniconda3\envs\nano\lib\asyncio\proactor_events.py", line 609, in __init__
    super().__init__(loop, sock, protocol, waiter, extra, server)
  File "C:\Users\<USER>\miniconda3\envs\nano\lib\asyncio\proactor_events.py", line 189, in __init__
    super().__init__(loop, sock, protocol, waiter, extra, server)
  File "C:\Users\<USER>\miniconda3\envs\nano\lib\asyncio\proactor_events.py", line 334, in __init__
    super().__init__(*args, **kw)
  File "C:\Users\<USER>\miniconda3\envs\nano\lib\asyncio\proactor_events.py", line 66, in __init__
    self._server._attach()
  File "C:\Users\<USER>\miniconda3\envs\nano\lib\asyncio\base_events.py", line 297, in _attach
    assert self._sockets is not None
AssertionError
2025-08-25 10:13:35 - ERROR - Exception in callback _ProactorReadPipeTransport._loop_reading(<_OverlappedF...ed result=569>)
handle: <Handle _ProactorReadPipeTransport._loop_reading(<_OverlappedF...ed result=569>)>
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\envs\nano\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\miniconda3\envs\nano\lib\asyncio\proactor_events.py", line 324, in _loop_reading
    self._data_received(data, length)
  File "C:\Users\<USER>\miniconda3\envs\nano\lib\asyncio\proactor_events.py", line 274, in _data_received
    self._protocol.data_received(data)
  File "C:\Users\<USER>\miniconda3\envs\nano\lib\site-packages\uvicorn\protocols\http\h11_impl.py", line 182, in data_received
    self.handle_events()
  File "C:\Users\<USER>\miniconda3\envs\nano\lib\site-packages\uvicorn\protocols\http\h11_impl.py", line 191, in handle_events
    self.send_400_response(msg)
  File "C:\Users\<USER>\miniconda3\envs\nano\lib\site-packages\uvicorn\protocols\http\h11_impl.py", line 301, in send_400_response
    output = self.conn.send(event)
  File "C:\Users\<USER>\miniconda3\envs\nano\lib\site-packages\h11\_connection.py", line 538, in send
    data_list = self.send_with_data_passthrough(event)
  File "C:\Users\<USER>\miniconda3\envs\nano\lib\site-packages\h11\_connection.py", line 563, in send_with_data_passthrough
    self._process_event(self.our_role, event)
  File "C:\Users\<USER>\miniconda3\envs\nano\lib\site-packages\h11\_connection.py", line 284, in _process_event
    self._cstate.process_event(role, type(event), server_switch_event)
  File "C:\Users\<USER>\miniconda3\envs\nano\lib\site-packages\h11\_state.py", line 291, in process_event
    self._fire_event_triggered_transitions(role, _event_type)
  File "C:\Users\<USER>\miniconda3\envs\nano\lib\site-packages\h11\_state.py", line 309, in _fire_event_triggered_transitions
    raise LocalProtocolError(
h11._util.LocalProtocolError: can't handle event type Response when role=SERVER and state=CLOSED
2025-08-25 10:13:38 - INFO - 🚀 启动智能简历评估系统
2025-08-25 10:13:38 - INFO - 📋 系统功能:
2025-08-25 10:13:38 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 10:13:38 - INFO -   - 多线程并发处理
2025-08-25 10:13:38 - INFO -   - 实时进度监控
2025-08-25 10:13:38 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 10:13:38 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 10:13:40 - INFO - 多进程任务管理已初始化
2025-08-25 10:13:48 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 10:13:48 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 10:13:48 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 10:13:58 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 10:13:58 - INFO - ✅ 发现已存在的JSON文件: ./user/未知用户/jd\AI产品经理.json
2025-08-25 10:13:58 - INFO - ✅ JSON文件格式验证通过
2025-08-25 10:13:58 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 10:13:58 - INFO - ✅ 发现已存在的JSON文件: ./user/杨锐雄/jd\AI产品经理.json
2025-08-25 10:13:58 - INFO - ✅ JSON文件格式验证通过
2025-08-25 10:14:01 - INFO - ⚠️ 跳过同名候选人: 安佰丽，文件已存在
2025-08-25 10:14:01 - INFO - 开始处理简历: 安佰丽
2025-08-25 10:14:01 - INFO - 开始第1轮评估: 安佰丽
2025-08-25 10:14:01 - INFO - 岗位名称: AI产品经理
2025-08-25 10:14:01 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 10:14:01 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 10:14:24 - INFO - ✅ 第1轮评估完成: 安佰丽 | 分数: 0.0
2025-08-25 10:14:24 - INFO - 检测到不合格关键词: 不匹配
2025-08-25 10:14:24 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_10：13_AI产品经理_一二点五三轮筛选结果/初筛不合格\未通过_安佰丽_第1轮_评估报告.md
2025-08-25 10:14:24 - INFO - 开始提取候选人 安佰丽 的初筛报告信息
2025-08-25 10:14:24 - INFO - 找到优势文本: **优势:**  
    - 拥有本科学历，符合岗位学历门槛  
    - 具备较强的组织协调能力，体现在跨部门人员管理、制度制定、活动策划等方面

- 
2025-08-25 10:14:24 - INFO - 找到风险文本: **风险与不足:**  
    - **核心职责匹配度低**：完全没有AI产品经理相关的经验或技能背景  
    - **职业背景与岗位方向严重不符**：长期从事行政与人力资源管理，缺乏产品、技术、用户研究等维度的积累  
    - **缺乏技术理解与产品思维**：简历中未见任何与AI、产品设计、技术协同相关的描述  

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。 ❌  
    2. 核心职责匹配度 评估结果为 "**低**"。 ✅  

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。 ✅  
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。 ❌  

**筛选结果：** ❌ 淘汰  
**筛选理由：** 虽然学历和薪资要求匹配，且年龄未设限，但候选人的职业背景完全集中在人力资源与行政管理方向，**缺乏与AI产品经理岗位相关的任何经验与能力描述**，核心职责匹配度为“低”，不符合岗位基本胜任要求。
2025-08-25 10:14:24 - INFO - 提取的优势和风险: 优势=拥有本科学历，符合岗位学历门槛  ；具备较强的组织协调能力，体现在跨部门人员管理、制度制定、活动策划等方面, 风险=**核心职责匹配度低**：完全没有AI产品经理相关的经验或技能背景  ；**职业背景与岗位方向严重不符**：长期从事行政与人力资源管理，缺乏产品、技术、用户研究等维度的积累  
2025-08-25 10:14:24 - INFO - 提取到姓名: 安伯丽
2025-08-25 10:14:24 - INFO - 提取到年龄: 40岁
2025-08-25 10:14:24 - INFO - 提取到工作经验: 超过10年，主要集中在人力资源和行政管理领域
2025-08-25 10:14:24 - INFO - 提取到学历: 本科（法学，非全日制）
2025-08-25 10:14:24 - INFO - 提取到当前职位: 人力资源部主管
2025-08-25 10:14:24 - INFO - 提取到期望薪资: 12-15K
2025-08-25 10:14:24 - INFO - 从初筛报告提取的基本信息: {'name': '安伯丽', 'age': '40岁', 'experience': '超过10年，主要集中在人力资源和行政管理领域', 'education': '本科（法学，非全日制）', 'current_position': '人力资源部主管', 'expected_salary': '12-15K'}
2025-08-25 10:14:24 - INFO - 候选人 安佰丽 初筛报告提取完成: advantages=拥有本科学历，符合岗位学历门槛  ；具备较强的组织协调能力，体现在跨部门人员管理、制度制定、活动策划等方面, risks=**核心职责匹配度低**：完全没有AI产品经理相关的经验或技能背景  ；**职业背景与岗位方向严重不符**：长期从事行政与人力资源管理，缺乏产品、技术、用户研究等维度的积累  , basic_info={'name': '安伯丽', 'age': '40岁', 'experience': '超过10年，主要集中在人力资源和行政管理领域', 'education': '本科（法学，非全日制）', 'current_position': '人力资源部主管', 'expected_salary': '12-15K'}
2025-08-25 10:14:24 - INFO - ❌ 安佰丽 未通过第1轮筛选，得分: 0.0
2025-08-25 10:14:28 - ERROR - 导出Excel失败: 'final_score'
2025-08-25 10:18:09 - INFO - 🚀 启动智能简历评估系统
2025-08-25 10:18:09 - INFO - 📋 系统功能:
2025-08-25 10:18:09 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 10:18:09 - INFO -   - 多线程并发处理
2025-08-25 10:18:09 - INFO -   - 实时进度监控
2025-08-25 10:18:09 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 10:18:09 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 10:18:11 - INFO - 多进程任务管理已初始化
2025-08-25 10:18:14 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 10:18:14 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 10:18:14 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 10:18:27 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 10:18:27 - INFO - ✅ 发现已存在的JSON文件: ./user/未知用户/jd\AI产品经理.json
2025-08-25 10:18:27 - INFO - ✅ JSON文件格式验证通过
2025-08-25 10:18:27 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 10:18:27 - INFO - ✅ 发现已存在的JSON文件: ./user/杨锐雄/jd\AI产品经理.json
2025-08-25 10:18:27 - INFO - ✅ JSON文件格式验证通过
2025-08-25 10:18:29 - INFO - ⚠️ 跳过同名候选人: 安佰丽，文件已存在
2025-08-25 10:18:29 - INFO - 开始处理简历: 安佰丽
2025-08-25 10:18:29 - INFO - 开始第1轮评估: 安佰丽
2025-08-25 10:18:29 - INFO - 岗位名称: AI产品经理
2025-08-25 10:18:29 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 10:18:29 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 10:18:56 - INFO - ✅ 第1轮评估完成: 安佰丽 | 分数: 0.0
2025-08-25 10:18:56 - INFO - 检测到不合格关键词: 不匹配
2025-08-25 10:18:56 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_10：18_AI产品经理_一二点五三轮筛选结果/初筛不合格\未通过_安佰丽_第1轮_评估报告.md
2025-08-25 10:18:56 - INFO - 开始提取候选人 安佰丽 的初筛报告信息
2025-08-25 10:18:56 - INFO - 找到优势文本: **优势:** 
    - 本科学历符合要求
    - 有多年管理经验，具备一定的跨部门协调能力
    - 期望薪资略低于JD上限，具备一定灵活性

- 
2025-08-25 10:18:56 - INFO - 找到风险文本: **风险与不足:** 
    - 完全缺乏AI产品经理岗位所需的核心职责相关经验
    - 无任何技术背景或产品开发经历
    - 缺乏用户需求洞察、AI应用、产品规划等能力的证明材料

**4. 初筛结论**

- **淘汰条件：**
    1. 核心职责匹配度 评估结果为 "**低**"。

- **通过条件：**
    1. 不符合通过条件

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人虽然满足学历、年龄、薪资等硬性门槛要求，但**完全缺乏AI产品经理岗位所需的核心职责相关经验**，包括AI技术应用、产品需求转化、项目执行等关键能力。**核心职责匹配度为“低”**，根据评估标准应予以淘汰。
2025-08-25 10:18:56 - INFO - 提取的优势和风险: 优势=本科学历符合要求；有多年管理经验，具备一定的跨部门协调能力；期望薪资略低于JD上限，具备一定灵活性, 风险=完全缺乏AI产品经理岗位所需的核心职责相关经验；无任何技术背景或产品开发经历
2025-08-25 10:18:56 - INFO - 提取到姓名: 安伯丽
2025-08-25 10:18:56 - INFO - 提取到年龄: 40岁
2025-08-25 10:18:56 - INFO - 提取到工作经验: 超过10年的人力资源及行政管理经验
2025-08-25 10:18:56 - INFO - 提取到学历: 本科（中山开放大学，法学专业，非全日制）
2025-08-25 10:18:56 - INFO - 提取到当前职位: 人力资源主管/人事经理
2025-08-25 10:18:56 - INFO - 提取到期望薪资: 12-15K
2025-08-25 10:18:56 - INFO - 从初筛报告提取的基本信息: {'name': '安伯丽', 'age': '40岁', 'experience': '超过10年的人力资源及行政管理经验', 'education': '本科（中山开放大学，法学专业，非全日制）', 'current_position': '人力资源主管/人事经理', 'expected_salary': '12-15K'}
2025-08-25 10:18:56 - INFO - 候选人 安佰丽 初筛报告提取完成: advantages=本科学历符合要求；有多年管理经验，具备一定的跨部门协调能力；期望薪资略低于JD上限，具备一定灵活性, risks=完全缺乏AI产品经理岗位所需的核心职责相关经验；无任何技术背景或产品开发经历, basic_info={'name': '安伯丽', 'age': '40岁', 'experience': '超过10年的人力资源及行政管理经验', 'education': '本科（中山开放大学，法学专业，非全日制）', 'current_position': '人力资源主管/人事经理', 'expected_salary': '12-15K'}
2025-08-25 10:18:56 - INFO - ❌ 安佰丽 未通过第1轮筛选，得分: 0.0
2025-08-25 10:18:56 - INFO - ⚠️ 跳过同名候选人: 曾杰，文件已存在
2025-08-25 10:18:56 - INFO - 开始处理简历: 曾杰
2025-08-25 10:18:56 - INFO - 开始第1轮评估: 曾杰
2025-08-25 10:18:56 - INFO - 岗位名称: AI产品经理
2025-08-25 10:18:56 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 10:18:56 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 10:19:25 - INFO - ✅ 第1轮评估完成: 曾杰 | 分数: 0.0
2025-08-25 10:19:25 - INFO - 检测到明确通过标识: ✅ 通过
2025-08-25 10:19:25 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_10：18_AI产品经理_一二点五三轮筛选结果/初筛合格\通过_曾杰_第1轮_评估报告.md
2025-08-25 10:19:25 - INFO - 开始提取候选人 曾杰 的初筛报告信息
2025-08-25 10:19:25 - INFO - 找到优势文本: **优势:** 
    - 多年AI相关产品经验，成功落地多个AI+行业解决方案（如无人机巡检、文档自动化处理）
    - 具备AI技术理解与产品化落地的全流程能力，涵盖需求转化、技术整合、商业化探索
    - 拥有PMP与NPDP认证，项目管理与产品设计能力扎实

- 
2025-08-25 10:19:25 - INFO - 找到风险文本: **风险与不足:** 
    - 无薪资期望信息，无法进一步评估薪资匹配情况（但未超过阈值，故不构成淘汰项）
    - 年龄38岁虽在合理范围内，但部分企业可能对AI产品经理年龄有偏好（非硬性淘汰项）

**4. 初筛结论**

- **淘汰条件：**
    1. ✅ 无硬性门槛不匹配项
    2. ✅ 核心职责匹配度为“高”
- **通过条件：**
    1. ✅ 硬性门槛审查中无“不匹配”项
    2. ✅ 核心职责匹配度为“高”

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历满足要求，年龄与薪资未超出限制，且无“不匹配”项。其在多个AI产品项目中展现出与JD高度匹配的AI应用能力、产品转化能力与全流程执行能力，核心职责匹配度为“高”，符合进入下一轮面试的资格。
2025-08-25 10:19:25 - INFO - 提取的优势和风险: 优势=多年AI相关产品经验，成功落地多个AI+行业解决方案（如无人机巡检、文档自动化处理）；具备AI技术理解与产品化落地的全流程能力，涵盖需求转化、技术整合、商业化探索；拥有PMP与NPDP认证，项目管理与产品设计能力扎实, 风险=无薪资期望信息，无法进一步评估薪资匹配情况（但未超过阈值，故不构成淘汰项）；年龄38岁虽在合理范围内，但部分企业可能对AI产品经理年龄有偏好（非硬性淘汰项）
2025-08-25 10:19:25 - INFO - 提取到姓名: 曾杰
2025-08-25 10:19:25 - INFO - 提取到年龄: 38岁
2025-08-25 10:19:25 - INFO - 提取到工作经验: 10年以上
2025-08-25 10:19:25 - INFO - 提取到学历: 硕士
2025-08-25 10:19:25 - INFO - 提取到当前职位: 产品经理
2025-08-25 10:19:25 - INFO - 提取到期望薪资: 未提供
2025-08-25 10:19:25 - INFO - 从初筛报告提取的基本信息: {'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提供'}
2025-08-25 10:19:25 - INFO - 候选人 曾杰 初筛报告提取完成: advantages=多年AI相关产品经验，成功落地多个AI+行业解决方案（如无人机巡检、文档自动化处理）；具备AI技术理解与产品化落地的全流程能力，涵盖需求转化、技术整合、商业化探索；拥有PMP与NPDP认证，项目管理与产品设计能力扎实, risks=无薪资期望信息，无法进一步评估薪资匹配情况（但未超过阈值，故不构成淘汰项）；年龄38岁虽在合理范围内，但部分企业可能对AI产品经理年龄有偏好（非硬性淘汰项）, basic_info={'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提供'}
2025-08-25 10:19:25 - INFO - 开始第2轮评估: 曾杰
2025-08-25 10:19:25 - INFO - 岗位名称: AI产品经理
2025-08-25 10:19:25 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/专业画像分析.md
2025-08-25 10:19:25 - INFO - 第2轮评估使用模型: qwen-plus
2025-08-25 10:19:40 - WARNING - 分数不一致: 总分=92.0, 计算总分=0.0 (A:0.0+B:0.0+C:0.0+D:0.0+E:0.0)
2025-08-25 10:19:40 - WARNING - 候选人曾杰分数不一致: 总分=92.0, 计算总分=0.0
2025-08-25 10:19:40 - INFO - ✅ 第2轮评估完成: 曾杰 | 分数: 92.0
2025-08-25 10:19:40 - INFO - 未检测到明确关键词，默认通过
2025-08-25 10:19:40 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_10：18_AI产品经理_一二点五三轮筛选结果/画像分析结果\曾杰_第2轮_画像分析.md
2025-08-25 10:19:40 - INFO - 开始第2.5轮评估: 曾杰
2025-08-25 10:19:40 - INFO - 岗位名称: AI产品经理
2025-08-25 10:19:40 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/专业画像匹配.md
2025-08-25 10:19:40 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/专业画像匹配.md
2025-08-25 10:19:40 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-08-25 10:19:44 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-08-25 10:19:47 - INFO - ✅ 第2.5轮评估完成: 曾杰 | 分数: 0.0
2025-08-25 10:19:47 - INFO - 第2.5轮检测到明确通过标识: ✅ 通过
2025-08-25 10:19:47 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_10：18_AI产品经理_一二点五三轮筛选结果/二筛合格\通过_曾杰_第2.5轮_评估报告.md
2025-08-25 10:19:47 - INFO - 开始第3轮评估: 曾杰
2025-08-25 10:19:47 - INFO - 岗位名称: AI产品经理
2025-08-25 10:19:47 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/终极筛选_打分规则.md
2025-08-25 10:19:47 - INFO - 第3轮评估使用模型: qwen-plus
2025-08-25 10:20:52 - ERROR - 导出Excel失败: 'final_score'
2025-08-25 10:21:07 - INFO - ✅ 第3轮评估完成: 曾杰 | 分数: 91.0
2025-08-25 10:21:07 - INFO - 第3轮检测到合格关键词: 合格
2025-08-25 10:21:07 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_10：18_AI产品经理_一二点五三轮筛选结果/最终通过\91.0分_曾杰_第3轮_评估报告.md
2025-08-25 10:21:07 - INFO - ✅ 曾杰 完成三轮筛选，最终得分: 91.0, 是否通过: True
2025-08-25 10:21:07 - INFO - 候选人 曾杰 最终返回信息: advantages=多年AI相关产品经验，成功落地多个AI+行业解决方案（如无人机巡检、文档自动化处理）；具备AI技术理解与产品化落地的全流程能力，涵盖需求转化、技术整合、商业化探索；拥有PMP与NPDP认证，项目管理与产品设计能力扎实, risks=无薪资期望信息，无法进一步评估薪资匹配情况（但未超过阈值，故不构成淘汰项）；年龄38岁虽在合理范围内，但部分企业可能对AI产品经理年龄有偏好（非硬性淘汰项）, basic_info={'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提供'}
2025-08-25 10:21:40 - ERROR - 导出Excel失败: 'final_score'
2025-08-25 10:25:53 - INFO - 🚀 启动智能简历评估系统
2025-08-25 10:25:53 - INFO - 📋 系统功能:
2025-08-25 10:25:53 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 10:25:53 - INFO -   - 多线程并发处理
2025-08-25 10:25:53 - INFO -   - 实时进度监控
2025-08-25 10:25:53 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 10:25:53 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 10:25:54 - INFO - 多进程任务管理已初始化
2025-08-25 10:26:00 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 10:26:00 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 10:26:00 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 10:26:06 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 10:26:06 - INFO - ✅ 发现已存在的JSON文件: ./user/未知用户/jd\AI产品经理.json
2025-08-25 10:26:06 - INFO - ✅ JSON文件格式验证通过
2025-08-25 10:26:06 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 10:26:06 - INFO - ✅ 发现已存在的JSON文件: ./user/杨锐雄/jd\AI产品经理.json
2025-08-25 10:26:06 - INFO - ✅ JSON文件格式验证通过
2025-08-25 10:26:07 - INFO - ⚠️ 跳过同名候选人: 安佰丽，文件已存在
2025-08-25 10:26:07 - INFO - 开始处理简历: 安佰丽
2025-08-25 10:26:07 - INFO - 开始第1轮评估: 安佰丽
2025-08-25 10:26:07 - INFO - 岗位名称: AI产品经理
2025-08-25 10:26:07 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 10:26:07 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 10:26:30 - INFO - ✅ 第1轮评估完成: 安佰丽 | 分数: 0.0
2025-08-25 10:26:30 - INFO - 检测到不合格关键词: 不匹配
2025-08-25 10:26:30 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_10：26_AI产品经理_一二点五三轮筛选结果/初筛不合格\未通过_安佰丽_第1轮_评估报告.md
2025-08-25 10:26:30 - INFO - 开始提取候选人 安佰丽 的初筛报告信息
2025-08-25 10:26:30 - INFO - 找到优势文本: **优势:**  
    - 拥有本科学历，符合基本学历门槛。  
    - 期望薪资略低于JD下限，可能在成本控制方面具有一定优势。

- 
2025-08-25 10:26:30 - INFO - 找到风险文本: **风险与不足:**  
    - **核心职责匹配度极低**：无任何AI产品、用户需求转化、项目执行、市场趋势分析等关键经验。  
    - **跨部门协调经验有限**：简历中主要体现为内部人事协调，而非产品开发中的跨职能协作。  
    - **缺乏技术背景与产品思维**：所有职责描述均围绕行政与人力资源管理，未体现产品经理所需技能。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。 → 无  
    2. 核心职责匹配度 评估结果为 "**低**"。 → ✅ 成立

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。 → ✅  
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。 → ❌ 未满足

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人虽满足学历和薪资要求，但**核心职责匹配度评估结果为“低”**，缺乏AI产品经理所需的AI技术理解、产品规划、用户需求转化、跨部门协调等关键能力，**与岗位要求严重不匹配**。
2025-08-25 10:26:30 - INFO - 提取的优势和风险: 优势=拥有本科学历，符合基本学历门槛。  ；期望薪资略低于JD下限，可能在成本控制方面具有一定优势。, 风险=**核心职责匹配度极低**：无任何AI产品、用户需求转化、项目执行、市场趋势分析等关键经验。  ；**跨部门协调经验有限**：简历中主要体现为内部人事协调，而非产品开发中的跨职能协作。  
2025-08-25 10:26:30 - INFO - 提取到姓名: 安伯丽
2025-08-25 10:26:30 - INFO - 提取到年龄: 40岁
2025-08-25 10:26:30 - INFO - 提取到工作经验: 近10年人力资源管理及行政管理经验
2025-08-25 10:26:30 - INFO - 提取到学历: 本科（中山开放大学，法学专业）
2025-08-25 10:26:30 - INFO - 提取到当前职位: 人力资源部主管
2025-08-25 10:26:30 - INFO - 提取到期望薪资: 12-15K
2025-08-25 10:26:30 - INFO - 从初筛报告提取的基本信息: {'name': '安伯丽', 'age': '40岁', 'experience': '近10年人力资源管理及行政管理经验', 'education': '本科（中山开放大学，法学专业）', 'current_position': '人力资源部主管', 'expected_salary': '12-15K'}
2025-08-25 10:26:30 - INFO - 候选人 安佰丽 初筛报告提取完成: advantages=拥有本科学历，符合基本学历门槛。  ；期望薪资略低于JD下限，可能在成本控制方面具有一定优势。, risks=**核心职责匹配度极低**：无任何AI产品、用户需求转化、项目执行、市场趋势分析等关键经验。  ；**跨部门协调经验有限**：简历中主要体现为内部人事协调，而非产品开发中的跨职能协作。  , basic_info={'name': '安伯丽', 'age': '40岁', 'experience': '近10年人力资源管理及行政管理经验', 'education': '本科（中山开放大学，法学专业）', 'current_position': '人力资源部主管', 'expected_salary': '12-15K'}
2025-08-25 10:26:30 - INFO - ❌ 安佰丽 未通过第1轮筛选，得分: 0.0
2025-08-25 10:26:40 - ERROR - 导出Excel失败: 'final_score'
2025-08-25 10:26:56 - ERROR - 导出Excel失败: 'final_score'
2025-08-25 10:29:45 - INFO - 🚀 启动智能简历评估系统
2025-08-25 10:29:45 - INFO - 📋 系统功能:
2025-08-25 10:29:45 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 10:29:45 - INFO -   - 多线程并发处理
2025-08-25 10:29:45 - INFO -   - 实时进度监控
2025-08-25 10:29:45 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 10:29:45 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 10:29:46 - INFO - 多进程任务管理已初始化
2025-08-25 10:29:52 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 10:29:52 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 10:29:52 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 10:29:58 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 10:29:58 - INFO - ✅ 发现已存在的JSON文件: ./user/未知用户/jd\AI产品经理.json
2025-08-25 10:29:58 - INFO - ✅ JSON文件格式验证通过
2025-08-25 10:29:58 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 10:29:58 - INFO - ✅ 发现已存在的JSON文件: ./user/杨锐雄/jd\AI产品经理.json
2025-08-25 10:29:58 - INFO - ✅ JSON文件格式验证通过
2025-08-25 10:29:59 - INFO - ⚠️ 跳过同名候选人: 安佰丽，文件已存在
2025-08-25 10:29:59 - INFO - 开始处理简历: 安佰丽
2025-08-25 10:29:59 - INFO - 开始第1轮评估: 安佰丽
2025-08-25 10:29:59 - INFO - 岗位名称: AI产品经理
2025-08-25 10:29:59 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 10:29:59 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 10:30:34 - INFO - ✅ 第1轮评估完成: 安佰丽 | 分数: 0.0
2025-08-25 10:30:34 - INFO - 检测到不合格关键词: 不通过
2025-08-25 10:30:34 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_10：29_AI产品经理_一二点五三轮筛选结果/初筛不合格\未通过_安佰丽_第1轮_评估报告.md
2025-08-25 10:30:34 - INFO - 开始提取候选人 安佰丽 的初筛报告信息
2025-08-25 10:30:34 - INFO - 找到优势文本: **优势:**  
    - 有多年人力资源管理经验，具备良好的组织协调与制度建设能力  
    - 曾负责跨区域多公司人员管理，具备一定的统筹与执行能力

- 
2025-08-25 10:30:34 - INFO - 找到风险文本: **风险与不足:**  
    - **缺乏AI产品经理所需的核心技能与经验（AI技术应用、需求转化、项目执行）**  
    - **无任何产品设计、用户调研、市场分析、技术协作等相关工作经历**  
    - **薪资略低于JD下限，虽未构成不匹配，但可能影响后续沟通**

**4. 初筛结论**

- **淘汰条件：**
    1. ✅ 硬性门槛审查 中有任意一项结果为 "**不匹配**"：无  
    2. ✅ 核心职责匹配度 评估结果为 "**低**"：是  

- **通过条件：**
    1. ✅ 硬性门槛审查 结果中**没有** "**不匹配**" 项：是  
    2. ❌ 核心职责匹配度 评估结果为 "**中**" 或 "**高**"：否  

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人虽满足学历与年龄要求，薪资也未构成不匹配，但其工作经历完全集中于人力资源与行政管理领域，**缺乏AI产品经理所需的产品规划、技术理解、跨部门协作及市场洞察等核心能力**，与岗位核心职责匹配度为“低”，因此不通过初筛。
2025-08-25 10:30:34 - INFO - 提取的优势和风险: 优势=有多年人力资源管理经验，具备良好的组织协调与制度建设能力  ；曾负责跨区域多公司人员管理，具备一定的统筹与执行能力, 风险=**缺乏AI产品经理所需的核心技能与经验（AI技术应用、需求转化、项目执行）**  ；**无任何产品设计、用户调研、市场分析、技术协作等相关工作经历**  
2025-08-25 10:30:34 - INFO - 提取到姓名: 安伯丽
2025-08-25 10:30:34 - INFO - 提取到年龄: 40岁
2025-08-25 10:30:34 - INFO - 提取到工作经验: 约10年（主要集中在人力资源与行政管理领域）
2025-08-25 10:30:34 - INFO - 提取到学历: 本科（法学，非全日制）
2025-08-25 10:30:34 - INFO - 提取到当前职位: 人力资源主管/人事经理
2025-08-25 10:30:34 - INFO - 提取到期望薪资: 12-15K
2025-08-25 10:30:34 - INFO - 从初筛报告提取的基本信息: {'name': '安伯丽', 'age': '40岁', 'experience': '约10年（主要集中在人力资源与行政管理领域）', 'education': '本科（法学，非全日制）', 'current_position': '人力资源主管/人事经理', 'expected_salary': '12-15K'}
2025-08-25 10:30:34 - INFO - 候选人 安佰丽 初筛报告提取完成: advantages=有多年人力资源管理经验，具备良好的组织协调与制度建设能力  ；曾负责跨区域多公司人员管理，具备一定的统筹与执行能力, risks=**缺乏AI产品经理所需的核心技能与经验（AI技术应用、需求转化、项目执行）**  ；**无任何产品设计、用户调研、市场分析、技术协作等相关工作经历**  , basic_info={'name': '安伯丽', 'age': '40岁', 'experience': '约10年（主要集中在人力资源与行政管理领域）', 'education': '本科（法学，非全日制）', 'current_position': '人力资源主管/人事经理', 'expected_salary': '12-15K'}
2025-08-25 10:30:34 - INFO - ❌ 安佰丽 未通过第1轮筛选，得分: 0.0
2025-08-25 11:08:32 - INFO - 🚀 启动智能简历评估系统
2025-08-25 11:08:32 - INFO - 📋 系统功能:
2025-08-25 11:08:32 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 11:08:32 - INFO -   - 多线程并发处理
2025-08-25 11:08:32 - INFO -   - 实时进度监控
2025-08-25 11:08:32 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 11:08:32 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 11:08:33 - INFO - 多进程任务管理已初始化
2025-08-25 11:08:43 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 11:08:43 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 11:08:43 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 11:09:55 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 11:09:55 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 11:09:55 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 11:17:08 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 11:17:08 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 11:17:08 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 11:17:32 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 11:17:32 - INFO - ✅ 发现已存在的JSON文件: ./user/未知用户/jd\AI产品经理.json
2025-08-25 11:17:32 - INFO - ✅ JSON文件格式验证通过
2025-08-25 11:17:32 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 11:17:32 - INFO - ✅ 发现已存在的JSON文件: ./user/杨锐雄/jd\AI产品经理.json
2025-08-25 11:17:32 - INFO - ✅ JSON文件格式验证通过
2025-08-25 11:17:33 - INFO - ⚠️ 跳过同名候选人: 曾杰，文件已存在
2025-08-25 11:17:33 - INFO - 开始处理简历: 曾杰
2025-08-25 11:17:33 - INFO - 开始第1轮评估: 曾杰
2025-08-25 11:17:33 - INFO - 岗位名称: AI产品经理
2025-08-25 11:17:33 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 11:17:33 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 11:17:46 - INFO - 🚀 启动智能简历评估系统
2025-08-25 11:17:46 - INFO - 📋 系统功能:
2025-08-25 11:17:46 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 11:17:46 - INFO -   - 多线程并发处理
2025-08-25 11:17:46 - INFO -   - 实时进度监控
2025-08-25 11:17:46 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 11:17:46 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 11:17:47 - INFO - 多进程任务管理已初始化
2025-08-25 11:17:53 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 11:17:53 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 11:17:53 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 11:17:58 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 11:17:58 - INFO - ✅ 发现已存在的JSON文件: ./user/未知用户/jd\AI产品经理.json
2025-08-25 11:17:58 - INFO - ✅ JSON文件格式验证通过
2025-08-25 11:17:58 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 11:17:58 - INFO - ✅ 发现已存在的JSON文件: ./user/杨锐雄/jd\AI产品经理.json
2025-08-25 11:17:58 - INFO - ✅ JSON文件格式验证通过
2025-08-25 11:17:59 - INFO - ⚠️ 跳过同名候选人: 曾杰，文件已存在
2025-08-25 11:17:59 - INFO - 开始处理简历: 曾杰
2025-08-25 11:17:59 - INFO - 开始第1轮评估: 曾杰
2025-08-25 11:17:59 - INFO - 岗位名称: AI产品经理
2025-08-25 11:17:59 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 11:17:59 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 11:18:38 - INFO - ✅ 第1轮评估完成: 曾杰 | 分数: 0.0
2025-08-25 11:18:38 - INFO - 检测到明确通过标识: ✅ 通过
2025-08-25 11:18:38 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_11：17_AI产品经理_一二点五三轮筛选结果/初筛合格\通过_曾杰_第1轮_评估报告.md
2025-08-25 11:18:38 - INFO - 开始提取候选人 曾杰 的初筛报告信息
2025-08-25 11:18:38 - INFO - 找到优势文本: **优势:**  
  - 多年AI+产品经验，主导多个AI技术落地项目（如无人机巡检、文档自动化处理）  
  - 具备跨部门资源整合与全流程执行能力，熟悉敏捷开发与产品生命周期管理  
  - 对技术趋势敏感，具备明确的产品迭代与商业化思维  

- 
2025-08-25 11:18:38 - INFO - 找到风险文本: **风险与不足:**  
  - 无硬性门槛不匹配项  
  - 期望薪资未提供，后续需确认是否在JD薪资范围内  

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。 ❌  
    2. 核心职责匹配度 评估结果为 "**低**"。 ❌  
- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。 ✅  
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。 ✅  

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历、年龄、薪资三项均未超出硬性门槛要求，且在核心职责方面展现出高度匹配的项目经验和能力，尤其在AI技术落地、跨部门协作、产品迭代优化等方面表现突出。虽期望薪资未提供，但整体匹配度高，建议进入下一轮面试。
2025-08-25 11:18:38 - INFO - 提取的优势和风险: 优势=多年AI+产品经验，主导多个AI技术落地项目（如无人机巡检、文档自动化处理）  ；具备跨部门资源整合与全流程执行能力，熟悉敏捷开发与产品生命周期管理  ；对技术趋势敏感，具备明确的产品迭代与商业化思维  , 风险=无硬性门槛不匹配项  ；期望薪资未提供，后续需确认是否在JD薪资范围内  
2025-08-25 11:18:38 - INFO - 提取到姓名: 曾杰
2025-08-25 11:18:38 - INFO - 提取到年龄: 38岁
2025-08-25 11:18:38 - INFO - 提取到工作经验: 10年以上
2025-08-25 11:18:38 - INFO - 提取到学历: 硕士（计算机技术）
2025-08-25 11:18:38 - INFO - 提取到当前职位: 产品经理
2025-08-25 11:18:38 - INFO - 提取到期望薪资: 未提及
2025-08-25 11:18:38 - INFO - 从初筛报告提取的基本信息: {'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士（计算机技术）', 'current_position': '产品经理', 'expected_salary': '未提及'}
2025-08-25 11:18:38 - INFO - 候选人 曾杰 初筛报告提取完成: advantages=多年AI+产品经验，主导多个AI技术落地项目（如无人机巡检、文档自动化处理）  ；具备跨部门资源整合与全流程执行能力，熟悉敏捷开发与产品生命周期管理  ；对技术趋势敏感，具备明确的产品迭代与商业化思维  , risks=无硬性门槛不匹配项  ；期望薪资未提供，后续需确认是否在JD薪资范围内  , basic_info={'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士（计算机技术）', 'current_position': '产品经理', 'expected_salary': '未提及'}
2025-08-25 11:18:38 - INFO - 开始第2轮评估: 曾杰
2025-08-25 11:18:38 - INFO - 岗位名称: AI产品经理
2025-08-25 11:18:38 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/专业画像分析.md
2025-08-25 11:18:38 - INFO - 第2轮评估使用模型: qwen-plus
2025-08-25 11:18:55 - WARNING - 分数不一致: 总分=82.0, 计算总分=0.0 (A:0.0+B:0.0+C:0.0+D:0.0+E:0.0)
2025-08-25 11:18:55 - WARNING - 候选人曾杰分数不一致: 总分=82.0, 计算总分=0.0
2025-08-25 11:18:55 - INFO - ✅ 第2轮评估完成: 曾杰 | 分数: 82.0
2025-08-25 11:18:55 - INFO - 未检测到明确关键词，默认通过
2025-08-25 11:18:55 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_11：17_AI产品经理_一二点五三轮筛选结果/画像分析结果\曾杰_第2轮_画像分析.md
2025-08-25 11:18:55 - INFO - 开始第2.5轮评估: 曾杰
2025-08-25 11:18:55 - INFO - 岗位名称: AI产品经理
2025-08-25 11:18:55 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/专业画像匹配.md
2025-08-25 11:18:55 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/专业画像匹配.md
2025-08-25 11:18:55 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-08-25 11:19:00 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-08-25 11:19:04 - INFO - ✅ 第2.5轮评估完成: 曾杰 | 分数: 0.0
2025-08-25 11:19:04 - INFO - 第2.5轮检测到明确通过标识: ✅ 通过
2025-08-25 11:19:04 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_11：17_AI产品经理_一二点五三轮筛选结果/二筛合格\通过_曾杰_第2.5轮_评估报告.md
2025-08-25 11:19:04 - INFO - 开始第3轮评估: 曾杰
2025-08-25 11:19:04 - INFO - 岗位名称: AI产品经理
2025-08-25 11:19:04 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/终极筛选_打分规则.md
2025-08-25 11:19:04 - INFO - 第3轮评估使用模型: qwen-plus
2025-08-25 11:20:19 - INFO - ✅ 第3轮评估完成: 曾杰 | 分数: 94.0
2025-08-25 11:20:19 - INFO - 第3轮检测到合格关键词: 通过
2025-08-25 11:20:19 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_11：17_AI产品经理_一二点五三轮筛选结果/最终通过\94.0分_曾杰_第3轮_评估报告.md
2025-08-25 11:20:19 - INFO - ✅ 曾杰 完成三轮筛选，最终得分: 94.0, 是否通过: True
2025-08-25 11:20:19 - INFO - 候选人 曾杰 最终返回信息: advantages=多年AI+产品经验，主导多个AI技术落地项目（如无人机巡检、文档自动化处理）  ；具备跨部门资源整合与全流程执行能力，熟悉敏捷开发与产品生命周期管理  ；对技术趋势敏感，具备明确的产品迭代与商业化思维  , risks=无硬性门槛不匹配项  ；期望薪资未提供，后续需确认是否在JD薪资范围内  , basic_info={'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士（计算机技术）', 'current_position': '产品经理', 'expected_salary': '未提及'}
2025-08-25 11:26:19 - INFO - 🚀 启动智能简历评估系统
2025-08-25 11:26:19 - INFO - 📋 系统功能:
2025-08-25 11:26:19 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 11:26:19 - INFO -   - 多线程并发处理
2025-08-25 11:26:19 - INFO -   - 实时进度监控
2025-08-25 11:26:19 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 11:26:19 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 11:26:19 - INFO - 多进程任务管理已初始化
2025-08-25 11:26:23 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 11:26:23 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 11:26:23 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 11:26:41 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 11:26:41 - INFO - ✅ 发现已存在的JSON文件: ./user/未知用户/jd\AI产品经理.json
2025-08-25 11:26:41 - INFO - ✅ JSON文件格式验证通过
2025-08-25 11:26:41 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 11:26:41 - INFO - ✅ 发现已存在的JSON文件: ./user/杨锐雄/jd\AI产品经理.json
2025-08-25 11:26:41 - INFO - ✅ JSON文件格式验证通过
2025-08-25 11:26:42 - INFO - ⚠️ 跳过同名候选人: 曾杰，文件已存在
2025-08-25 11:26:42 - INFO - 开始处理简历: 曾杰
2025-08-25 11:26:42 - INFO - 开始第1轮评估: 曾杰
2025-08-25 11:26:42 - INFO - 岗位名称: AI产品经理
2025-08-25 11:26:42 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 11:26:42 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 11:27:08 - INFO - ✅ 第1轮评估完成: 曾杰 | 分数: 0.0
2025-08-25 11:27:08 - INFO - 检测到明确通过标识: ✅ 通过
2025-08-25 11:27:08 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_11：26_AI产品经理_一二点五三轮筛选结果/初筛合格\通过_曾杰_第1轮_评估报告.md
2025-08-25 11:27:08 - INFO - 开始提取候选人 曾杰 的初筛报告信息
2025-08-25 11:27:08 - INFO - 找到优势文本: **优势:**
    1. 拥有多个AI产品设计与落地经验，尤其在户外智能装备（如无人机巡检）领域具备深厚积累。
    2. 具备完整的项目管理与跨部门协作能力，能独立推动AI产品从需求分析到商业化落地的全流程。
    3. 技术背景扎实，熟悉Prompt工程、RAG、Agent等AI技术，具备LangChain开发经验。

- 
2025-08-25 11:27:08 - INFO - 找到优势文本: 优势:**
    1. 拥有多个AI产品设计与落地经验，尤其在户外智能装备（如无人机巡检）领域具备深厚积累。
    2. 具备完整的项目管理与跨部门协作能力，能独立推动AI产品从需求分析到商业化落地的全流程。
    3. 技术背景扎实，熟悉Prompt工程、RAG、Agent等AI技术，具备LangChain开发经验。

- **
2025-08-25 11:27:08 - INFO - 找到风险文本: **风险与不足:**
    - 无硬性门槛风险项。
    - 虽然候选人具备丰富的AI产品经验，但JD中提及“户外产品”应用场景，候选人主要集中在电力巡检领域，是否完全契合“户外用品市场并了解用户痛点”（加分项）需进一步评估。

**4. 初筛结论**

- **淘汰条件：**
    1. ✅ 无硬性门槛“不匹配”项。
    2. ✅ 核心职责匹配度为“高”。

- **通过条件：**
    1. ✅ 所有硬性门槛项均匹配。
    2. ✅ 核心职责匹配度为“高”。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历、年龄、薪资均符合JD要求，且在AI产品设计、跨部门协作、户外智能装备应用等方面具备丰富经验，与JD中核心职责高度匹配。虽未明确提及“户外用品市场”经验，但其在户外智能巡检领域的产品实践具有高度相关性，建议进入下一轮面试。
2025-08-25 11:27:08 - INFO - 提取的优势和风险: 优势=**, 风险=无硬性门槛风险项。；虽然候选人具备丰富的AI产品经验，但JD中提及“户外产品”应用场景，候选人主要集中在电力巡检领域，是否完全契合“户外用品市场并了解用户痛点”（加分项）需进一步评估。
2025-08-25 11:27:08 - INFO - 提取到姓名: 曾杰
2025-08-25 11:27:08 - INFO - 提取到年龄: 38岁
2025-08-25 11:27:08 - INFO - 提取到工作经验: 10年以上
2025-08-25 11:27:08 - INFO - 提取到学历: 硕士
2025-08-25 11:27:08 - INFO - 提取到当前职位: 产品经理
2025-08-25 11:27:08 - INFO - 提取到期望薪资: 未提供
2025-08-25 11:27:08 - INFO - 从初筛报告提取的基本信息: {'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提供'}
2025-08-25 11:27:08 - INFO - 候选人 曾杰 初筛报告提取完成: advantages=**, risks=无硬性门槛风险项。；虽然候选人具备丰富的AI产品经验，但JD中提及“户外产品”应用场景，候选人主要集中在电力巡检领域，是否完全契合“户外用品市场并了解用户痛点”（加分项）需进一步评估。, basic_info={'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提供'}
2025-08-25 11:27:08 - INFO - 开始第2轮评估: 曾杰
2025-08-25 11:27:08 - INFO - 岗位名称: AI产品经理
2025-08-25 11:27:08 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/专业画像分析.md
2025-08-25 11:27:08 - INFO - 第2轮评估使用模型: qwen-plus
2025-08-25 11:27:29 - WARNING - 分数不一致: 总分=88.0, 计算总分=0.0 (A:0.0+B:0.0+C:0.0+D:0.0+E:0.0)
2025-08-25 11:27:29 - WARNING - 候选人曾杰分数不一致: 总分=88.0, 计算总分=0.0
2025-08-25 11:27:29 - INFO - ✅ 第2轮评估完成: 曾杰 | 分数: 88.0
2025-08-25 11:27:29 - INFO - 未检测到明确关键词，默认通过
2025-08-25 11:27:29 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_11：26_AI产品经理_一二点五三轮筛选结果/画像分析结果\曾杰_第2轮_画像分析.md
2025-08-25 11:27:29 - INFO - 开始第2.5轮评估: 曾杰
2025-08-25 11:27:29 - INFO - 岗位名称: AI产品经理
2025-08-25 11:27:29 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/专业画像匹配.md
2025-08-25 11:27:29 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/专业画像匹配.md
2025-08-25 11:27:29 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-08-25 11:27:32 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-08-25 11:27:35 - INFO - ✅ 第2.5轮评估完成: 曾杰 | 分数: 0.0
2025-08-25 11:27:35 - INFO - 第2.5轮检测到明确通过标识: ✅ 通过
2025-08-25 11:27:35 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_11：26_AI产品经理_一二点五三轮筛选结果/二筛合格\通过_曾杰_第2.5轮_评估报告.md
2025-08-25 11:27:35 - INFO - 开始第3轮评估: 曾杰
2025-08-25 11:27:35 - INFO - 岗位名称: AI产品经理
2025-08-25 11:27:35 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/终极筛选_打分规则.md
2025-08-25 11:27:35 - INFO - 第3轮评估使用模型: qwen-plus
2025-08-25 11:33:20 - INFO - 🚀 启动智能简历评估系统
2025-08-25 11:33:20 - INFO - 📋 系统功能:
2025-08-25 11:33:20 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 11:33:20 - INFO -   - 多线程并发处理
2025-08-25 11:33:20 - INFO -   - 实时进度监控
2025-08-25 11:33:20 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 11:33:20 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 11:33:21 - INFO - 多进程任务管理已初始化
2025-08-25 11:33:33 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 11:33:33 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 11:33:33 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 11:33:55 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 11:34:27 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 11:38:15 - INFO - 🚀 启动智能简历评估系统
2025-08-25 11:38:15 - INFO - 📋 系统功能:
2025-08-25 11:38:15 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 11:38:15 - INFO -   - 多线程并发处理
2025-08-25 11:38:15 - INFO -   - 实时进度监控
2025-08-25 11:38:15 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 11:38:15 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 11:38:16 - INFO - 多进程任务管理已初始化
2025-08-25 11:40:31 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 11:40:31 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 11:40:31 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 11:40:32 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 11:40:37 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 11:40:37 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 11:40:37 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 11:40:38 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 11:40:38 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 11:40:49 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 11:40:57 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 11:40:59 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 11:40:59 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 11:41:05 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 11:41:12 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 11:41:12 - INFO - ✅ 发现已存在的JSON文件: ./user/未知用户/jd\AI产品经理.json
2025-08-25 11:41:13 - INFO - ✅ JSON文件格式验证通过
2025-08-25 11:41:13 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 11:41:13 - INFO - ✅ 发现已存在的JSON文件: ./user/杨锐雄/jd\AI产品经理.json
2025-08-25 11:41:13 - INFO - ✅ JSON文件格式验证通过
2025-08-25 11:41:13 - INFO - ⚠️ 跳过同名候选人: 安佰丽，文件已存在
2025-08-25 11:41:13 - INFO - 开始处理简历: 安佰丽
2025-08-25 11:41:13 - INFO - 开始第1轮评估: 安佰丽
2025-08-25 11:41:13 - INFO - 岗位名称: AI产品经理
2025-08-25 11:41:13 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 11:41:13 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 11:41:29 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 11:41:41 - INFO - ✅ 第1轮评估完成: 安佰丽 | 分数: 0.0
2025-08-25 11:41:41 - INFO - 检测到不合格关键词: 淘汰
2025-08-25 11:41:41 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_11：41_AI产品经理_一二点五三轮筛选结果/初筛不合格\未通过_安佰丽_第1轮_评估报告.md
2025-08-25 11:41:41 - INFO - 开始提取候选人 安佰丽 的初筛报告信息
2025-08-25 11:41:41 - INFO - 找到优势文本: **优势:**  
    - 拥有本科学历，符合学历门槛要求  
    - 有丰富的行政与人力资源管理经验，组织协调能力较强  

- 
2025-08-25 11:41:41 - INFO - 找到风险文本: **风险与不足:**  
    - **完全缺乏AI产品经理岗位所需的核心职责经验**，包括AI技术应用、产品规划、跨部门协作、用户需求转化等  
    - 简历中未体现任何与“AI”、“产品”、“项目管理”、“市场趋势”等相关的关键词或项目经历  
    - 虽然薪资期望未超过JD上限，但其经验与岗位要求严重脱节  

**4. 初筛结论**

- **淘汰条件：**
    1. 核心职责匹配度 评估结果为 "**低**"  

**筛选结果：** ❌ 淘汰  
**筛选理由：** 简历中未体现任何与AI产品经理岗位相关的职责经验或能力，核心职责匹配度为“低”。虽然学历和薪资期望符合要求，但缺乏关键岗位能力，无法胜任该职位。
2025-08-25 11:41:41 - INFO - 提取的优势和风险: 优势=拥有本科学历，符合学历门槛要求  ；有丰富的行政与人力资源管理经验，组织协调能力较强  , 风险=**完全缺乏AI产品经理岗位所需的核心职责经验**，包括AI技术应用、产品规划、跨部门协作、用户需求转化等  ；简历中未体现任何与“AI”、“产品”、“项目管理”、“市场趋势”等相关的关键词或项目经历  
2025-08-25 11:41:41 - INFO - 提取到姓名: 安伯丽
2025-08-25 11:41:41 - INFO - 提取到年龄: 40岁
2025-08-25 11:41:41 - INFO - 提取到工作经验: 约10年行政/人力资源管理相关经验
2025-08-25 11:41:41 - INFO - 提取到学历: 本科（法学，非全日制）
2025-08-25 11:41:41 - INFO - 提取到当前职位: 人力资源部主管
2025-08-25 11:41:41 - INFO - 提取到期望薪资: 12-15K
2025-08-25 11:41:41 - INFO - 从初筛报告提取的基本信息: {'name': '安伯丽', 'age': '40岁', 'experience': '约10年行政/人力资源管理相关经验', 'education': '本科（法学，非全日制）', 'current_position': '人力资源部主管', 'expected_salary': '12-15K'}
2025-08-25 11:41:41 - INFO - 候选人 安佰丽 初筛报告提取完成: advantages=拥有本科学历，符合学历门槛要求  ；有丰富的行政与人力资源管理经验，组织协调能力较强  , risks=**完全缺乏AI产品经理岗位所需的核心职责经验**，包括AI技术应用、产品规划、跨部门协作、用户需求转化等  ；简历中未体现任何与“AI”、“产品”、“项目管理”、“市场趋势”等相关的关键词或项目经历  , basic_info={'name': '安伯丽', 'age': '40岁', 'experience': '约10年行政/人力资源管理相关经验', 'education': '本科（法学，非全日制）', 'current_position': '人力资源部主管', 'expected_salary': '12-15K'}
2025-08-25 11:41:41 - INFO - ❌ 安佰丽 未通过第1轮筛选，得分: 0.0
2025-08-25 11:42:09 - ERROR - 导出Excel失败: [Errno 2] No such file or directory: './temp_未知用户/Report/简历评估结果_20250825_114209.xlsx'
2025-08-25 11:42:46 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 11:42:46 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 11:42:46 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 11:42:52 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 11:42:52 - INFO - ✅ 发现已存在的JSON文件: ./user/未知用户/jd\AI产品经理.json
2025-08-25 11:42:52 - INFO - ✅ JSON文件格式验证通过
2025-08-25 11:42:52 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 11:42:52 - INFO - ✅ 发现已存在的JSON文件: ./user/杨锐雄/jd\AI产品经理.json
2025-08-25 11:42:52 - INFO - ✅ JSON文件格式验证通过
2025-08-25 11:42:53 - INFO - ⚠️ 跳过同名候选人: 安佰丽，文件已存在
2025-08-25 11:42:53 - INFO - 开始处理简历: 安佰丽
2025-08-25 11:42:53 - INFO - 开始第1轮评估: 安佰丽
2025-08-25 11:42:53 - INFO - 岗位名称: AI产品经理
2025-08-25 11:42:53 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 11:42:53 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 11:43:05 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 11:43:09 - INFO - 查找历史记录路径: d:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 11:43:20 - INFO - ✅ 第1轮评估完成: 安佰丽 | 分数: 0.0
2025-08-25 11:43:20 - INFO - 检测到不合格关键词: 不匹配
2025-08-25 11:43:20 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_11：42_AI产品经理_一二点五三轮筛选结果/初筛不合格\未通过_安佰丽_第1轮_评估报告.md
2025-08-25 11:43:20 - INFO - 开始提取候选人 安佰丽 的初筛报告信息
2025-08-25 11:43:20 - INFO - 找到优势文本: **优势:**
    - 具备丰富的行政与人力资源管理经验，擅长组织制度建设与跨部门协调。
    - 拥有本科学历，符合岗位最低学历要求。

- 
2025-08-25 11:43:20 - INFO - 找到风险文本: **风险与不足:**
    - **核心职责匹配度低**：无AI、产品管理、用户需求转化、技术项目落地等方面的经验或成果。
    - **核心硬技能缺失**：缺乏AI技术应用能力、产品需求转化能力、项目全流程执行能力。
    - **岗位方向不匹配**：候选人背景与AI产品经理岗位存在本质性偏差。

---

**4. 初筛结论**

- **淘汰条件：**
    1. 核心职责匹配度评估结果为 "**低**"。

- **通过条件：**
    1. 不满足。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人虽满足学历、年龄、薪资等硬性门槛要求，但其**核心职责匹配度评估为“低”**，且**缺乏AI产品经理岗位所需的核心硬技能与实际经验**，不具备推动AI产品开发与落地的能力，与岗位需求存在本质性偏差。
2025-08-25 11:43:20 - INFO - 提取的优势和风险: 优势=具备丰富的行政与人力资源管理经验，擅长组织制度建设与跨部门协调。；拥有本科学历，符合岗位最低学历要求。, 风险=**核心职责匹配度低**：无AI、产品管理、用户需求转化、技术项目落地等方面的经验或成果。；**核心硬技能缺失**：缺乏AI技术应用能力、产品需求转化能力、项目全流程执行能力。
2025-08-25 11:43:20 - INFO - 提取到姓名: 安伯丽
2025-08-25 11:43:20 - INFO - 提取到年龄: 40岁
2025-08-25 11:43:20 - INFO - 提取到工作经验: 约10年（2010年至今，涵盖人事行政、人力资源管理等）
2025-08-25 11:43:20 - INFO - 提取到学历: 本科（中山开放大学，法学专业）
2025-08-25 11:43:20 - INFO - 提取到当前职位: 人力资源部主管
2025-08-25 11:43:20 - INFO - 提取到期望薪资: 12-15K
2025-08-25 11:43:20 - INFO - 从初筛报告提取的基本信息: {'name': '安伯丽', 'age': '40岁', 'experience': '约10年（2010年至今，涵盖人事行政、人力资源管理等）', 'education': '本科（中山开放大学，法学专业）', 'current_position': '人力资源部主管', 'expected_salary': '12-15K'}
2025-08-25 11:43:20 - INFO - 候选人 安佰丽 初筛报告提取完成: advantages=具备丰富的行政与人力资源管理经验，擅长组织制度建设与跨部门协调。；拥有本科学历，符合岗位最低学历要求。, risks=**核心职责匹配度低**：无AI、产品管理、用户需求转化、技术项目落地等方面的经验或成果。；**核心硬技能缺失**：缺乏AI技术应用能力、产品需求转化能力、项目全流程执行能力。, basic_info={'name': '安伯丽', 'age': '40岁', 'experience': '约10年（2010年至今，涵盖人事行政、人力资源管理等）', 'education': '本科（中山开放大学，法学专业）', 'current_position': '人力资源部主管', 'expected_salary': '12-15K'}
2025-08-25 11:43:20 - INFO - ❌ 安佰丽 未通过第1轮筛选，得分: 0.0
2025-08-25 11:43:24 - ERROR - 导出Excel失败: [Errno 2] No such file or directory: './temp_未知用户/Report/简历评估结果_20250825_114324.xlsx'
2025-08-25 11:47:19 - INFO - 🚀 启动智能简历评估系统
2025-08-25 11:47:19 - INFO - 📋 系统功能:
2025-08-25 11:47:19 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 11:47:19 - INFO -   - 多线程并发处理
2025-08-25 11:47:19 - INFO -   - 实时进度监控
2025-08-25 11:47:19 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 11:47:19 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 11:47:20 - INFO - 多进程任务管理已初始化
2025-08-25 11:47:24 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 11:47:24 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 11:47:24 - INFO - 查找历史记录路径: D:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 11:47:31 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 11:47:31 - INFO - ✅ 发现已存在的JSON文件: ./user/未知用户/jd\AI产品经理.json
2025-08-25 11:47:31 - INFO - ✅ JSON文件格式验证通过
2025-08-25 11:47:31 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 11:47:31 - INFO - ✅ 发现已存在的JSON文件: ./user/杨锐雄/jd\AI产品经理.json
2025-08-25 11:47:31 - INFO - ✅ JSON文件格式验证通过
2025-08-25 11:47:32 - INFO - ⚠️ 跳过同名候选人: 安佰丽，文件已存在
2025-08-25 11:47:32 - INFO - 开始处理简历: 安佰丽
2025-08-25 11:47:32 - INFO - 开始第1轮评估: 安佰丽
2025-08-25 11:47:32 - INFO - 岗位名称: AI产品经理
2025-08-25 11:47:32 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 11:47:32 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 11:47:57 - INFO - ✅ 第1轮评估完成: 安佰丽 | 分数: 0.0
2025-08-25 11:47:57 - INFO - 检测到不合格关键词: 淘汰
2025-08-25 11:47:57 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_11：47_AI产品经理_一二点五三轮筛选结果/初筛不合格\未通过_安佰丽_第1轮_评估报告.md
2025-08-25 11:47:57 - INFO - 开始提取候选人 安佰丽 的初筛报告信息
2025-08-25 11:47:57 - INFO - 找到优势文本: **优势:**  
    - 学历符合岗位要求（本科）  
    - 期望薪资低于JD上限，具备一定成本优势  

- 
2025-08-25 11:47:57 - INFO - 找到风险文本: **风险与不足:**  
    - **完全缺乏AI产品、技术应用、项目管理、用户需求转化等核心职责相关经验**  
    - 工作经历与JD中要求的“AI技术应用能力”、“产品需求转化能力”、“项目全流程执行能力”等硬技能无任何匹配  
    - 无任何跨部门协调推动技术产品落地的经验  
    - 年龄偏大（40岁），虽未设硬性门槛，但在AI产品领域可能存在适应性和学习曲线风险  

---

**4. 初筛结论**

- **淘汰条件：**
    1. 核心职责匹配度 评估结果为 "**低**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人虽学历匹配、年龄未设限、薪资匹配，但**完全缺乏AI产品经理岗位所需的核心职责经验与技能**，包括AI技术应用、产品规划、用户需求转化、跨部门协调、产品迭代优化等，无法胜任该岗位的基本要求。
2025-08-25 11:47:57 - INFO - 提取的优势和风险: 优势=学历符合岗位要求（本科）  ；期望薪资低于JD上限，具备一定成本优势  , 风险=**完全缺乏AI产品、技术应用、项目管理、用户需求转化等核心职责相关经验**  ；工作经历与JD中要求的“AI技术应用能力”、“产品需求转化能力”、“项目全流程执行能力”等硬技能无任何匹配  
2025-08-25 11:47:57 - INFO - 提取到姓名: 安伯丽
2025-08-25 11:47:57 - INFO - 提取到年龄: 40岁
2025-08-25 11:47:57 - INFO - 提取到工作经验: 超过10年人力资源及行政管理经验，无AI产品或技术相关经验
2025-08-25 11:47:57 - INFO - 提取到学历: 本科（法学，非全日制）
2025-08-25 11:47:57 - INFO - 提取到当前职位: 人力资源主管/经理
2025-08-25 11:47:57 - INFO - 提取到期望薪资: 12-15K
2025-08-25 11:47:57 - INFO - 从初筛报告提取的基本信息: {'name': '安伯丽', 'age': '40岁', 'experience': '超过10年人力资源及行政管理经验，无AI产品或技术相关经验', 'education': '本科（法学，非全日制）', 'current_position': '人力资源主管/经理', 'expected_salary': '12-15K'}
2025-08-25 11:47:57 - INFO - 候选人 安佰丽 初筛报告提取完成: advantages=学历符合岗位要求（本科）  ；期望薪资低于JD上限，具备一定成本优势  , risks=**完全缺乏AI产品、技术应用、项目管理、用户需求转化等核心职责相关经验**  ；工作经历与JD中要求的“AI技术应用能力”、“产品需求转化能力”、“项目全流程执行能力”等硬技能无任何匹配  , basic_info={'name': '安伯丽', 'age': '40岁', 'experience': '超过10年人力资源及行政管理经验，无AI产品或技术相关经验', 'education': '本科（法学，非全日制）', 'current_position': '人力资源主管/经理', 'expected_salary': '12-15K'}
2025-08-25 11:47:57 - INFO - ❌ 安佰丽 未通过第1轮筛选，得分: 0.0
2025-08-25 11:48:18 - INFO - 查找历史记录路径: D:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 11:48:28 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 11:48:28 - INFO - ✅ 发现已存在的JSON文件: ./user/未知用户/jd\AI产品经理.json
2025-08-25 11:48:28 - INFO - ✅ JSON文件格式验证通过
2025-08-25 11:48:28 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 11:48:28 - INFO - ✅ 发现已存在的JSON文件: ./user/杨锐雄/jd\AI产品经理.json
2025-08-25 11:48:28 - INFO - ✅ JSON文件格式验证通过
2025-08-25 11:48:29 - INFO - ⚠️ 跳过同名候选人: 安佰丽，文件已存在
2025-08-25 11:48:29 - INFO - 开始处理简历: 安佰丽
2025-08-25 11:48:29 - INFO - 开始第1轮评估: 安佰丽
2025-08-25 11:48:29 - INFO - 岗位名称: AI产品经理
2025-08-25 11:48:29 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 11:48:29 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 11:48:51 - INFO - ✅ 第1轮评估完成: 安佰丽 | 分数: 0.0
2025-08-25 11:48:51 - INFO - 检测到不合格关键词: 不匹配
2025-08-25 11:48:51 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_11：48_AI产品经理_一二点五三轮筛选结果/初筛不合格\未通过_安佰丽_第1轮_评估报告.md
2025-08-25 11:48:51 - INFO - 开始提取候选人 安佰丽 的初筛报告信息
2025-08-25 11:48:51 - INFO - 找到优势文本: **优势:**  
    - 拥有本科学历，符合岗位基本学历要求；
    - 具备丰富的跨部门沟通经验（如协调外包用工、企业文化建设等）；
    - 简历中体现出一定的组织协调与项目执行能力（如活动策划、制度制定等）。

- 
2025-08-25 11:48:51 - INFO - 找到风险文本: **风险与不足:**  
    - **缺乏与AI产品经理岗位直接相关的任何技能与经验**；
    - **无产品开发、AI技术应用、市场趋势分析、用户需求转化等能力体现**；
    - **期望薪资略低于JD薪资范围中位数，但非决定性优势**。

**4. 初筛结论**

- **淘汰条件：**
    1. ✅ 硬性门槛审查中无“不匹配”项；
    2. ✅ 核心职责匹配度为“低” → 满足淘汰条件。

- **通过条件：**
    1. ✅ 硬性门槛审查中无“不匹配”项；
    2. ❌ 核心职责匹配度为“低”，不满足通过条件。

**筛选结果：** ❌ 淘汰  
**筛选理由：**  
尽管候选人在学历、年龄、薪资期望方面均符合硬性要求，但其**无任何与AI产品经理岗位相关的核心职责经验**，如AI技术应用、产品创新、用户需求转化、跨部门协作开发等。**核心职责匹配度为“低”，不符合通过条件**。
2025-08-25 11:48:51 - INFO - 提取的优势和风险: 优势=- 具备丰富的跨部门沟通经验（如协调外包用工、企业文化建设等）；- 简历中体现出一定的组织协调与项目执行能力（如活动策划、制度制定等）。

-, 风险=- **无产品开发、AI技术应用、市场趋势分析、用户需求转化等能力体现**；- **期望薪资略低于JD薪资范围中位数，但非决定性优势**。

**4. 初筛结论**

- **淘汰条件：**
    1. ✅ 硬性门槛审查中无“不匹配”项
2025-08-25 11:48:51 - INFO - 提取到姓名: 安伯丽
2025-08-25 11:48:51 - INFO - 提取到年龄: 40岁
2025-08-25 11:48:51 - INFO - 提取到工作经验: 7年以上人力资源及行政管理经验
2025-08-25 11:48:51 - INFO - 提取到学历: 本科（法学，非全日制）
2025-08-25 11:48:51 - INFO - 提取到当前职位: 人力资源主管
2025-08-25 11:48:51 - INFO - 提取到期望薪资: 12-15K
2025-08-25 11:48:51 - INFO - 从初筛报告提取的基本信息: {'name': '安伯丽', 'age': '40岁', 'experience': '7年以上人力资源及行政管理经验', 'education': '本科（法学，非全日制）', 'current_position': '人力资源主管', 'expected_salary': '12-15K'}
2025-08-25 11:48:51 - INFO - 候选人 安佰丽 初筛报告提取完成: advantages=- 具备丰富的跨部门沟通经验（如协调外包用工、企业文化建设等）；- 简历中体现出一定的组织协调与项目执行能力（如活动策划、制度制定等）。

-, risks=- **无产品开发、AI技术应用、市场趋势分析、用户需求转化等能力体现**；- **期望薪资略低于JD薪资范围中位数，但非决定性优势**。

**4. 初筛结论**

- **淘汰条件：**
    1. ✅ 硬性门槛审查中无“不匹配”项, basic_info={'name': '安伯丽', 'age': '40岁', 'experience': '7年以上人力资源及行政管理经验', 'education': '本科（法学，非全日制）', 'current_position': '人力资源主管', 'expected_salary': '12-15K'}
2025-08-25 11:48:51 - INFO - ❌ 安佰丽 未通过第1轮筛选，得分: 0.0
2025-08-25 11:48:51 - INFO - ⚠️ 跳过同名候选人: 曾杰，文件已存在
2025-08-25 11:48:51 - INFO - 开始处理简历: 曾杰
2025-08-25 11:48:51 - INFO - 开始第1轮评估: 曾杰
2025-08-25 11:48:51 - INFO - 岗位名称: AI产品经理
2025-08-25 11:48:51 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 11:48:51 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 11:49:25 - INFO - ✅ 第1轮评估完成: 曾杰 | 分数: 0.0
2025-08-25 11:49:25 - INFO - 检测到明确通过标识: ✅ 通过
2025-08-25 11:49:25 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_11：48_AI产品经理_一二点五三轮筛选结果/初筛合格\通过_曾杰_第1轮_评估报告.md
2025-08-25 11:49:25 - INFO - 开始提取候选人 曾杰 的初筛报告信息
2025-08-25 11:49:25 - INFO - 找到优势文本: **优势:**
    - 具备多个AI技术落地的实际项目经验，尤其在无人机智能巡检领域有深度积累。
    - 拥有跨部门协调、全流程产品管理能力，且项目成果显著（年节约成本数百万）。
    - 对AI技术（如Prompt工程、RAG、Agent）有系统理解，具备AI产品设计能力。

- 
2025-08-25 11:49:25 - INFO - 找到风险文本: **风险与不足:**
    - 无硬性门槛“潜在风险”或“不匹配”项。
    - JD中强调“户外产品”，虽然候选人有无人机巡检项目经验，但该经验集中于电力行业而非消费级户外用品领域，可能在用户痛点理解上存在差异，属于**潜在风险**。

**4. 初筛结论**

- **淘汰条件：**
    - 不适用
- **通过条件：**
    - 所有硬性门槛匹配
    - 核心职责匹配度为“高”

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历、年龄、薪资均符合岗位要求；在AI技术落地、产品全流程管理、跨部门协作等方面具备扎实经验，且项目成果与JD核心职责高度契合。唯一潜在风险是其户外产品经验偏重工业场景，可能需要在面试中进一步考察其对消费级户外用品市场的理解。
2025-08-25 11:49:25 - INFO - 提取的优势和风险: 优势=具备多个AI技术落地的实际项目经验，尤其在无人机智能巡检领域有深度积累。；拥有跨部门协调、全流程产品管理能力，且项目成果显著（年节约成本数百万）。；对AI技术（如Prompt工程、RAG、Agent）有系统理解，具备AI产品设计能力。, 风险=在AI技术落地、产品全流程管理、跨部门协作等方面具备扎实经验，且项目成果与JD核心职责高度契合。唯一潜在风险是其户外产品经验偏重工业场景，可能需要在面试中进一步考察其对消费级户外用品市场的理解。
2025-08-25 11:49:25 - INFO - 提取到姓名: 曾杰
2025-08-25 11:49:25 - INFO - 提取到年龄: 38岁
2025-08-25 11:49:25 - INFO - 提取到工作经验: 10年以上
2025-08-25 11:49:25 - INFO - 提取到学历: 硕士
2025-08-25 11:49:25 - INFO - 提取到当前职位: 产品经理
2025-08-25 11:49:25 - INFO - 提取到期望薪资: 未提供
2025-08-25 11:49:25 - INFO - 从初筛报告提取的基本信息: {'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提供'}
2025-08-25 11:49:25 - INFO - 候选人 曾杰 初筛报告提取完成: advantages=具备多个AI技术落地的实际项目经验，尤其在无人机智能巡检领域有深度积累。；拥有跨部门协调、全流程产品管理能力，且项目成果显著（年节约成本数百万）。；对AI技术（如Prompt工程、RAG、Agent）有系统理解，具备AI产品设计能力。, risks=在AI技术落地、产品全流程管理、跨部门协作等方面具备扎实经验，且项目成果与JD核心职责高度契合。唯一潜在风险是其户外产品经验偏重工业场景，可能需要在面试中进一步考察其对消费级户外用品市场的理解。, basic_info={'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提供'}
2025-08-25 11:49:25 - INFO - 开始第2轮评估: 曾杰
2025-08-25 11:49:25 - INFO - 岗位名称: AI产品经理
2025-08-25 11:49:25 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/专业画像分析.md
2025-08-25 11:49:25 - INFO - 第2轮评估使用模型: qwen-plus
2025-08-25 11:50:01 - WARNING - 分数不一致: 总分=90.0, 计算总分=0.0 (A:0.0+B:0.0+C:0.0+D:0.0+E:0.0)
2025-08-25 11:50:01 - WARNING - 候选人曾杰分数不一致: 总分=90.0, 计算总分=0.0
2025-08-25 11:50:01 - INFO - ✅ 第2轮评估完成: 曾杰 | 分数: 90.0
2025-08-25 11:50:01 - INFO - 未检测到明确关键词，默认通过
2025-08-25 11:50:01 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_11：48_AI产品经理_一二点五三轮筛选结果/画像分析结果\曾杰_第2轮_画像分析.md
2025-08-25 11:50:01 - INFO - 开始第2.5轮评估: 曾杰
2025-08-25 11:50:01 - INFO - 岗位名称: AI产品经理
2025-08-25 11:50:01 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/专业画像匹配.md
2025-08-25 11:50:01 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/专业画像匹配.md
2025-08-25 11:50:01 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-08-25 11:50:04 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-08-25 11:50:06 - INFO - ✅ 第2.5轮评估完成: 曾杰 | 分数: 0.0
2025-08-25 11:50:06 - INFO - 第2.5轮检测到明确通过标识: ✅ 通过
2025-08-25 11:50:06 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_11：48_AI产品经理_一二点五三轮筛选结果/二筛合格\通过_曾杰_第2.5轮_评估报告.md
2025-08-25 11:50:06 - INFO - 开始第3轮评估: 曾杰
2025-08-25 11:50:06 - INFO - 岗位名称: AI产品经理
2025-08-25 11:50:06 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/终极筛选_打分规则.md
2025-08-25 11:50:06 - INFO - 第3轮评估使用模型: qwen-plus
2025-08-25 11:50:18 - INFO - 查找历史记录路径: D:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 11:50:53 - INFO - ✅ 第3轮评估完成: 曾杰 | 分数: 93.0
2025-08-25 11:50:53 - INFO - 第3轮检测到合格关键词: 通过
2025-08-25 11:50:53 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_11：48_AI产品经理_一二点五三轮筛选结果/最终通过\93.0分_曾杰_第3轮_评估报告.md
2025-08-25 11:50:53 - INFO - ✅ 曾杰 完成三轮筛选，最终得分: 93.0, 是否通过: True
2025-08-25 11:50:53 - INFO - 候选人 曾杰 最终返回信息: advantages=具备多个AI技术落地的实际项目经验，尤其在无人机智能巡检领域有深度积累。；拥有跨部门协调、全流程产品管理能力，且项目成果显著（年节约成本数百万）。；对AI技术（如Prompt工程、RAG、Agent）有系统理解，具备AI产品设计能力。, risks=在AI技术落地、产品全流程管理、跨部门协作等方面具备扎实经验，且项目成果与JD核心职责高度契合。唯一潜在风险是其户外产品经验偏重工业场景，可能需要在面试中进一步考察其对消费级户外用品市场的理解。, basic_info={'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提供'}
2025-08-25 11:50:53 - INFO - 查找历史记录路径: D:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 11:52:09 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 11:52:09 - INFO - ✅ 发现已存在的JSON文件: ./user/未知用户/jd\AI产品经理.json
2025-08-25 11:52:09 - INFO - ✅ JSON文件格式验证通过
2025-08-25 11:52:09 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 11:52:09 - INFO - ✅ 发现已存在的JSON文件: ./user/杨锐雄/jd\AI产品经理.json
2025-08-25 11:52:09 - INFO - ✅ JSON文件格式验证通过
2025-08-25 11:52:10 - INFO - ⚠️ 跳过同名候选人: 安佰丽，文件已存在
2025-08-25 11:52:10 - INFO - 开始处理简历: 安佰丽
2025-08-25 11:52:10 - INFO - 开始第1轮评估: 安佰丽
2025-08-25 11:52:10 - INFO - 岗位名称: AI产品经理
2025-08-25 11:52:10 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 11:52:10 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 11:52:19 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 11:52:19 - INFO - ✅ 发现已存在的JSON文件: ./user/未知用户/jd\AI产品经理.json
2025-08-25 11:52:19 - INFO - ✅ JSON文件格式验证通过
2025-08-25 11:52:19 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 11:52:19 - INFO - ✅ 发现已存在的JSON文件: ./user/杨锐雄/jd\AI产品经理.json
2025-08-25 11:52:19 - INFO - ✅ JSON文件格式验证通过
2025-08-25 11:52:20 - INFO - ⚠️ 跳过同名候选人: 安佰丽，文件已存在
2025-08-25 11:52:20 - INFO - 开始处理简历: 安佰丽
2025-08-25 11:52:20 - INFO - 开始第1轮评估: 安佰丽
2025-08-25 11:52:20 - INFO - 岗位名称: AI产品经理
2025-08-25 11:52:20 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 11:52:20 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 11:52:35 - INFO - ✅ 第1轮评估完成: 安佰丽 | 分数: 0.0
2025-08-25 11:52:35 - INFO - 检测到不合格关键词: 淘汰
2025-08-25 11:52:35 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_11：52_AI产品经理_一二点五三轮筛选结果/初筛不合格\未通过_安佰丽_第1轮_评估报告.md
2025-08-25 11:52:35 - INFO - 开始提取候选人 安佰丽 的初筛报告信息
2025-08-25 11:52:35 - INFO - 找到优势文本: **优势:**  
    - 本科学历达标  
    - 具备较强的行政统筹与跨部门协作能力（如管理多家分公司、协调多类行政事务）  
    - 对组织管理、制度建设有一定经验

- 
2025-08-25 11:52:35 - INFO - 找到风险文本: **风险与不足:**  
    - 无任何与AI技术、产品管理相关的经验  
    - 缺乏用户需求转化、产品设计、技术落地等核心职责相关能力  
    - 期望薪资略低于JD薪资范围下限（12-15K vs 14-20K），可能影响岗位匹配度  
    - 年龄40岁，虽非硬性门槛，但在AI产品岗位中可能面临学习曲线与适应性挑战  

---

**4. 初筛结论**

- **淘汰条件：**
    1. 核心职责匹配度评估结果为 "**低**"

- **通过条件：**
    1. 不符合

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人虽满足学历与薪资要求，但**无任何AI产品相关经验，也未体现出与JD中核心职责（如AI技术应用、用户需求转化、跨部门推进产品开发）相关的履历内容**，整体匹配度为“低”，不符合岗位要求。
2025-08-25 11:52:35 - INFO - 提取的优势和风险: 优势=本科学历达标  ；具备较强的行政统筹与跨部门协作能力（如管理多家分公司、协调多类行政事务）  ；对组织管理、制度建设有一定经验, 风险=无任何与AI技术、产品管理相关的经验  ；缺乏用户需求转化、产品设计、技术落地等核心职责相关能力  
2025-08-25 11:52:35 - INFO - 提取到姓名: 安伯丽
2025-08-25 11:52:35 - INFO - 提取到年龄: 40岁
2025-08-25 11:52:35 - INFO - 提取到工作经验: 8年以上人力资源及行政管理经验，无直接产品管理或AI技术相关工作经验
2025-08-25 11:52:35 - INFO - 提取到学历: 本科（法学，非全日制）
2025-08-25 11:52:35 - INFO - 提取到当前职位: 人力资源主管 / 人事经理
2025-08-25 11:52:35 - INFO - 提取到期望薪资: 12-15K
2025-08-25 11:52:35 - INFO - 从初筛报告提取的基本信息: {'name': '安伯丽', 'age': '40岁', 'experience': '8年以上人力资源及行政管理经验，无直接产品管理或AI技术相关工作经验', 'education': '本科（法学，非全日制）', 'current_position': '人力资源主管 / 人事经理', 'expected_salary': '12-15K'}
2025-08-25 11:52:35 - INFO - 候选人 安佰丽 初筛报告提取完成: advantages=本科学历达标  ；具备较强的行政统筹与跨部门协作能力（如管理多家分公司、协调多类行政事务）  ；对组织管理、制度建设有一定经验, risks=无任何与AI技术、产品管理相关的经验  ；缺乏用户需求转化、产品设计、技术落地等核心职责相关能力  , basic_info={'name': '安伯丽', 'age': '40岁', 'experience': '8年以上人力资源及行政管理经验，无直接产品管理或AI技术相关工作经验', 'education': '本科（法学，非全日制）', 'current_position': '人力资源主管 / 人事经理', 'expected_salary': '12-15K'}
2025-08-25 11:52:35 - INFO - ❌ 安佰丽 未通过第1轮筛选，得分: 0.0
2025-08-25 11:52:46 - INFO - ✅ 第1轮评估完成: 安佰丽 | 分数: 0.0
2025-08-25 11:52:46 - INFO - 检测到不合格关键词: 淘汰
2025-08-25 11:52:46 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_11：52_AI产品经理_一二点五三轮筛选结果/初筛不合格\未通过_安佰丽_第1轮_评估报告.md
2025-08-25 11:52:46 - INFO - 开始提取候选人 安佰丽 的初筛报告信息
2025-08-25 11:52:46 - INFO - 找到优势文本: **优势:**
    - 本科学历达标
    - 有丰富的跨部门沟通经验（基于人力资源岗位）
    - 有组织和推动执行能力（如活动策划、制度建设等）

- 
2025-08-25 11:52:46 - INFO - 找到风险文本: **风险与不足:**
    - **核心职责匹配度低**：缺乏AI技术理解、产品开发经验、用户需求转化能力等关键要素
    - **无AI相关项目经验**：不符合加分项
    - **无户外用品市场理解**：不符合加分项
    - **非技术背景出身**：难以理解AI技术落地与产品化过程

---

**4. 初筛结论**

- **淘汰条件：**
    1. 核心职责匹配度 评估结果为 "**低**"。

- **通过条件：**
    1. 不满足通过条件（核心职责匹配度低）

**筛选结果：** ❌ 淘汰  
**筛选理由：** 简历中未体现与AI产品经理岗位相关的任何核心职责经验或能力，如AI技术应用、产品创新、用户需求转化、跨部门协作推动技术落地等。尽管学历和薪资匹配，但核心职责匹配度为“低”，因此不符合初筛通过标准。
2025-08-25 11:52:46 - INFO - 提取的优势和风险: 优势=本科学历达标；有丰富的跨部门沟通经验（基于人力资源岗位）；有组织和推动执行能力（如活动策划、制度建设等）, 风险=**核心职责匹配度低**：缺乏AI技术理解、产品开发经验、用户需求转化能力等关键要素；**无AI相关项目经验**：不符合加分项
2025-08-25 11:52:46 - INFO - 提取到姓名: 安伯丽
2025-08-25 11:52:46 - INFO - 提取到年龄: 40岁
2025-08-25 11:52:46 - INFO - 提取到工作经验: 近10年人力资源及行政管理经验，未涉及AI或产品经理相关工作内容
2025-08-25 11:52:46 - INFO - 提取到学历: 本科（法学，非全日制）
2025-08-25 11:52:46 - INFO - 提取到当前职位: 人力资源主管/经理
2025-08-25 11:52:46 - INFO - 提取到期望薪资: 12-15K
2025-08-25 11:52:46 - INFO - 从初筛报告提取的基本信息: {'name': '安伯丽', 'age': '40岁', 'experience': '近10年人力资源及行政管理经验，未涉及AI或产品经理相关工作内容', 'education': '本科（法学，非全日制）', 'current_position': '人力资源主管/经理', 'expected_salary': '12-15K'}
2025-08-25 11:52:46 - INFO - 候选人 安佰丽 初筛报告提取完成: advantages=本科学历达标；有丰富的跨部门沟通经验（基于人力资源岗位）；有组织和推动执行能力（如活动策划、制度建设等）, risks=**核心职责匹配度低**：缺乏AI技术理解、产品开发经验、用户需求转化能力等关键要素；**无AI相关项目经验**：不符合加分项, basic_info={'name': '安伯丽', 'age': '40岁', 'experience': '近10年人力资源及行政管理经验，未涉及AI或产品经理相关工作内容', 'education': '本科（法学，非全日制）', 'current_position': '人力资源主管/经理', 'expected_salary': '12-15K'}
2025-08-25 11:52:46 - INFO - ❌ 安佰丽 未通过第1轮筛选，得分: 0.0
2025-08-25 11:54:35 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 11:54:35 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 11:54:35 - INFO - 查找历史记录路径: D:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 11:54:57 - INFO - 开始转换MD格式JD: 人力资源主管.md
2025-08-25 11:54:57 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/JD_md转json.md
2025-08-25 11:55:02 - INFO - ✅ 成功将MD格式JD转换为JSON格式并保存到用户目录: ./user/未知用户/jd\人力资源主管.json
2025-08-25 11:55:02 - INFO - 开始转换MD格式JD: 人力资源主管.md
2025-08-25 11:55:02 - INFO - ✅ 发现已存在的JSON文件: ./user/杨锐雄/jd\人力资源主管.json
2025-08-25 11:55:02 - INFO - ✅ JSON文件格式验证通过
2025-08-25 11:55:03 - INFO - ✅ 保存新候选人简历到VL目录: ./文本简历/VL_Result_人力资源主管\曾杰_VL识别结果.txt
2025-08-25 11:55:03 - INFO - 开始处理简历: 曾杰
2025-08-25 11:55:03 - INFO - 开始第1轮评估: 曾杰
2025-08-25 11:55:03 - INFO - 岗位名称: 人力资源主管
2025-08-25 11:55:03 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 11:55:03 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 11:55:26 - INFO - ✅ 第1轮评估完成: 曾杰 | 分数: 0.0
2025-08-25 11:55:26 - INFO - 检测到不合格关键词: 淘汰
2025-08-25 11:55:26 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_11：55_人力资源主管_一二点五三轮筛选结果/初筛不合格\未通过_曾杰_第1轮_评估报告.md
2025-08-25 11:55:26 - INFO - 开始提取候选人 曾杰 的初筛报告信息
2025-08-25 11:55:26 - INFO - 找到优势文本: **优势:**  
    - 学历高（硕士），且具备丰富的技术产品管理经验  
    - 具备良好的项目管理与跨部门协作能力  

- 
2025-08-25 11:55:26 - INFO - 找到风险文本: **风险与不足:**  
    - **核心职责匹配度低**，**缺乏人力资源管理相关经验**  
    - 期望薪资未提供，存在潜在风险（若超出JD上限6-7K的20%即8.4K，则构成风险）

**4. 初筛结论**

- **淘汰条件：**
    1. 核心职责匹配度 评估结果为 "**低**"。

- **筛选结果：** ❌ 淘汰  
- **筛选理由：** 候选人虽然满足学历、年龄、薪资等硬性门槛要求，但**缺乏与人力资源主管岗位相关的核心职责经验**，在简历中未体现任何关于人力资源战略规划、招聘体系搭建、员工关系管理、薪酬福利体系设计等方面的实际工作内容，因此**不满足岗位核心能力要求**。
2025-08-25 11:55:26 - INFO - 提取的优势和风险: 优势=学历高（硕士），且具备丰富的技术产品管理经验  ；具备良好的项目管理与跨部门协作能力  , 风险=**核心职责匹配度低**，**缺乏人力资源管理相关经验**  ；期望薪资未提供，存在潜在风险（若超出JD上限6-7K的20%即8.4K，则构成风险）
2025-08-25 11:55:26 - INFO - 提取到姓名: 曾杰
2025-08-25 11:55:26 - INFO - 提取到年龄: 38岁
2025-08-25 11:55:26 - INFO - 提取到工作经验: 10年以上
2025-08-25 11:55:26 - INFO - 提取到学历: 硕士
2025-08-25 11:55:26 - INFO - 提取到当前职位: 产品经理
2025-08-25 11:55:26 - INFO - 提取到期望薪资: 未提供
2025-08-25 11:55:26 - INFO - 从初筛报告提取的基本信息: {'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提供'}
2025-08-25 11:55:26 - INFO - 候选人 曾杰 初筛报告提取完成: advantages=学历高（硕士），且具备丰富的技术产品管理经验  ；具备良好的项目管理与跨部门协作能力  , risks=**核心职责匹配度低**，**缺乏人力资源管理相关经验**  ；期望薪资未提供，存在潜在风险（若超出JD上限6-7K的20%即8.4K，则构成风险）, basic_info={'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提供'}
2025-08-25 11:55:26 - INFO - ❌ 曾杰 未通过第1轮筛选，得分: 0.0
2025-08-25 13:41:07 - INFO - 🚀 启动智能简历评估系统
2025-08-25 13:41:07 - INFO - 📋 系统功能:
2025-08-25 13:41:07 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 13:41:07 - INFO -   - 多线程并发处理
2025-08-25 13:41:07 - INFO -   - 实时进度监控
2025-08-25 13:41:07 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 13:41:07 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 13:41:08 - INFO - 多进程任务管理已初始化
2025-08-25 13:41:22 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 13:41:22 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 13:41:22 - INFO - 查找历史记录路径: D:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 13:41:34 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 13:41:34 - INFO - ✅ 发现已存在的JSON文件: ./user/未知用户/jd\AI产品经理.json
2025-08-25 13:41:34 - INFO - ✅ JSON文件格式验证通过
2025-08-25 13:41:34 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 13:41:34 - INFO - ✅ 发现已存在的JSON文件: ./user/杨锐雄/jd\AI产品经理.json
2025-08-25 13:41:34 - INFO - ✅ JSON文件格式验证通过
2025-08-25 13:43:29 - INFO - 🚀 启动智能简历评估系统
2025-08-25 13:43:29 - INFO - 📋 系统功能:
2025-08-25 13:43:29 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 13:43:29 - INFO -   - 多线程并发处理
2025-08-25 13:43:29 - INFO -   - 实时进度监控
2025-08-25 13:43:29 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 13:43:29 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 13:43:30 - INFO - 多进程任务管理已初始化
2025-08-25 13:43:35 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 13:43:35 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 13:43:35 - INFO - 查找历史记录路径: D:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 13:43:43 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 13:43:43 - INFO - ✅ 发现已存在的JSON文件: ./user/未知用户/jd\AI产品经理.json
2025-08-25 13:43:43 - INFO - ✅ JSON文件格式验证通过
2025-08-25 13:43:43 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 13:43:43 - INFO - ✅ 发现已存在的JSON文件: ./user/杨锐雄/jd\AI产品经理.json
2025-08-25 13:43:43 - INFO - ✅ JSON文件格式验证通过
2025-08-25 13:43:44 - INFO - ⚠️ 跳过同名候选人: 安佰丽，文件已存在
2025-08-25 13:43:44 - INFO - 开始处理简历: 安佰丽
2025-08-25 13:43:44 - INFO - 开始第1轮评估: 安佰丽
2025-08-25 13:43:44 - INFO - 岗位名称: AI产品经理
2025-08-25 13:43:44 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 13:43:44 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 13:44:22 - INFO - ✅ 第1轮评估完成: 安佰丽 | 分数: 0.0
2025-08-25 13:44:22 - INFO - 检测到不合格关键词: 不匹配
2025-08-25 13:44:22 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_13：43_AI产品经理_一二点五三轮筛选结果/初筛不合格\未通过_安佰丽_第1轮_评估报告.md
2025-08-25 13:44:22 - INFO - 开始提取候选人 安佰丽 的初筛报告信息
2025-08-25 13:44:22 - INFO - 找到优势文本: **优势:** 
    - 本科学历符合要求
    - 有多年企业管理制度建设与跨部门协作经验（但为人力资源方向）
    - 沟通协调能力较强（在人力资源岗位中体现）

- 
2025-08-25 13:44:22 - INFO - 找到风险文本: **风险与不足:** 
    - 完全缺乏AI产品经理所需的产品设计、技术理解、用户需求转化、跨部门推进等核心能力
    - 无任何AI项目经验或相关行业背景
    - 期望薪资略低于JD薪资范围，虽匹配但可能影响后续沟通意愿

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。 → **否**
    2. 核心职责匹配度 评估结果为 "**低**"。 → **是**

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。 → **是**
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。 → **否**

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人虽满足学历和薪资要求，但**完全缺乏AI产品经理岗位所需的核心职责经验与能力**，包括AI技术应用、产品设计、用户需求洞察、跨部门协作推进等，核心职责匹配度评估为“低”，不符合进入下一轮面试的基本资格。
2025-08-25 13:44:22 - INFO - 提取的优势和风险: 优势=本科学历符合要求；有多年企业管理制度建设与跨部门协作经验（但为人力资源方向）；沟通协调能力较强（在人力资源岗位中体现）, 风险=完全缺乏AI产品经理所需的产品设计、技术理解、用户需求转化、跨部门推进等核心能力；无任何AI项目经验或相关行业背景
2025-08-25 13:44:22 - INFO - 提取到姓名: 安伯丽
2025-08-25 13:44:22 - INFO - 提取到年龄: 40岁
2025-08-25 13:44:22 - INFO - 提取到工作经验: 约10年人力资源与行政管理经验
2025-08-25 13:44:22 - INFO - 提取到学历: 本科（法学，中山开放大学，非全日制）
2025-08-25 13:44:22 - INFO - 提取到当前职位: 人力资源主管
2025-08-25 13:44:22 - INFO - 提取到期望薪资: 12-15K
2025-08-25 13:44:22 - INFO - 从初筛报告提取的基本信息: {'name': '安伯丽', 'age': '40岁', 'experience': '约10年人力资源与行政管理经验', 'education': '本科（法学，中山开放大学，非全日制）', 'current_position': '人力资源主管', 'expected_salary': '12-15K'}
2025-08-25 13:44:22 - INFO - 候选人 安佰丽 初筛报告提取完成: advantages=本科学历符合要求；有多年企业管理制度建设与跨部门协作经验（但为人力资源方向）；沟通协调能力较强（在人力资源岗位中体现）, risks=完全缺乏AI产品经理所需的产品设计、技术理解、用户需求转化、跨部门推进等核心能力；无任何AI项目经验或相关行业背景, basic_info={'name': '安伯丽', 'age': '40岁', 'experience': '约10年人力资源与行政管理经验', 'education': '本科（法学，中山开放大学，非全日制）', 'current_position': '人力资源主管', 'expected_salary': '12-15K'}
2025-08-25 13:44:22 - INFO - ❌ 安佰丽 未通过第1轮筛选，得分: 0.0
2025-08-25 13:54:57 - INFO - 查找历史记录路径: D:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 13:55:07 - INFO - 开始转换MD格式JD: 人力资源主管.md
2025-08-25 13:55:07 - INFO - ✅ 发现已存在的JSON文件: ./user/未知用户/jd\人力资源主管.json
2025-08-25 13:55:07 - INFO - ✅ JSON文件格式验证通过
2025-08-25 13:55:07 - INFO - 开始转换MD格式JD: 人力资源主管.md
2025-08-25 13:55:07 - INFO - ✅ 发现已存在的JSON文件: ./user/杨锐雄/jd\人力资源主管.json
2025-08-25 13:55:07 - INFO - ✅ JSON文件格式验证通过
2025-08-25 13:55:08 - INFO - ⚠️ 跳过同名候选人: 安佰丽，文件已存在
2025-08-25 13:55:08 - INFO - 开始处理简历: 安佰丽
2025-08-25 13:55:08 - INFO - 开始第1轮评估: 安佰丽
2025-08-25 13:55:08 - INFO - 岗位名称: 人力资源主管
2025-08-25 13:55:08 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 13:55:08 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 13:55:34 - INFO - ✅ 第1轮评估完成: 安佰丽 | 分数: 0.0
2025-08-25 13:55:34 - INFO - 检测到不合格关键词: 不匹配
2025-08-25 13:55:34 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_13：55_人力资源主管_一二点五三轮筛选结果/初筛不合格\未通过_安佰丽_第1轮_评估报告.md
2025-08-25 13:55:34 - INFO - 开始提取候选人 安佰丽 的初筛报告信息
2025-08-25 13:55:34 - INFO - 找到优势文本: **优势:**  
    - 丰富的HR主管经验（10年以上），具备统筹六大模块的能力  
    - 有大型员工管理经验（监督6000人）  
    - 具备员工生命周期管理、薪酬福利设计、企业文化建设等多方面能力  

- 
2025-08-25 13:55:34 - INFO - 找到风险文本: **风险与不足:**  
    - 学历为非全日制本科，虽符合门槛，但在某些企业可能存在偏好全日制的隐性要求  
    - **期望薪资严重超出JD薪资范围（15K vs 7K上限）**，属于硬性不匹配项  

**4. 初筛结论**

- **淘汰条件：**
    1. ✅ 硬性门槛审查 中有任意一项结果为 "**不匹配**"。（薪资不匹配）
    2. —— 核心职责匹配度 评估结果为 "**低**"。（未触发）

- **通过条件：**
    1. ❌ 硬性门槛审查 结果中**没有** "**不匹配**" 项。（存在薪资不匹配）
    2. ✅ 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。（实际为“高”）

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人虽然具备与岗位高度匹配的人力资源管理经验和能力，且学历符合要求，但其期望薪资（15K）远超JD薪资上限（7K）的1.2倍（8.4K），构成硬性不匹配项，因此被淘汰。
2025-08-25 13:55:34 - INFO - 提取的优势和风险: 优势=丰富的HR主管经验（10年以上），具备统筹六大模块的能力  ；有大型员工管理经验（监督6000人）  ；具备员工生命周期管理、薪酬福利设计、企业文化建设等多方面能力  , 风险=学历为非全日制本科，虽符合门槛，但在某些企业可能存在偏好全日制的隐性要求  ；**期望薪资严重超出JD薪资范围（15K vs 7K上限）**，属于硬性不匹配项  
2025-08-25 13:55:34 - INFO - 提取到姓名: 安伯丽
2025-08-25 13:55:34 - INFO - 提取到年龄: 40岁
2025-08-25 13:55:34 - INFO - 提取到工作经验: 约10年（2014.06 - 至今）
2025-08-25 13:55:34 - INFO - 提取到学历: 本科（中山开放大学，法学专业，非全日制）
2025-08-25 13:55:34 - INFO - 提取到当前职位: 中培集团（珠海市）有限公司 人力资源部/主管
2025-08-25 13:55:34 - INFO - 提取到期望薪资: 12-15K
2025-08-25 13:55:34 - INFO - 从初筛报告提取的基本信息: {'name': '安伯丽', 'age': '40岁', 'experience': '约10年（2014.06 - 至今）', 'education': '本科（中山开放大学，法学专业，非全日制）', 'current_position': '中培集团（珠海市）有限公司 人力资源部/主管', 'expected_salary': '12-15K'}
2025-08-25 13:55:34 - INFO - 候选人 安佰丽 初筛报告提取完成: advantages=丰富的HR主管经验（10年以上），具备统筹六大模块的能力  ；有大型员工管理经验（监督6000人）  ；具备员工生命周期管理、薪酬福利设计、企业文化建设等多方面能力  , risks=学历为非全日制本科，虽符合门槛，但在某些企业可能存在偏好全日制的隐性要求  ；**期望薪资严重超出JD薪资范围（15K vs 7K上限）**，属于硬性不匹配项  , basic_info={'name': '安伯丽', 'age': '40岁', 'experience': '约10年（2014.06 - 至今）', 'education': '本科（中山开放大学，法学专业，非全日制）', 'current_position': '中培集团（珠海市）有限公司 人力资源部/主管', 'expected_salary': '12-15K'}
2025-08-25 13:55:34 - INFO - ❌ 安佰丽 未通过第1轮筛选，得分: 0.0
2025-08-25 13:55:34 - INFO - ⚠️ 跳过同名候选人: 曾杰，文件已存在
2025-08-25 13:55:34 - INFO - 开始处理简历: 曾杰
2025-08-25 13:55:34 - INFO - 开始第1轮评估: 曾杰
2025-08-25 13:55:34 - INFO - 岗位名称: 人力资源主管
2025-08-25 13:55:34 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 13:55:34 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 13:56:12 - INFO - ✅ 第1轮评估完成: 曾杰 | 分数: 0.0
2025-08-25 13:56:12 - INFO - 检测到不合格关键词: 不匹配
2025-08-25 13:56:12 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_13：55_人力资源主管_一二点五三轮筛选结果/初筛不合格\未通过_曾杰_第1轮_评估报告.md
2025-08-25 13:56:12 - INFO - 开始提取候选人 曾杰 的初筛报告信息
2025-08-25 13:56:12 - INFO - 找到优势文本: **优势:**
    - 拥有10年以上的产品与项目管理经验，具备较强的组织协调与流程优化能力；
    - 曾主导多个大型项目，展现出良好的跨部门协作与执行力。

- 
2025-08-25 13:56:12 - INFO - 找到风险文本: **风险与不足:**
    - 无任何与人力资源管理相关的直接经验；
    - 缺乏员工生命周期管理、薪酬体系设计等核心职责的履历支撑；
    - 虽具备管理能力，但领域不匹配，需从零开始转型。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。 ❌
    2. 核心职责匹配度 评估结果为 "**低**"。 ✅

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。 ✅
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。 ❌

**筛选结果：** ❌ 淘汰  
**筛选理由：** 尽管候选人满足学历、年龄、薪资等硬性门槛要求，但其简历中**缺乏与人力资源主管岗位核心职责直接匹配的经验**，尤其是员工生命周期管理、薪酬体系设计等方面无相关履历证据，核心职责匹配度评估为“低”，因此不符合初筛通过标准。
2025-08-25 13:56:12 - INFO - 提取的优势和风险: 优势=- 曾主导多个大型项目，展现出良好的跨部门协作与执行力。

-, 风险=- 缺乏员工生命周期管理、薪酬体系设计等核心职责的履历支撑；- 虽具备管理能力，但领域不匹配，需从零开始转型。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。 ❌
    2. 核心职责匹配度 评估结果为 "**低**"。 ✅

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。 ✅
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。 ❌

**筛选结果：** ❌ 淘汰  
**筛选理由：** 尽管候选人满足学历、年龄、薪资等硬性门槛要求，但其简历中**缺乏与人力资源主管岗位核心职责直接匹配的经验**，尤其是员工生命周期管理、薪酬体系设计等方面无相关履历证据，核心职责匹配度评估为“低”，因此不符合初筛通过标准。
2025-08-25 13:56:12 - INFO - 提取到姓名: 曾杰
2025-08-25 13:56:12 - INFO - 提取到年龄: 38岁
2025-08-25 13:56:12 - INFO - 提取到工作经验: 10年以上
2025-08-25 13:56:12 - INFO - 提取到学历: 硕士
2025-08-25 13:56:12 - INFO - 提取到当前职位: 产品经理
2025-08-25 13:56:12 - INFO - 提取到期望薪资: 未提供
2025-08-25 13:56:12 - INFO - 从初筛报告提取的基本信息: {'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提供'}
2025-08-25 13:56:12 - INFO - 候选人 曾杰 初筛报告提取完成: advantages=- 曾主导多个大型项目，展现出良好的跨部门协作与执行力。

-, risks=- 缺乏员工生命周期管理、薪酬体系设计等核心职责的履历支撑；- 虽具备管理能力，但领域不匹配，需从零开始转型。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。 ❌
    2. 核心职责匹配度 评估结果为 "**低**"。 ✅

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。 ✅
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。 ❌

**筛选结果：** ❌ 淘汰  
**筛选理由：** 尽管候选人满足学历、年龄、薪资等硬性门槛要求，但其简历中**缺乏与人力资源主管岗位核心职责直接匹配的经验**，尤其是员工生命周期管理、薪酬体系设计等方面无相关履历证据，核心职责匹配度评估为“低”，因此不符合初筛通过标准。, basic_info={'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提供'}
2025-08-25 13:56:12 - INFO - ❌ 曾杰 未通过第1轮筛选，得分: 0.0
2025-08-25 13:57:27 - INFO - 查找历史记录路径: D:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 13:57:54 - INFO - 开始转换MD格式JD: 人力资源主管.md
2025-08-25 13:57:54 - INFO - ✅ 发现已存在的JSON文件: ./user/未知用户/jd\人力资源主管.json
2025-08-25 13:57:54 - INFO - ✅ JSON文件格式验证通过
2025-08-25 13:57:54 - INFO - 开始转换MD格式JD: 人力资源主管.md
2025-08-25 13:57:54 - INFO - ✅ 发现已存在的JSON文件: ./user/杨锐雄/jd\人力资源主管.json
2025-08-25 13:57:54 - INFO - ✅ JSON文件格式验证通过
2025-08-25 13:57:55 - INFO - ⚠️ 跳过同名候选人: 安佰丽，文件已存在
2025-08-25 13:57:55 - INFO - 开始处理简历: 安佰丽
2025-08-25 13:57:55 - INFO - 开始第1轮评估: 安佰丽
2025-08-25 13:57:55 - INFO - 岗位名称: 人力资源主管
2025-08-25 13:57:55 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 13:57:55 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 13:58:30 - INFO - ✅ 第1轮评估完成: 安佰丽 | 分数: 0.0
2025-08-25 13:58:30 - INFO - 检测到不合格关键词: 不匹配
2025-08-25 13:58:30 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_13：57_人力资源主管_一二点五三轮筛选结果/初筛不合格\未通过_安佰丽_第1轮_评估报告.md
2025-08-25 13:58:30 - INFO - 开始提取候选人 安佰丽 的初筛报告信息
2025-08-25 13:58:30 - INFO - 找到优势文本: **优势:**
    1. 具备多年人力资源主管经验，覆盖六大模块，熟悉员工生命周期管理；
    2. 多次参与薪酬与福利体系建设，并取得实际绩效成果（如平均工资提升、员工满意度提升）；
    3. 熟悉企业文化建设、制度制定、绩效管理等岗位核心职责。

- 
2025-08-25 13:58:30 - INFO - 找到风险文本: **风险与不足:**
    1. 期望薪资远高于JD薪资上限（15K vs 7K），存在显著不匹配；
    2. 所有学历为非全日制，可能影响部分企业对候选人的认可度（但JD未设全日制要求）；
    3. 无明确体现组织变革推动经验，仅在部分绩效中提到“支持业务发展”。

**4. 初筛结论**

- **淘汰条件：**
    1. ✅ 硬性门槛审查中有“不匹配”项（薪资）；
    2. ❌ 核心职责匹配度为“高”。

- **通过条件：**
    1. ❌ 存在“不匹配”项（薪资）；
    2. ✅ 核心职责匹配度为“高”。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人虽然具备丰富的人力资源主管经验，且核心职责匹配度高，但其期望薪资15K远高于JD薪资上限7K的1.2倍（8.4K），构成硬性不匹配项，因此被淘汰。
2025-08-25 13:58:30 - INFO - 提取的优势和风险: 优势=2. 多次参与薪酬与福利体系建设，并取得实际绩效成果（如平均工资提升、员工满意度提升）；3. 熟悉企业文化建设、制度制定、绩效管理等岗位核心职责。

-, 风险=2. 所有学历为非全日制，可能影响部分企业对候选人的认可度（但JD未设全日制要求）；3. 无明确体现组织变革推动经验，仅在部分绩效中提到“支持业务发展”。

**4. 初筛结论**

- **淘汰条件：**
    1. ✅ 硬性门槛审查中有“不匹配”项（薪资）
2025-08-25 13:58:30 - INFO - 提取到姓名: 安伯丽
2025-08-25 13:58:30 - INFO - 提取到年龄: 40岁
2025-08-25 13:58:30 - INFO - 提取到工作经验: 约10年（2010年至今）
2025-08-25 13:58:30 - INFO - 提取到学历: 本科
2025-08-25 13:58:30 - INFO - 提取到当前职位: 人力资源主管
2025-08-25 13:58:30 - INFO - 提取到期望薪资: 12-15K
2025-08-25 13:58:30 - INFO - 从初筛报告提取的基本信息: {'name': '安伯丽', 'age': '40岁', 'experience': '约10年（2010年至今）', 'education': '本科', 'current_position': '人力资源主管', 'expected_salary': '12-15K'}
2025-08-25 13:58:30 - INFO - 候选人 安佰丽 初筛报告提取完成: advantages=2. 多次参与薪酬与福利体系建设，并取得实际绩效成果（如平均工资提升、员工满意度提升）；3. 熟悉企业文化建设、制度制定、绩效管理等岗位核心职责。

-, risks=2. 所有学历为非全日制，可能影响部分企业对候选人的认可度（但JD未设全日制要求）；3. 无明确体现组织变革推动经验，仅在部分绩效中提到“支持业务发展”。

**4. 初筛结论**

- **淘汰条件：**
    1. ✅ 硬性门槛审查中有“不匹配”项（薪资）, basic_info={'name': '安伯丽', 'age': '40岁', 'experience': '约10年（2010年至今）', 'education': '本科', 'current_position': '人力资源主管', 'expected_salary': '12-15K'}
2025-08-25 13:58:30 - INFO - ❌ 安佰丽 未通过第1轮筛选，得分: 0.0
2025-08-25 13:58:30 - INFO - ⚠️ 跳过同名候选人: 曾杰，文件已存在
2025-08-25 13:58:30 - INFO - 开始处理简历: 曾杰
2025-08-25 13:58:30 - INFO - 开始第1轮评估: 曾杰
2025-08-25 13:58:30 - INFO - 岗位名称: 人力资源主管
2025-08-25 13:58:30 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 13:58:30 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 13:58:32 - INFO - 查找历史记录路径: D:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 13:58:53 - INFO - ✅ 第1轮评估完成: 曾杰 | 分数: 0.0
2025-08-25 13:58:53 - INFO - 检测到不合格关键词: 淘汰
2025-08-25 13:58:53 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_13：57_人力资源主管_一二点五三轮筛选结果/初筛不合格\未通过_曾杰_第1轮_评估报告.md
2025-08-25 13:58:53 - INFO - 开始提取候选人 曾杰 的初筛报告信息
2025-08-25 13:58:53 - INFO - 找到优势文本: **优势:**
    - 硕士学历，符合岗位学历要求；
    - 拥有丰富的项目管理经验，具备较强的组织协调与跨部门协作能力。

- 
2025-08-25 13:58:53 - INFO - 找到风险文本: **风险与不足:**
    - **核心职责匹配度低**，简历中未体现与人力资源管理相关的战略规划、员工生命周期管理或薪酬福利设计等关键经验；
    - 所有工作经历均围绕“产品经理”与“项目管理”展开，**职业路径与目标岗位存在明显偏差**。

---

**4. 初筛结论**

- **淘汰条件：**
    1. 核心职责匹配度评估结果为 "**低**"。

- **通过条件：**
    1. 未满足。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人虽满足学历、年龄、薪资等硬性门槛要求，但其简历中**未体现与人力资源主管岗位核心职责相关的任何实际经验**，核心职责匹配度为“低”，不符合进入下一轮面试的基本资格。
2025-08-25 13:58:53 - INFO - 提取的优势和风险: 优势=- 拥有丰富的项目管理经验，具备较强的组织协调与跨部门协作能力。

-, 风险=- 所有工作经历均围绕“产品经理”与“项目管理”展开，**职业路径与目标岗位存在明显偏差**。

---

**4. 初筛结论**

- **淘汰条件：**
    1. 核心职责匹配度评估结果为 "**低**"。

- **通过条件：**
    1. 未满足。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人虽满足学历、年龄、薪资等硬性门槛要求，但其简历中**未体现与人力资源主管岗位核心职责相关的任何实际经验**，核心职责匹配度为“低”，不符合进入下一轮面试的基本资格。
2025-08-25 13:58:53 - INFO - 提取到姓名: 曾杰
2025-08-25 13:58:53 - INFO - 提取到年龄: 38岁
2025-08-25 13:58:53 - INFO - 提取到工作经验: 10年以上
2025-08-25 13:58:53 - INFO - 提取到学历: 硕士
2025-08-25 13:58:53 - INFO - 提取到当前职位: 产品经理
2025-08-25 13:58:53 - INFO - 提取到期望薪资: 未提供
2025-08-25 13:58:53 - INFO - 从初筛报告提取的基本信息: {'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提供'}
2025-08-25 13:58:53 - INFO - 候选人 曾杰 初筛报告提取完成: advantages=- 拥有丰富的项目管理经验，具备较强的组织协调与跨部门协作能力。

-, risks=- 所有工作经历均围绕“产品经理”与“项目管理”展开，**职业路径与目标岗位存在明显偏差**。

---

**4. 初筛结论**

- **淘汰条件：**
    1. 核心职责匹配度评估结果为 "**低**"。

- **通过条件：**
    1. 未满足。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人虽满足学历、年龄、薪资等硬性门槛要求，但其简历中**未体现与人力资源主管岗位核心职责相关的任何实际经验**，核心职责匹配度为“低”，不符合进入下一轮面试的基本资格。, basic_info={'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提供'}
2025-08-25 13:58:53 - INFO - ❌ 曾杰 未通过第1轮筛选，得分: 0.0
2025-08-25 13:59:04 - INFO - 查找历史记录路径: D:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 13:59:40 - INFO - 查找历史记录路径: D:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 13:59:44 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 13:59:44 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 13:59:44 - INFO - 查找历史记录路径: D:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 13:59:48 - INFO - 爬取任务已启动: 7a23f5d6-b51d-4772-9918-21bab109efa3, 职位: AI产品经理
2025-08-25 13:59:48 - INFO - 📢 通知爬虫系统开始任务 7a23f5d6-b51d-4772-9918-21bab109efa3: AI产品经理, 目标数量: 20
2025-08-25 13:59:48 - INFO - ⏳ 等待爬虫系统通过API获取任务并开始执行...
2025-08-25 13:59:48 - INFO - 📋 爬虫系统需要调用以下API:
2025-08-25 13:59:48 - INFO -    1. GET /pending_crawl_tasks - 获取待处理任务
2025-08-25 13:59:48 - INFO -    2. POST /update_crawl_progress - 更新进度
2025-08-25 13:59:48 - INFO -    3. POST /complete_crawl_task - 提交结果
2025-08-25 14:00:10 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 14:00:10 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 14:00:10 - INFO - 查找历史记录路径: D:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 14:04:23 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 14:04:23 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 14:04:23 - INFO - 查找历史记录路径: D:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 14:04:29 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 14:04:29 - INFO - ✅ 发现已存在的JSON文件: ./user/未知用户/jd\AI产品经理.json
2025-08-25 14:04:29 - INFO - ✅ JSON文件格式验证通过
2025-08-25 14:04:29 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 14:04:29 - INFO - ✅ 发现已存在的JSON文件: ./user/杨锐雄/jd\AI产品经理.json
2025-08-25 14:04:29 - INFO - ✅ JSON文件格式验证通过
2025-08-25 14:04:30 - INFO - ⚠️ 跳过同名候选人: 曾杰，文件已存在
2025-08-25 14:04:30 - INFO - 开始处理简历: 曾杰
2025-08-25 14:04:30 - INFO - 开始第1轮评估: 曾杰
2025-08-25 14:04:30 - INFO - 岗位名称: AI产品经理
2025-08-25 14:04:30 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 14:04:30 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 14:04:49 - INFO - 🚀 启动智能简历评估系统
2025-08-25 14:04:49 - INFO - 📋 系统功能:
2025-08-25 14:04:49 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 14:04:49 - INFO -   - 多线程并发处理
2025-08-25 14:04:49 - INFO -   - 实时进度监控
2025-08-25 14:04:49 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 14:04:49 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 14:04:50 - INFO - 多进程任务管理已初始化
2025-08-25 14:04:56 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 14:04:56 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 14:04:56 - INFO - 查找历史记录路径: D:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 14:05:03 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 14:05:03 - INFO - ✅ 发现已存在的JSON文件: ./user/未知用户/jd\AI产品经理.json
2025-08-25 14:05:03 - INFO - ✅ JSON文件格式验证通过
2025-08-25 14:05:03 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 14:05:03 - INFO - ✅ 发现已存在的JSON文件: ./user/杨锐雄/jd\AI产品经理.json
2025-08-25 14:05:03 - INFO - ✅ JSON文件格式验证通过
2025-08-25 14:05:04 - INFO - ⚠️ 跳过同名候选人: 安佰丽，文件已存在
2025-08-25 14:05:04 - INFO - 开始处理简历: 安佰丽
2025-08-25 14:05:04 - INFO - 开始第1轮评估: 安佰丽
2025-08-25 14:05:04 - INFO - 岗位名称: AI产品经理
2025-08-25 14:05:04 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 14:05:04 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 14:05:26 - INFO - ✅ 第1轮评估完成: 安佰丽 | 分数: 0.0
2025-08-25 14:05:26 - INFO - 检测到不合格关键词: 淘汰
2025-08-25 14:05:26 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_14：05_AI产品经理_一二点五三轮筛选结果/初筛不合格\未通过_安佰丽_第1轮_评估报告.md
2025-08-25 14:05:26 - INFO - 开始提取候选人 安佰丽 的初筛报告信息
2025-08-25 14:05:26 - INFO - 找到优势文本: **优势:**  
    - 本科学历符合要求  
    - 具备多年管理经验，有组织协调和流程管理能力（非AI产品方向）

- 
2025-08-25 14:05:26 - INFO - 找到风险文本: **风险与不足:**  
    - **核心职责匹配度低**：完全缺乏AI、产品管理、用户需求转化、技术协同开发等方面的经验  
    - **薪资期望潜在风险**：期望薪资略低于JD薪资范围下限，可能在后续沟通中存在谈判空间问题  

**4. 初筛结论**

- **淘汰条件：**
    1. 核心职责匹配度 评估结果为 "**低**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人虽满足学历要求且无年龄限制冲突，但其全部工作经历集中在人力资源与行政管理领域，**无任何AI技术应用、产品管理、用户需求转化、跨部门协作等与AI产品经理岗位相关的核心职责经验**，因此核心职责匹配度为“低”，不符合初筛通过标准。此外，其期望薪资略低于JD薪资下限，存在潜在风险，进一步影响其匹配性。
2025-08-25 14:05:26 - INFO - 提取的优势和风险: 优势=本科学历符合要求  ；具备多年管理经验，有组织协调和流程管理能力（非AI产品方向）, 风险=**核心职责匹配度低**：完全缺乏AI、产品管理、用户需求转化、技术协同开发等方面的经验  ；**薪资期望潜在风险**：期望薪资略低于JD薪资范围下限，可能在后续沟通中存在谈判空间问题  
2025-08-25 14:05:26 - INFO - 提取到姓名: 安伯丽
2025-08-25 14:05:26 - INFO - 提取到年龄: 40岁
2025-08-25 14:05:26 - INFO - 提取到工作经验: 超过10年（2010年至今）
2025-08-25 14:05:26 - INFO - 提取到学历: 本科
2025-08-25 14:05:26 - INFO - 提取到当前职位: 人力资源主管 / 人事经理
2025-08-25 14:05:26 - INFO - 提取到期望薪资: 12-15K
2025-08-25 14:05:26 - INFO - 从初筛报告提取的基本信息: {'name': '安伯丽', 'age': '40岁', 'experience': '超过10年（2010年至今）', 'education': '本科', 'current_position': '人力资源主管 / 人事经理', 'expected_salary': '12-15K'}
2025-08-25 14:05:26 - INFO - 候选人 安佰丽 初筛报告提取完成: advantages=本科学历符合要求  ；具备多年管理经验，有组织协调和流程管理能力（非AI产品方向）, risks=**核心职责匹配度低**：完全缺乏AI、产品管理、用户需求转化、技术协同开发等方面的经验  ；**薪资期望潜在风险**：期望薪资略低于JD薪资范围下限，可能在后续沟通中存在谈判空间问题  , basic_info={'name': '安伯丽', 'age': '40岁', 'experience': '超过10年（2010年至今）', 'education': '本科', 'current_position': '人力资源主管 / 人事经理', 'expected_salary': '12-15K'}
2025-08-25 14:05:26 - INFO - ❌ 安佰丽 未通过第1轮筛选，得分: 0.0
2025-08-25 14:06:27 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 14:06:27 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 14:06:27 - INFO - 查找历史记录路径: D:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 14:06:29 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 14:06:29 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 14:06:29 - INFO - 查找历史记录路径: D:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 14:06:44 - INFO - 开始转换MD格式JD: 人力资源主管.md
2025-08-25 14:06:44 - INFO - ✅ 发现已存在的JSON文件: ./user/未知用户/jd\人力资源主管.json
2025-08-25 14:06:44 - INFO - ✅ JSON文件格式验证通过
2025-08-25 14:06:44 - INFO - 开始转换MD格式JD: 人力资源主管.md
2025-08-25 14:06:44 - INFO - ✅ 发现已存在的JSON文件: ./user/杨锐雄/jd\人力资源主管.json
2025-08-25 14:06:44 - INFO - ✅ JSON文件格式验证通过
2025-08-25 14:06:45 - INFO - ⚠️ 跳过同名候选人: 曾杰，文件已存在
2025-08-25 14:06:45 - INFO - 开始处理简历: 曾杰
2025-08-25 14:06:45 - INFO - 开始第1轮评估: 曾杰
2025-08-25 14:06:45 - INFO - 岗位名称: 人力资源主管
2025-08-25 14:06:45 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 14:06:45 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 14:07:11 - INFO - ✅ 第1轮评估完成: 曾杰 | 分数: 0.0
2025-08-25 14:07:11 - INFO - 检测到不合格关键词: 淘汰
2025-08-25 14:07:11 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_14：06_人力资源主管_一二点五三轮筛选结果/初筛不合格\未通过_曾杰_第1轮_评估报告.md
2025-08-25 14:07:11 - INFO - 开始提取候选人 曾杰 的初筛报告信息
2025-08-25 14:07:11 - INFO - 找到优势文本: **优势:** 候选人具备10年以上产品管理经验，拥有硕士学历背景，具备较强的项目统筹和产品设计能力，尤其在AI产品与智能系统方面有丰富经验。
- 
2025-08-25 14:07:11 - INFO - 找到优势文本: 优势:** 候选人具备10年以上产品管理经验，拥有硕士学历背景，具备较强的项目统筹和产品设计能力，尤其在AI产品与智能系统方面有丰富经验。
- **
2025-08-25 14:07:11 - INFO - 找到风险文本: **风险与不足:** 
    - 候选人缺乏人力资源管理相关经验，特别是JD中要求的核心职责均未在简历中体现；
    - 尽管薪资未提供，但若候选人实际期望超过JD上限20%仍可能构成潜在风险；
    - 年龄虽未设限，但结合岗位性质（人力资源主管通常更偏好稳定性强的候选人），38岁在部分企业中可能被视为偏高。

**4. 初筛结论**

- **淘汰条件：**
    1. 核心职责匹配度 评估结果为 "**低**"。
- **通过条件：**
    1. 不满足

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人虽然满足学历、年龄、薪资等硬性门槛要求，但其过往经历集中在产品管理与技术领域，缺乏人力资源管理尤其是统筹管理、员工生命周期管理、薪酬体系设计等方面的工作经验，与JD中要求的核心职责匹配度为“低”，不满足进入下一轮面试的基本资格。
2025-08-25 14:07:11 - INFO - 提取的优势和风险: 优势=**, 风险=- 尽管薪资未提供，但若候选人实际期望超过JD上限20%仍可能构成潜在风险；- 年龄虽未设限，但结合岗位性质（人力资源主管通常更偏好稳定性强的候选人），38岁在部分企业中可能被视为偏高。

**4. 初筛结论**

- **淘汰条件：**
    1. 核心职责匹配度 评估结果为 "**低**"。
- **通过条件：**
    1. 不满足

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人虽然满足学历、年龄、薪资等硬性门槛要求，但其过往经历集中在产品管理与技术领域，缺乏人力资源管理尤其是统筹管理、员工生命周期管理、薪酬体系设计等方面的工作经验，与JD中要求的核心职责匹配度为“低”，不满足进入下一轮面试的基本资格。
2025-08-25 14:07:11 - INFO - 提取到姓名: 曾杰
2025-08-25 14:07:11 - INFO - 提取到年龄: 38岁
2025-08-25 14:07:11 - INFO - 提取到工作经验: 10年以上
2025-08-25 14:07:11 - INFO - 提取到学历: 硕士
2025-08-25 14:07:11 - INFO - 提取到当前职位: 产品经理
2025-08-25 14:07:11 - INFO - 提取到期望薪资: 未提供
2025-08-25 14:07:11 - INFO - 从初筛报告提取的基本信息: {'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提供'}
2025-08-25 14:07:11 - INFO - 候选人 曾杰 初筛报告提取完成: advantages=**, risks=- 尽管薪资未提供，但若候选人实际期望超过JD上限20%仍可能构成潜在风险；- 年龄虽未设限，但结合岗位性质（人力资源主管通常更偏好稳定性强的候选人），38岁在部分企业中可能被视为偏高。

**4. 初筛结论**

- **淘汰条件：**
    1. 核心职责匹配度 评估结果为 "**低**"。
- **通过条件：**
    1. 不满足

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人虽然满足学历、年龄、薪资等硬性门槛要求，但其过往经历集中在产品管理与技术领域，缺乏人力资源管理尤其是统筹管理、员工生命周期管理、薪酬体系设计等方面的工作经验，与JD中要求的核心职责匹配度为“低”，不满足进入下一轮面试的基本资格。, basic_info={'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提供'}
2025-08-25 14:07:11 - INFO - ❌ 曾杰 未通过第1轮筛选，得分: 0.0
2025-08-25 14:46:46 - INFO - 🚀 启动智能简历评估系统
2025-08-25 14:46:46 - INFO - 📋 系统功能:
2025-08-25 14:46:46 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 14:46:46 - INFO -   - 多线程并发处理
2025-08-25 14:46:46 - INFO -   - 实时进度监控
2025-08-25 14:46:46 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 14:46:46 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 14:46:47 - INFO - 多进程任务管理已初始化
2025-08-25 14:47:08 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 14:47:08 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 14:47:08 - INFO - 查找历史记录路径: D:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 14:47:14 - INFO - 开始转换MD格式JD: 人力资源主管.md
2025-08-25 14:47:14 - INFO - ✅ 发现已存在的JSON文件: ./user/未知用户/jd\人力资源主管.json
2025-08-25 14:47:14 - INFO - ✅ JSON文件格式验证通过
2025-08-25 14:47:14 - INFO - 开始转换MD格式JD: 人力资源主管.md
2025-08-25 14:47:14 - INFO - ✅ 发现已存在的JSON文件: ./user/杨锐雄/jd\人力资源主管.json
2025-08-25 14:47:14 - INFO - ✅ JSON文件格式验证通过
2025-08-25 14:47:15 - INFO - ⚠️ 跳过同名候选人: 安佰丽，文件已存在
2025-08-25 14:47:15 - INFO - 开始处理简历: 安佰丽
2025-08-25 14:47:15 - INFO - 开始第1轮评估: 安佰丽
2025-08-25 14:47:15 - INFO - 岗位名称: 人力资源主管
2025-08-25 14:47:15 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 14:47:15 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 14:47:46 - INFO - ✅ 第1轮评估完成: 安佰丽 | 分数: 0.0
2025-08-25 14:47:46 - INFO - 检测到不合格关键词: 不匹配
2025-08-25 14:47:46 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_14：47_人力资源主管_一二点五三轮筛选结果/初筛不合格\未通过_安佰丽_第1轮_评估报告.md
2025-08-25 14:47:46 - INFO - 开始提取候选人 安佰丽 的初筛报告信息
2025-08-25 14:47:46 - INFO - 找到优势文本: **优势:**
    - 拥有多年人力资源管理经验，涵盖人事六大模块、员工关系、招聘与培训等；
    - 具备企业文化建设、员工生命周期管理等经验，与JD要求高度契合。

- 
2025-08-25 14:47:46 - INFO - 找到风险文本: **风险与不足:**
    - **薪资期望远高于JD薪资上限（15K vs 7K）**；
    - 缺乏明确的薪酬福利体系设计经验描述；
    - 教育为非全日制本科，可能在部分企业中存在接受度问题。

---

**4. 初筛结论**

- **淘汰条件：**
    1. ✅ 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. ❌ 核心职责匹配度 评估结果为 "**低**"。

- **通过条件：**
    1. ❌ 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. ✅ 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人虽具备丰富的HR管理经验，与岗位核心职责匹配度为“中”，但其期望薪资（12-15K）远高于JD薪资上限（6-7K），超出合理浮动范围（1.2倍），属于**硬性门槛“不匹配”项**，因此被淘汰。
2025-08-25 14:47:46 - INFO - 提取的优势和风险: 优势=- 具备企业文化建设、员工生命周期管理等经验，与JD要求高度契合。

-, 风险=- 缺乏明确的薪酬福利体系设计经验描述；- 教育为非全日制本科，可能在部分企业中存在接受度问题。

---

**4. 初筛结论**

- **淘汰条件：**
    1. ✅ 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. ❌ 核心职责匹配度 评估结果为 "**低**"。

- **通过条件：**
    1. ❌ 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. ✅ 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人虽具备丰富的HR管理经验，与岗位核心职责匹配度为“中”，但其期望薪资（12-15K）远高于JD薪资上限（6-7K），超出合理浮动范围（1.2倍），属于**硬性门槛“不匹配”项**，因此被淘汰。
2025-08-25 14:47:46 - INFO - 提取到姓名: 安伯丽
2025-08-25 14:47:46 - INFO - 提取到年龄: 40岁
2025-08-25 14:47:46 - INFO - 提取到工作经验: 超过8年（2014.06 - 至今）
2025-08-25 14:47:46 - INFO - 提取到学历: 本科（中山开放大学，法学，非全日制）
2025-08-25 14:47:46 - INFO - 提取到当前职位: 人力资源主管
2025-08-25 14:47:46 - INFO - 提取到期望薪资: 12-15K
2025-08-25 14:47:46 - INFO - 从初筛报告提取的基本信息: {'name': '安伯丽', 'age': '40岁', 'experience': '超过8年（2014.06 - 至今）', 'education': '本科（中山开放大学，法学，非全日制）', 'current_position': '人力资源主管', 'expected_salary': '12-15K'}
2025-08-25 14:47:46 - INFO - 候选人 安佰丽 初筛报告提取完成: advantages=- 具备企业文化建设、员工生命周期管理等经验，与JD要求高度契合。

-, risks=- 缺乏明确的薪酬福利体系设计经验描述；- 教育为非全日制本科，可能在部分企业中存在接受度问题。

---

**4. 初筛结论**

- **淘汰条件：**
    1. ✅ 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. ❌ 核心职责匹配度 评估结果为 "**低**"。

- **通过条件：**
    1. ❌ 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. ✅ 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人虽具备丰富的HR管理经验，与岗位核心职责匹配度为“中”，但其期望薪资（12-15K）远高于JD薪资上限（6-7K），超出合理浮动范围（1.2倍），属于**硬性门槛“不匹配”项**，因此被淘汰。, basic_info={'name': '安伯丽', 'age': '40岁', 'experience': '超过8年（2014.06 - 至今）', 'education': '本科（中山开放大学，法学，非全日制）', 'current_position': '人力资源主管', 'expected_salary': '12-15K'}
2025-08-25 14:47:46 - INFO - ❌ 安佰丽 未通过第1轮筛选，得分: 0.0
2025-08-25 14:52:15 - INFO - 🚀 启动智能简历评估系统
2025-08-25 14:52:15 - INFO - 📋 系统功能:
2025-08-25 14:52:15 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 14:52:15 - INFO -   - 多线程并发处理
2025-08-25 14:52:15 - INFO -   - 实时进度监控
2025-08-25 14:52:15 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 14:52:15 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 14:52:16 - INFO - 多进程任务管理已初始化
2025-08-25 14:52:24 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 14:52:24 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 14:52:24 - INFO - 查找历史记录路径: D:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 14:52:29 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 14:52:29 - INFO - ✅ 发现已存在的JSON文件: ./user/未知用户/jd\AI产品经理.json
2025-08-25 14:52:29 - INFO - ✅ JSON文件格式验证通过
2025-08-25 14:52:29 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 14:52:29 - INFO - ✅ 发现已存在的JSON文件: ./user/杨锐雄/jd\AI产品经理.json
2025-08-25 14:52:29 - INFO - ✅ JSON文件格式验证通过
2025-08-25 14:52:30 - INFO - ⚠️ 跳过同名候选人: 安佰丽，文件已存在
2025-08-25 14:52:30 - INFO - 开始处理简历: 安佰丽
2025-08-25 14:52:30 - INFO - 开始第1轮评估: 安佰丽
2025-08-25 14:52:30 - INFO - 岗位名称: AI产品经理
2025-08-25 14:52:30 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 14:52:30 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 14:52:51 - INFO - ✅ 第1轮评估完成: 安佰丽 | 分数: 0.0
2025-08-25 14:52:51 - INFO - 检测到不合格关键词: 不匹配
2025-08-25 14:52:51 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_14：52_AI产品经理_一二点五三轮筛选结果/初筛不合格\未通过_安佰丽_第1轮_评估报告.md
2025-08-25 14:52:51 - INFO - 开始提取候选人 安佰丽 的初筛报告信息
2025-08-25 14:52:51 - INFO - 找到优势文本: **优势:** 学历达标，具备良好的组织协调与管理能力，期望薪资低于岗位薪资上限。

- 
2025-08-25 14:52:51 - INFO - 找到优势文本: 优势:** 学历达标，具备良好的组织协调与管理能力，期望薪资低于岗位薪资上限。

- **
2025-08-25 14:52:51 - INFO - 找到风险文本: **风险与不足:**
    - 核心职责匹配度评估为“低”
    - 缺乏AI产品经理岗位所需的专业背景与项目经验
    - 无AI技术应用、产品设计、用户需求转化、项目执行等能力体现

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。 → 不适用
    2. 核心职责匹配度 评估结果为 "**低**"。 → 符合

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。 → 符合
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。 → 不符合

**筛选结果：** ❌ 淘汰  
**筛选理由：** 简历中未体现任何与AI产品经理岗位相关的核心能力或项目经验，**核心职责匹配度评估为“低”**，不符合岗位基本要求。尽管硬性门槛均匹配，但核心能力严重缺失，无法胜任该岗位职责。
2025-08-25 14:52:51 - INFO - 提取的优势和风险: 优势=**, 风险=核心职责匹配度评估为“低”；缺乏AI产品经理岗位所需的专业背景与项目经验
2025-08-25 14:52:51 - INFO - 提取到姓名: 安伯丽
2025-08-25 14:52:51 - INFO - 提取到年龄: 40岁
2025-08-25 14:52:51 - INFO - 提取到工作经验: 超过10年行政/人力资源相关经验，无AI、产品经理相关经验
2025-08-25 14:52:51 - INFO - 提取到学历: 本科（法学，非全日制）
2025-08-25 14:52:51 - INFO - 提取到当前职位: 人力资源部主管
2025-08-25 14:52:51 - INFO - 提取到期望薪资: 12-15K
2025-08-25 14:52:51 - INFO - 从初筛报告提取的基本信息: {'name': '安伯丽', 'age': '40岁', 'experience': '超过10年行政/人力资源相关经验，无AI、产品经理相关经验', 'education': '本科（法学，非全日制）', 'current_position': '人力资源部主管', 'expected_salary': '12-15K'}
2025-08-25 14:52:51 - INFO - 候选人 安佰丽 初筛报告提取完成: advantages=**, risks=核心职责匹配度评估为“低”；缺乏AI产品经理岗位所需的专业背景与项目经验, basic_info={'name': '安伯丽', 'age': '40岁', 'experience': '超过10年行政/人力资源相关经验，无AI、产品经理相关经验', 'education': '本科（法学，非全日制）', 'current_position': '人力资源部主管', 'expected_salary': '12-15K'}
2025-08-25 14:52:51 - INFO - ❌ 安佰丽 未通过第1轮筛选，得分: 0.0
2025-08-25 14:55:38 - INFO - 🚀 启动智能简历评估系统
2025-08-25 14:55:38 - INFO - 📋 系统功能:
2025-08-25 14:55:38 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 14:55:38 - INFO -   - 多线程并发处理
2025-08-25 14:55:38 - INFO -   - 实时进度监控
2025-08-25 14:55:38 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 14:55:38 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 14:55:39 - INFO - 多进程任务管理已初始化
2025-08-25 14:55:42 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 14:55:42 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 14:55:42 - INFO - 查找历史记录路径: D:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 14:55:50 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 14:55:50 - INFO - ✅ 发现已存在的JSON文件: ./user/未知用户/jd\AI产品经理.json
2025-08-25 14:55:50 - INFO - ✅ JSON文件格式验证通过
2025-08-25 14:55:50 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-08-25 14:55:50 - INFO - ✅ 发现已存在的JSON文件: ./user/杨锐雄/jd\AI产品经理.json
2025-08-25 14:55:50 - INFO - ✅ JSON文件格式验证通过
2025-08-25 14:55:51 - INFO - ⚠️ 跳过同名候选人: 安佰丽，文件已存在
2025-08-25 14:55:51 - INFO - 开始处理简历: 安佰丽
2025-08-25 14:55:51 - INFO - 开始第1轮评估: 安佰丽
2025-08-25 14:55:51 - INFO - 岗位名称: AI产品经理
2025-08-25 14:55:51 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 14:55:51 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 14:56:16 - INFO - ✅ 第1轮评估完成: 安佰丽 | 分数: 0.0
2025-08-25 14:56:16 - INFO - 检测到不合格关键词: 淘汰
2025-08-25 14:56:16 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_14：55_AI产品经理_一二点五三轮筛选结果/初筛不合格\未通过_安佰丽_第1轮_评估报告.md
2025-08-25 14:56:16 - INFO - 开始提取候选人 安佰丽 的初筛报告信息
2025-08-25 14:56:16 - INFO - 找到优势文本: **优势:**  
    - 本科学历符合要求  
    - 具备多年人力资源经验，具备一定的组织协调与制度建设能力  

- 
2025-08-25 14:56:16 - INFO - 找到风险文本: **风险与不足:**  
    - **核心职责匹配度低**：无AI、产品管理、用户需求转化等相关经验  
    - **潜在风险**：期望薪资略低于JD薪资下限，可能存在岗位理解偏差或定位不准确  

**4. 初筛结论**

- **淘汰条件：**
    1. 核心职责匹配度评估结果为 "**低**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人虽满足学历门槛，但其全部工作经历集中于人力资源与行政管理领域，**缺乏与AI产品经理岗位相关的任何经验或能力证据**，核心职责匹配度为“低”。此外，期望薪资略低于JD薪资下限，存在定位偏差的潜在风险。
2025-08-25 14:56:16 - INFO - 提取的优势和风险: 优势=本科学历符合要求  ；具备多年人力资源经验，具备一定的组织协调与制度建设能力  , 风险=**核心职责匹配度低**：无AI、产品管理、用户需求转化等相关经验  ；**潜在风险**：期望薪资略低于JD薪资下限，可能存在岗位理解偏差或定位不准确  
2025-08-25 14:56:16 - INFO - 提取到姓名: 安伯丽
2025-08-25 14:56:16 - INFO - 提取到年龄: 40岁
2025-08-25 14:56:16 - INFO - 提取到工作经验: 约10年人力资源及行政管理经验，无明确产品或AI相关经验
2025-08-25 14:56:16 - INFO - 提取到学历: 本科
2025-08-25 14:56:16 - INFO - 提取到当前职位: 人力资源部主管
2025-08-25 14:56:16 - INFO - 提取到期望薪资: 12-15K
2025-08-25 14:56:16 - INFO - 从初筛报告提取的基本信息: {'name': '安伯丽', 'age': '40岁', 'experience': '约10年人力资源及行政管理经验，无明确产品或AI相关经验', 'education': '本科', 'current_position': '人力资源部主管', 'expected_salary': '12-15K'}
2025-08-25 14:56:16 - INFO - 候选人 安佰丽 初筛报告提取完成: advantages=本科学历符合要求  ；具备多年人力资源经验，具备一定的组织协调与制度建设能力  , risks=**核心职责匹配度低**：无AI、产品管理、用户需求转化等相关经验  ；**潜在风险**：期望薪资略低于JD薪资下限，可能存在岗位理解偏差或定位不准确  , basic_info={'name': '安伯丽', 'age': '40岁', 'experience': '约10年人力资源及行政管理经验，无明确产品或AI相关经验', 'education': '本科', 'current_position': '人力资源部主管', 'expected_salary': '12-15K'}
2025-08-25 14:56:16 - INFO - ❌ 安佰丽 未通过第1轮筛选，得分: 0.0
2025-08-25 15:03:17 - INFO - 🚀 启动智能简历评估系统
2025-08-25 15:03:17 - INFO - 📋 系统功能:
2025-08-25 15:03:17 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 15:03:17 - INFO -   - 多线程并发处理
2025-08-25 15:03:17 - INFO -   - 实时进度监控
2025-08-25 15:03:17 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 15:03:17 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 15:03:18 - INFO - 多进程任务管理已初始化
2025-08-25 15:03:31 - INFO - 🚀 启动智能简历评估系统
2025-08-25 15:03:31 - INFO - 📋 系统功能:
2025-08-25 15:03:31 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 15:03:31 - INFO -   - 多线程并发处理
2025-08-25 15:03:31 - INFO -   - 实时进度监控
2025-08-25 15:03:31 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 15:03:31 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 15:03:32 - INFO - 多进程任务管理已初始化
2025-08-25 15:04:10 - INFO - 🚀 启动智能简历评估系统
2025-08-25 15:04:10 - INFO - 📋 系统功能:
2025-08-25 15:04:10 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 15:04:10 - INFO -   - 多线程并发处理
2025-08-25 15:04:10 - INFO -   - 实时进度监控
2025-08-25 15:04:10 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 15:04:10 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 15:04:11 - INFO - 多进程任务管理已初始化
2025-08-25 15:04:19 - INFO - 🚀 启动智能简历评估系统
2025-08-25 15:04:19 - INFO - 📋 系统功能:
2025-08-25 15:04:19 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 15:04:19 - INFO -   - 多线程并发处理
2025-08-25 15:04:19 - INFO -   - 实时进度监控
2025-08-25 15:04:19 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 15:04:19 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 15:04:20 - INFO - 多进程任务管理已初始化
2025-08-25 15:04:22 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 15:04:22 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 15:04:22 - INFO - 查找历史记录路径: D:\project\智能招聘v1.0.2\智能招聘系统 - 副本\user\杨锐雄\Result
2025-08-25 19:42:47 - INFO - 🚀 启动智能简历评估系统
2025-08-25 19:42:47 - INFO - 📋 系统功能:
2025-08-25 19:42:47 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 19:42:47 - INFO -   - 多线程并发处理
2025-08-25 19:42:47 - INFO -   - 实时进度监控
2025-08-25 19:42:47 - INFO -   - 集成Boss直聘爬虫系统
2025-08-25 19:42:47 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 19:42:47 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 19:42:47 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-08-25 19:42:47 - INFO - 多进程任务管理已初始化
2025-08-25 19:42:59 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 19:42:59 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 19:42:59 - INFO - 查找历史记录路径: D:\project\智能招聘系统_\user\杨锐雄\Result
2025-08-25 19:43:05 - INFO - 爬取任务已启动: cbc2cf36-757a-4d8b-a9d7-5bf641e3b6e5, 职位: AI产品经理
2025-08-25 19:43:05 - INFO - 🚀 开始调用爬虫程序: cbc2cf36-757a-4d8b-a9d7-5bf641e3b6e5, 职位: AI产品经理, 目标数量: 5
2025-08-25 19:43:05 - INFO - 🔍 检查岗位匹配...
2025-08-25 19:43:05 - ERROR - 岗位匹配失败: No module named 'aiohttp'
2025-08-25 19:43:05 - ERROR - 调用爬虫程序失败: 岗位匹配失败: 岗位匹配失败: No module named 'aiohttp'
2025-08-25 19:43:43 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 19:43:43 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 19:43:43 - INFO - 查找历史记录路径: D:\project\智能招聘系统_\user\杨锐雄\Result
2025-08-25 19:43:47 - INFO - 爬取任务已启动: 9b6e9bb7-9658-4dc6-ae66-d4496635a5a4, 职位: AI产品经理
2025-08-25 19:43:47 - INFO - 🚀 开始调用爬虫程序: 9b6e9bb7-9658-4dc6-ae66-d4496635a5a4, 职位: AI产品经理, 目标数量: 5
2025-08-25 19:43:47 - INFO - 🔍 检查岗位匹配...
2025-08-25 19:43:49 - ERROR - 岗位匹配失败: Cannot connect to host localhost:8000 ssl:default [远程计算机拒绝网络连接。]
2025-08-25 19:43:49 - ERROR - 调用爬虫程序失败: 岗位匹配失败: 岗位匹配失败: Cannot connect to host localhost:8000 ssl:default [远程计算机拒绝网络连接。]
2025-08-25 19:47:23 - INFO - 🚀 启动智能简历评估系统
2025-08-25 19:47:23 - INFO - 📋 系统功能:
2025-08-25 19:47:23 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 19:47:23 - INFO -   - 多线程并发处理
2025-08-25 19:47:23 - INFO -   - 实时进度监控
2025-08-25 19:47:23 - INFO -   - 集成Boss直聘爬虫系统
2025-08-25 19:47:23 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 19:47:23 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 19:47:23 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-08-25 19:47:23 - INFO - 多进程任务管理已初始化
2025-08-25 19:47:50 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 19:47:50 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 19:47:50 - INFO - 查找历史记录路径: D:\project\智能招聘系统_\user\杨锐雄\Result
2025-08-25 19:47:54 - INFO - 爬取任务已启动: 129a1411-5263-470a-b484-991099c28a26, 职位: 人力资源主管
2025-08-25 19:47:54 - INFO - 🚀 开始调用爬虫程序: 129a1411-5263-470a-b484-991099c28a26, 职位: 人力资源主管, 目标数量: 5
2025-08-25 19:47:54 - INFO - 🔍 检查岗位匹配...
2025-08-25 19:48:02 - INFO - 岗位匹配结果: {'success': True, 'matched': True, 'current_job': '人力资源主管', 'recommendation': '人力资源主管 _ 珠海 10-12K', 'match_reason': '完全匹配', 'message': '岗位匹配成功！当前推荐: 人力资源主管 _ 珠海 10-12K'}
2025-08-25 19:48:02 - INFO - ✅ 岗位匹配成功，开始爬取简历...
2025-08-25 19:48:03 - INFO - 爬取任务启动结果: {'task_id': 'task_1', 'message': '收集任务已启动'}
2025-08-25 19:48:03 - ERROR - 调用爬虫程序失败: 启动爬取失败: 收集任务已启动
2025-08-25 19:50:58 - INFO - 🚀 启动智能简历评估系统
2025-08-25 19:50:58 - INFO - 📋 系统功能:
2025-08-25 19:50:58 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 19:50:58 - INFO -   - 多线程并发处理
2025-08-25 19:50:58 - INFO -   - 实时进度监控
2025-08-25 19:50:58 - INFO -   - 集成Boss直聘爬虫系统
2025-08-25 19:50:58 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 19:50:58 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 19:50:58 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-08-25 19:50:59 - INFO - 多进程任务管理已初始化
2025-08-25 19:54:29 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 19:54:29 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 19:54:29 - INFO - 查找历史记录路径: D:\project\智能招聘系统_\user\杨锐雄\Result
2025-08-25 19:54:35 - INFO - 爬取任务已启动: 1579d255-de3b-4fcd-9377-a3530c91b739, 职位: 人力资源主管
2025-08-25 19:54:35 - INFO - 🚀 开始调用爬虫程序: 1579d255-de3b-4fcd-9377-a3530c91b739, 职位: 人力资源主管, 目标数量: 5
2025-08-25 19:54:35 - INFO - 🔍 检查岗位匹配...
2025-08-25 19:54:44 - INFO - 岗位匹配结果: {'success': True, 'matched': True, 'current_job': '人力资源主管', 'recommendation': '人力资源主管 _ 珠海 10-12K', 'match_reason': '完全匹配', 'message': '岗位匹配成功！当前推荐: 人力资源主管 _ 珠海 10-12K'}
2025-08-25 19:54:44 - INFO - ✅ 岗位匹配成功，开始爬取简历...
2025-08-25 19:54:44 - INFO - 爬取任务启动结果: {'task_id': 'task_1', 'message': '收集任务已启动'}
2025-08-25 19:54:44 - ERROR - 调用爬虫程序失败: 启动爬取失败: 收集任务已启动
2025-08-25 20:01:47 - INFO - 🚀 启动智能简历评估系统
2025-08-25 20:01:47 - INFO - 📋 系统功能:
2025-08-25 20:01:47 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 20:01:47 - INFO -   - 多线程并发处理
2025-08-25 20:01:47 - INFO -   - 实时进度监控
2025-08-25 20:01:47 - INFO -   - 集成Boss直聘爬虫系统
2025-08-25 20:01:47 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 20:01:47 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 20:01:47 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-08-25 20:01:48 - INFO - 多进程任务管理已初始化
2025-08-25 20:01:50 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 20:01:50 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 20:01:50 - INFO - 查找历史记录路径: D:\project\智能招聘系统_\user\杨锐雄\Result
2025-08-25 20:01:57 - INFO - 爬取任务已启动: 700159fb-5c26-4f8a-9510-f1cf462b8e4c, 职位: 人力资源主管
2025-08-25 20:01:57 - INFO - 🚀 开始调用爬虫程序: 700159fb-5c26-4f8a-9510-f1cf462b8e4c, 职位: 人力资源主管, 目标数量: 5
2025-08-25 20:01:57 - INFO - 🔍 检查岗位匹配...
2025-08-25 20:02:04 - INFO - 岗位匹配结果: {'success': True, 'matched': True, 'current_job': '人力资源主管', 'recommendation': '人力资源主管 _ 珠海 10-12K', 'match_reason': '完全匹配', 'message': '岗位匹配成功！当前推荐: 人力资源主管 _ 珠海 10-12K'}
2025-08-25 20:02:04 - INFO - ✅ 岗位匹配成功，开始爬取简历...
2025-08-25 20:02:05 - INFO - 爬取任务启动结果: {'task_id': 'task_1', 'message': '收集任务已启动'}
2025-08-25 20:02:05 - INFO - ✅ 爬取任务已启动: task_1
2025-08-25 20:02:05 - INFO - 开始监控爬取进度: task_1
2025-08-25 20:02:15 - ERROR - 监控爬取进度异常: 
2025-08-25 20:02:36 - ERROR - 监控爬取进度异常: 
2025-08-25 20:02:56 - ERROR - 监控爬取进度异常: 
2025-08-25 20:03:16 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 100, 'results_count': 38, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '候女士_人力资源主管_20250822_171411.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\候女士_人力资源主管_20250822_171411.png', 'size': 487307, 'created_time': 1756110341.2987957, 'candidate_name': '候女士'}, {'filename': '周女士_人力资源主管_20250825_161403.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\周女士_人力资源主管_20250825_161403.png', 'size': 262129, 'created_time': 1756110341.2997956, 'candidate_name': '周女士'}, {'filename': '周宏枫_人力资源主管_20250822_153505.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\周宏枫_人力资源主管_20250822_153505.png', 'size': 397996, 'created_time': 1756110341.3007958, 'candidate_name': '周宏枫'}, {'filename': '廖海艳_人力资源主管_20250822_160543.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\廖海艳_人力资源主管_20250822_160543.png', 'size': 330588, 'created_time': 1756110341.3017988, 'candidate_name': '廖海艳'}, {'filename': '张女士_人力资源主管_20250822_153354.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\张女士_人力资源主管_20250822_153354.png', 'size': 528934, 'created_time': 1756110341.3027964, 'candidate_name': '张女士'}, {'filename': '徐彦霖_人力资源主管_20250822_170214.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\徐彦霖_人力资源主管_20250822_170214.png', 'size': 177619, 'created_time': 1756110341.3047957, 'candidate_name': '徐彦霖'}, {'filename': '晓静_人力资源主管_20250822_160616.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\晓静_人力资源主管_20250822_160616.png', 'size': 395131, 'created_time': 1756110341.3047957, 'candidate_name': '晓静'}, {'filename': '曹女士_人力资源主管_20250822_154348.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\曹女士_人力资源主管_20250822_154348.png', 'size': 336444, 'created_time': 1756110341.3057957, 'candidate_name': '曹女士'}, {'filename': '木木_人力资源主管_20250822_154402.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\木木_人力资源主管_20250822_154402.png', 'size': 337882, 'created_time': 1756110341.3067956, 'candidate_name': '木木'}, {'filename': '李女士_人力资源主管_20250822_155150.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李女士_人力资源主管_20250822_155150.png', 'size': 343775, 'created_time': 1756110341.3077958, 'candidate_name': '李女士'}, {'filename': '李杏娜_人力资源主管_20250822_171341.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李杏娜_人力资源主管_20250822_171341.png', 'size': 216685, 'created_time': 1756110341.3087957, 'candidate_name': '李杏娜'}, {'filename': '李海燕_人力资源主管_20250825_161306.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李海燕_人力资源主管_20250825_161306.png', 'size': 302668, 'created_time': 1756110341.3097959, 'candidate_name': '李海燕'}, {'filename': '林妙华_人力资源主管_20250822_171456.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\林妙华_人力资源主管_20250822_171456.png', 'size': 271203, 'created_time': 1756110341.3107958, 'candidate_name': '林妙华'}, {'filename': '梁女士_人力资源主管_20250822_153324.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\梁女士_人力资源主管_20250822_153324.png', 'size': 397887, 'created_time': 1756110341.3117957, 'candidate_name': '梁女士'}, {'filename': '梁燕珊_人力资源主管_20250822_155136.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\梁燕珊_人力资源主管_20250822_155136.png', 'size': 214548, 'created_time': 1756110341.3127956, 'candidate_name': '梁燕珊'}, {'filename': '梅女士_人力资源主管_20250822_153410.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\梅女士_人力资源主管_20250822_153410.png', 'size': 412276, 'created_time': 1756110341.3127956, 'candidate_name': '梅女士'}, {'filename': '沈先生_人力资源主管_20250822_154420.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\沈先生_人力资源主管_20250822_154420.png', 'size': 669012, 'created_time': 1756110341.3147964, 'candidate_name': '沈先生'}, {'filename': '王相元_人力资源主管_20250822_155241.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王相元_人力资源主管_20250822_155241.png', 'size': 566359, 'created_time': 1756110341.316796, 'candidate_name': '王相元'}, {'filename': '王鸣晨_人力资源主管_20250822_153519.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王鸣晨_人力资源主管_20250822_153519.png', 'size': 309599, 'created_time': 1756110341.3177962, 'candidate_name': '王鸣晨'}, {'filename': '罗女士_人力资源主管_20250822_160600.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\罗女士_人力资源主管_20250822_160600.png', 'size': 498554, 'created_time': 1756110341.3187957, 'candidate_name': '罗女士'}, {'filename': '胡女士_人力资源主管_20250822_153337.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\胡女士_人力资源主管_20250822_153337.png', 'size': 213534, 'created_time': 1756110341.3207958, 'candidate_name': '胡女士'}, {'filename': '莫林艳_人力资源主管_20250822_155205.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\莫林艳_人力资源主管_20250822_155205.png', 'size': 383306, 'created_time': 1756110341.3207958, 'candidate_name': '莫林艳'}, {'filename': '许女士_人力资源主管_20250822_153436.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\许女士_人力资源主管_20250822_153436.png', 'size': 310957, 'created_time': 1756110341.3217957, 'candidate_name': '许女士'}, {'filename': '赵女士_人力资源主管_20250822_155223.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\赵女士_人力资源主管_20250822_155223.png', 'size': 603480, 'created_time': 1756110341.3227956, 'candidate_name': '赵女士'}, {'filename': '邦尼_人力资源主管_20250822_153449.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\邦尼_人力资源主管_20250822_153449.png', 'size': 247364, 'created_time': 1756110341.3247964, 'candidate_name': '邦尼'}, {'filename': '陈品乐_人力资源主管_20250822_170519.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\陈品乐_人力资源主管_20250822_170519.png', 'size': 754064, 'created_time': 1756110341.325796, 'candidate_name': '陈品乐'}, {'filename': '陈雅斯_人力资源主管_20250822_171425.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\陈雅斯_人力资源主管_20250822_171425.png', 'size': 281940, 'created_time': 1756110341.3277957, 'candidate_name': '陈雅斯'}, {'filename': '马慧杰_人力资源主管_20250822_153532.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\马慧杰_人力资源主管_20250822_153532.png', 'size': 168456, 'created_time': 1756110341.328797, 'candidate_name': '马慧杰'}, {'filename': '高晓扬_人力资源主管_20250822_155510.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\高晓扬_人力资源主管_20250822_155510.png', 'size': 300302, 'created_time': 1756110341.3297956, 'candidate_name': '高晓扬'}, {'filename': '黄小姐_人力资源主管_20250822_153422.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黄小姐_人力资源主管_20250822_153422.png', 'size': 182251, 'created_time': 1756110341.3297956, 'candidate_name': '黄小姐'}, {'filename': '黄衍丹_人力资源主管_20250822_171355.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黄衍丹_人力资源主管_20250822_171355.png', 'size': 291321, 'created_time': 1756110341.3307958, 'candidate_name': '黄衍丹'}, {'filename': '黄雅琪_人力资源主管_20250822_171442.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黄雅琪_人力资源主管_20250822_171442.png', 'size': 580818, 'created_time': 1756110341.3317952, 'candidate_name': '黄雅琪'}, {'filename': '孙女士_人力资源主管_20250825_165144.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\孙女士_人力资源主管_20250825_165144.png', 'size': 215993, 'created_time': 1756111904.2625697, 'candidate_name': '孙女士'}, {'filename': '吴女士_人力资源主管_20250825_165235.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\吴女士_人力资源主管_20250825_165235.png', 'size': 364932, 'created_time': 1756111955.4221387, 'candidate_name': '吴女士'}, {'filename': '李冰卉_人力资源主管_20250825_165423.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李冰卉_人力资源主管_20250825_165423.png', 'size': 156605, 'created_time': 1756112063.1731703, 'candidate_name': '李冰卉'}, {'filename': '黎先生_人力资源主管_20250825_165514.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黎先生_人力资源主管_20250825_165514.png', 'size': 178267, 'created_time': 1756112114.0681944, 'candidate_name': '黎先生'}, {'filename': '黎先生_人力资源主管_20250825_195507.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黎先生_人力资源主管_20250825_195507.png', 'size': 174405, 'created_time': 1756122907.2201126, 'candidate_name': '黎先生'}, {'filename': '李潭_人力资源主管_20250825_200252.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李潭_人力资源主管_20250825_200252.png', 'size': 64835, 'created_time': 1756123372.9513416, 'candidate_name': '李潭'}]}
2025-08-25 20:03:28 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 100, 'results_count': 38, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '候女士_人力资源主管_20250822_171411.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\候女士_人力资源主管_20250822_171411.png', 'size': 487307, 'created_time': 1756110341.2987957, 'candidate_name': '候女士'}, {'filename': '周女士_人力资源主管_20250825_161403.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\周女士_人力资源主管_20250825_161403.png', 'size': 262129, 'created_time': 1756110341.2997956, 'candidate_name': '周女士'}, {'filename': '周宏枫_人力资源主管_20250822_153505.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\周宏枫_人力资源主管_20250822_153505.png', 'size': 397996, 'created_time': 1756110341.3007958, 'candidate_name': '周宏枫'}, {'filename': '廖海艳_人力资源主管_20250822_160543.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\廖海艳_人力资源主管_20250822_160543.png', 'size': 330588, 'created_time': 1756110341.3017988, 'candidate_name': '廖海艳'}, {'filename': '张女士_人力资源主管_20250822_153354.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\张女士_人力资源主管_20250822_153354.png', 'size': 528934, 'created_time': 1756110341.3027964, 'candidate_name': '张女士'}, {'filename': '徐彦霖_人力资源主管_20250822_170214.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\徐彦霖_人力资源主管_20250822_170214.png', 'size': 177619, 'created_time': 1756110341.3047957, 'candidate_name': '徐彦霖'}, {'filename': '晓静_人力资源主管_20250822_160616.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\晓静_人力资源主管_20250822_160616.png', 'size': 395131, 'created_time': 1756110341.3047957, 'candidate_name': '晓静'}, {'filename': '曹女士_人力资源主管_20250822_154348.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\曹女士_人力资源主管_20250822_154348.png', 'size': 336444, 'created_time': 1756110341.3057957, 'candidate_name': '曹女士'}, {'filename': '木木_人力资源主管_20250822_154402.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\木木_人力资源主管_20250822_154402.png', 'size': 337882, 'created_time': 1756110341.3067956, 'candidate_name': '木木'}, {'filename': '李女士_人力资源主管_20250822_155150.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李女士_人力资源主管_20250822_155150.png', 'size': 343775, 'created_time': 1756110341.3077958, 'candidate_name': '李女士'}, {'filename': '李杏娜_人力资源主管_20250822_171341.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李杏娜_人力资源主管_20250822_171341.png', 'size': 216685, 'created_time': 1756110341.3087957, 'candidate_name': '李杏娜'}, {'filename': '李海燕_人力资源主管_20250825_161306.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李海燕_人力资源主管_20250825_161306.png', 'size': 302668, 'created_time': 1756110341.3097959, 'candidate_name': '李海燕'}, {'filename': '林妙华_人力资源主管_20250822_171456.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\林妙华_人力资源主管_20250822_171456.png', 'size': 271203, 'created_time': 1756110341.3107958, 'candidate_name': '林妙华'}, {'filename': '梁女士_人力资源主管_20250822_153324.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\梁女士_人力资源主管_20250822_153324.png', 'size': 397887, 'created_time': 1756110341.3117957, 'candidate_name': '梁女士'}, {'filename': '梁燕珊_人力资源主管_20250822_155136.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\梁燕珊_人力资源主管_20250822_155136.png', 'size': 214548, 'created_time': 1756110341.3127956, 'candidate_name': '梁燕珊'}, {'filename': '梅女士_人力资源主管_20250822_153410.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\梅女士_人力资源主管_20250822_153410.png', 'size': 412276, 'created_time': 1756110341.3127956, 'candidate_name': '梅女士'}, {'filename': '沈先生_人力资源主管_20250822_154420.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\沈先生_人力资源主管_20250822_154420.png', 'size': 669012, 'created_time': 1756110341.3147964, 'candidate_name': '沈先生'}, {'filename': '王相元_人力资源主管_20250822_155241.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王相元_人力资源主管_20250822_155241.png', 'size': 566359, 'created_time': 1756110341.316796, 'candidate_name': '王相元'}, {'filename': '王鸣晨_人力资源主管_20250822_153519.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王鸣晨_人力资源主管_20250822_153519.png', 'size': 309599, 'created_time': 1756110341.3177962, 'candidate_name': '王鸣晨'}, {'filename': '罗女士_人力资源主管_20250822_160600.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\罗女士_人力资源主管_20250822_160600.png', 'size': 498554, 'created_time': 1756110341.3187957, 'candidate_name': '罗女士'}, {'filename': '胡女士_人力资源主管_20250822_153337.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\胡女士_人力资源主管_20250822_153337.png', 'size': 213534, 'created_time': 1756110341.3207958, 'candidate_name': '胡女士'}, {'filename': '莫林艳_人力资源主管_20250822_155205.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\莫林艳_人力资源主管_20250822_155205.png', 'size': 383306, 'created_time': 1756110341.3207958, 'candidate_name': '莫林艳'}, {'filename': '许女士_人力资源主管_20250822_153436.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\许女士_人力资源主管_20250822_153436.png', 'size': 310957, 'created_time': 1756110341.3217957, 'candidate_name': '许女士'}, {'filename': '赵女士_人力资源主管_20250822_155223.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\赵女士_人力资源主管_20250822_155223.png', 'size': 603480, 'created_time': 1756110341.3227956, 'candidate_name': '赵女士'}, {'filename': '邦尼_人力资源主管_20250822_153449.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\邦尼_人力资源主管_20250822_153449.png', 'size': 247364, 'created_time': 1756110341.3247964, 'candidate_name': '邦尼'}, {'filename': '陈品乐_人力资源主管_20250822_170519.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\陈品乐_人力资源主管_20250822_170519.png', 'size': 754064, 'created_time': 1756110341.325796, 'candidate_name': '陈品乐'}, {'filename': '陈雅斯_人力资源主管_20250822_171425.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\陈雅斯_人力资源主管_20250822_171425.png', 'size': 281940, 'created_time': 1756110341.3277957, 'candidate_name': '陈雅斯'}, {'filename': '马慧杰_人力资源主管_20250822_153532.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\马慧杰_人力资源主管_20250822_153532.png', 'size': 168456, 'created_time': 1756110341.328797, 'candidate_name': '马慧杰'}, {'filename': '高晓扬_人力资源主管_20250822_155510.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\高晓扬_人力资源主管_20250822_155510.png', 'size': 300302, 'created_time': 1756110341.3297956, 'candidate_name': '高晓扬'}, {'filename': '黄小姐_人力资源主管_20250822_153422.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黄小姐_人力资源主管_20250822_153422.png', 'size': 182251, 'created_time': 1756110341.3297956, 'candidate_name': '黄小姐'}, {'filename': '黄衍丹_人力资源主管_20250822_171355.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黄衍丹_人力资源主管_20250822_171355.png', 'size': 291321, 'created_time': 1756110341.3307958, 'candidate_name': '黄衍丹'}, {'filename': '黄雅琪_人力资源主管_20250822_171442.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黄雅琪_人力资源主管_20250822_171442.png', 'size': 580818, 'created_time': 1756110341.3317952, 'candidate_name': '黄雅琪'}, {'filename': '孙女士_人力资源主管_20250825_165144.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\孙女士_人力资源主管_20250825_165144.png', 'size': 215993, 'created_time': 1756111904.2625697, 'candidate_name': '孙女士'}, {'filename': '吴女士_人力资源主管_20250825_165235.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\吴女士_人力资源主管_20250825_165235.png', 'size': 364932, 'created_time': 1756111955.4221387, 'candidate_name': '吴女士'}, {'filename': '李冰卉_人力资源主管_20250825_165423.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李冰卉_人力资源主管_20250825_165423.png', 'size': 156605, 'created_time': 1756112063.1731703, 'candidate_name': '李冰卉'}, {'filename': '黎先生_人力资源主管_20250825_165514.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黎先生_人力资源主管_20250825_165514.png', 'size': 178267, 'created_time': 1756112114.0681944, 'candidate_name': '黎先生'}, {'filename': '黎先生_人力资源主管_20250825_195507.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黎先生_人力资源主管_20250825_195507.png', 'size': 174405, 'created_time': 1756122907.2201126, 'candidate_name': '黎先生'}, {'filename': '李潭_人力资源主管_20250825_200252.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李潭_人力资源主管_20250825_200252.png', 'size': 64835, 'created_time': 1756123372.9513416, 'candidate_name': '李潭'}]}
2025-08-25 20:03:40 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 100, 'results_count': 38, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '候女士_人力资源主管_20250822_171411.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\候女士_人力资源主管_20250822_171411.png', 'size': 487307, 'created_time': 1756110341.2987957, 'candidate_name': '候女士'}, {'filename': '周女士_人力资源主管_20250825_161403.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\周女士_人力资源主管_20250825_161403.png', 'size': 262129, 'created_time': 1756110341.2997956, 'candidate_name': '周女士'}, {'filename': '周宏枫_人力资源主管_20250822_153505.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\周宏枫_人力资源主管_20250822_153505.png', 'size': 397996, 'created_time': 1756110341.3007958, 'candidate_name': '周宏枫'}, {'filename': '廖海艳_人力资源主管_20250822_160543.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\廖海艳_人力资源主管_20250822_160543.png', 'size': 330588, 'created_time': 1756110341.3017988, 'candidate_name': '廖海艳'}, {'filename': '张女士_人力资源主管_20250822_153354.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\张女士_人力资源主管_20250822_153354.png', 'size': 528934, 'created_time': 1756110341.3027964, 'candidate_name': '张女士'}, {'filename': '徐彦霖_人力资源主管_20250822_170214.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\徐彦霖_人力资源主管_20250822_170214.png', 'size': 177619, 'created_time': 1756110341.3047957, 'candidate_name': '徐彦霖'}, {'filename': '晓静_人力资源主管_20250822_160616.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\晓静_人力资源主管_20250822_160616.png', 'size': 395131, 'created_time': 1756110341.3047957, 'candidate_name': '晓静'}, {'filename': '曹女士_人力资源主管_20250822_154348.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\曹女士_人力资源主管_20250822_154348.png', 'size': 336444, 'created_time': 1756110341.3057957, 'candidate_name': '曹女士'}, {'filename': '木木_人力资源主管_20250822_154402.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\木木_人力资源主管_20250822_154402.png', 'size': 337882, 'created_time': 1756110341.3067956, 'candidate_name': '木木'}, {'filename': '李女士_人力资源主管_20250822_155150.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李女士_人力资源主管_20250822_155150.png', 'size': 343775, 'created_time': 1756110341.3077958, 'candidate_name': '李女士'}, {'filename': '李杏娜_人力资源主管_20250822_171341.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李杏娜_人力资源主管_20250822_171341.png', 'size': 216685, 'created_time': 1756110341.3087957, 'candidate_name': '李杏娜'}, {'filename': '李海燕_人力资源主管_20250825_161306.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李海燕_人力资源主管_20250825_161306.png', 'size': 302668, 'created_time': 1756110341.3097959, 'candidate_name': '李海燕'}, {'filename': '林妙华_人力资源主管_20250822_171456.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\林妙华_人力资源主管_20250822_171456.png', 'size': 271203, 'created_time': 1756110341.3107958, 'candidate_name': '林妙华'}, {'filename': '梁女士_人力资源主管_20250822_153324.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\梁女士_人力资源主管_20250822_153324.png', 'size': 397887, 'created_time': 1756110341.3117957, 'candidate_name': '梁女士'}, {'filename': '梁燕珊_人力资源主管_20250822_155136.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\梁燕珊_人力资源主管_20250822_155136.png', 'size': 214548, 'created_time': 1756110341.3127956, 'candidate_name': '梁燕珊'}, {'filename': '梅女士_人力资源主管_20250822_153410.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\梅女士_人力资源主管_20250822_153410.png', 'size': 412276, 'created_time': 1756110341.3127956, 'candidate_name': '梅女士'}, {'filename': '沈先生_人力资源主管_20250822_154420.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\沈先生_人力资源主管_20250822_154420.png', 'size': 669012, 'created_time': 1756110341.3147964, 'candidate_name': '沈先生'}, {'filename': '王相元_人力资源主管_20250822_155241.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王相元_人力资源主管_20250822_155241.png', 'size': 566359, 'created_time': 1756110341.316796, 'candidate_name': '王相元'}, {'filename': '王鸣晨_人力资源主管_20250822_153519.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王鸣晨_人力资源主管_20250822_153519.png', 'size': 309599, 'created_time': 1756110341.3177962, 'candidate_name': '王鸣晨'}, {'filename': '罗女士_人力资源主管_20250822_160600.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\罗女士_人力资源主管_20250822_160600.png', 'size': 498554, 'created_time': 1756110341.3187957, 'candidate_name': '罗女士'}, {'filename': '胡女士_人力资源主管_20250822_153337.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\胡女士_人力资源主管_20250822_153337.png', 'size': 213534, 'created_time': 1756110341.3207958, 'candidate_name': '胡女士'}, {'filename': '莫林艳_人力资源主管_20250822_155205.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\莫林艳_人力资源主管_20250822_155205.png', 'size': 383306, 'created_time': 1756110341.3207958, 'candidate_name': '莫林艳'}, {'filename': '许女士_人力资源主管_20250822_153436.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\许女士_人力资源主管_20250822_153436.png', 'size': 310957, 'created_time': 1756110341.3217957, 'candidate_name': '许女士'}, {'filename': '赵女士_人力资源主管_20250822_155223.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\赵女士_人力资源主管_20250822_155223.png', 'size': 603480, 'created_time': 1756110341.3227956, 'candidate_name': '赵女士'}, {'filename': '邦尼_人力资源主管_20250822_153449.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\邦尼_人力资源主管_20250822_153449.png', 'size': 247364, 'created_time': 1756110341.3247964, 'candidate_name': '邦尼'}, {'filename': '陈品乐_人力资源主管_20250822_170519.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\陈品乐_人力资源主管_20250822_170519.png', 'size': 754064, 'created_time': 1756110341.325796, 'candidate_name': '陈品乐'}, {'filename': '陈雅斯_人力资源主管_20250822_171425.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\陈雅斯_人力资源主管_20250822_171425.png', 'size': 281940, 'created_time': 1756110341.3277957, 'candidate_name': '陈雅斯'}, {'filename': '马慧杰_人力资源主管_20250822_153532.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\马慧杰_人力资源主管_20250822_153532.png', 'size': 168456, 'created_time': 1756110341.328797, 'candidate_name': '马慧杰'}, {'filename': '高晓扬_人力资源主管_20250822_155510.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\高晓扬_人力资源主管_20250822_155510.png', 'size': 300302, 'created_time': 1756110341.3297956, 'candidate_name': '高晓扬'}, {'filename': '黄小姐_人力资源主管_20250822_153422.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黄小姐_人力资源主管_20250822_153422.png', 'size': 182251, 'created_time': 1756110341.3297956, 'candidate_name': '黄小姐'}, {'filename': '黄衍丹_人力资源主管_20250822_171355.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黄衍丹_人力资源主管_20250822_171355.png', 'size': 291321, 'created_time': 1756110341.3307958, 'candidate_name': '黄衍丹'}, {'filename': '黄雅琪_人力资源主管_20250822_171442.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黄雅琪_人力资源主管_20250822_171442.png', 'size': 580818, 'created_time': 1756110341.3317952, 'candidate_name': '黄雅琪'}, {'filename': '孙女士_人力资源主管_20250825_165144.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\孙女士_人力资源主管_20250825_165144.png', 'size': 215993, 'created_time': 1756111904.2625697, 'candidate_name': '孙女士'}, {'filename': '吴女士_人力资源主管_20250825_165235.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\吴女士_人力资源主管_20250825_165235.png', 'size': 364932, 'created_time': 1756111955.4221387, 'candidate_name': '吴女士'}, {'filename': '李冰卉_人力资源主管_20250825_165423.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李冰卉_人力资源主管_20250825_165423.png', 'size': 156605, 'created_time': 1756112063.1731703, 'candidate_name': '李冰卉'}, {'filename': '黎先生_人力资源主管_20250825_165514.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黎先生_人力资源主管_20250825_165514.png', 'size': 178267, 'created_time': 1756112114.0681944, 'candidate_name': '黎先生'}, {'filename': '黎先生_人力资源主管_20250825_195507.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黎先生_人力资源主管_20250825_195507.png', 'size': 174405, 'created_time': 1756122907.2201126, 'candidate_name': '黎先生'}, {'filename': '李潭_人力资源主管_20250825_200252.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李潭_人力资源主管_20250825_200252.png', 'size': 64835, 'created_time': 1756123372.9513416, 'candidate_name': '李潭'}]}
2025-08-25 20:03:55 - ERROR - 监控爬取进度异常: 
2025-08-25 20:04:37 - INFO - 🚀 启动智能简历评估系统
2025-08-25 20:04:37 - INFO - 📋 系统功能:
2025-08-25 20:04:37 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 20:04:37 - INFO -   - 多线程并发处理
2025-08-25 20:04:37 - INFO -   - 实时进度监控
2025-08-25 20:04:37 - INFO -   - 集成Boss直聘爬虫系统
2025-08-25 20:04:37 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 20:04:37 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 20:04:37 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-08-25 20:04:38 - INFO - 多进程任务管理已初始化
2025-08-25 20:04:43 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 20:04:43 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 20:04:43 - INFO - 查找历史记录路径: D:\project\智能招聘系统_\user\杨锐雄\Result
2025-08-25 20:04:46 - INFO - 爬取任务已启动: 03f466bb-ea6d-44bb-91f3-3e8a967c07bd, 职位: 人力资源主管
2025-08-25 20:04:46 - INFO - 🚀 开始调用爬虫程序: 03f466bb-ea6d-44bb-91f3-3e8a967c07bd, 职位: 人力资源主管, 目标数量: 5
2025-08-25 20:04:46 - INFO - 🔍 检查岗位匹配...
2025-08-25 20:04:54 - INFO - 岗位匹配结果: {'success': True, 'matched': True, 'current_job': '人力资源主管', 'recommendation': '人力资源主管 _ 珠海 10-12K', 'match_reason': '完全匹配', 'message': '岗位匹配成功！当前推荐: 人力资源主管 _ 珠海 10-12K'}
2025-08-25 20:04:54 - INFO - ✅ 岗位匹配成功，开始爬取简历...
2025-08-25 20:04:54 - INFO - 爬取任务启动结果: {'task_id': 'task_1', 'message': '收集任务已启动'}
2025-08-25 20:04:54 - INFO - ✅ 爬取任务已启动: task_1
2025-08-25 20:04:54 - INFO - 开始监控爬取进度: task_1
2025-08-25 20:05:05 - ERROR - 监控爬取进度异常: 
2025-08-25 20:05:26 - ERROR - 监控爬取进度异常: 
2025-08-25 20:05:36 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 20.0, 'results_count': 1, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '李潭_人力资源主管_20250825_200512.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李潭_人力资源主管_20250825_200512.png', 'size': 64462, 'created_time': 1756123512.6452193, 'candidate_name': '李潭'}]}
2025-08-25 20:05:47 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 20.0, 'results_count': 1, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '李潭_人力资源主管_20250825_200512.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李潭_人力资源主管_20250825_200512.png', 'size': 64462, 'created_time': 1756123512.6452193, 'candidate_name': '李潭'}]}
2025-08-25 20:05:59 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 20.0, 'results_count': 1, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '李潭_人力资源主管_20250825_200512.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李潭_人力资源主管_20250825_200512.png', 'size': 64462, 'created_time': 1756123512.6452193, 'candidate_name': '李潭'}]}
2025-08-25 20:06:15 - ERROR - 监控爬取进度异常: 
2025-08-25 20:06:35 - ERROR - 监控爬取进度异常: 
2025-08-25 20:06:48 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 40.0, 'results_count': 2, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '李潭_人力资源主管_20250825_200512.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李潭_人力资源主管_20250825_200512.png', 'size': 64462, 'created_time': 1756123512.6452193, 'candidate_name': '李潭'}, {'filename': '黄女士_人力资源主管_20250825_200612.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黄女士_人力资源主管_20250825_200612.png', 'size': 195558, 'created_time': 1756123572.7359817, 'candidate_name': '黄女士'}]}
2025-08-25 20:07:00 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 40.0, 'results_count': 2, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '李潭_人力资源主管_20250825_200512.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李潭_人力资源主管_20250825_200512.png', 'size': 64462, 'created_time': 1756123512.6452193, 'candidate_name': '李潭'}, {'filename': '黄女士_人力资源主管_20250825_200612.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黄女士_人力资源主管_20250825_200612.png', 'size': 195558, 'created_time': 1756123572.7359817, 'candidate_name': '黄女士'}]}
2025-08-25 20:07:15 - ERROR - 监控爬取进度异常: 
2025-08-25 20:07:34 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 3, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '李潭_人力资源主管_20250825_200512.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李潭_人力资源主管_20250825_200512.png', 'size': 64462, 'created_time': 1756123512.6452193, 'candidate_name': '李潭'}, {'filename': '黄女士_人力资源主管_20250825_200612.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黄女士_人力资源主管_20250825_200612.png', 'size': 195558, 'created_time': 1756123572.7359817, 'candidate_name': '黄女士'}, {'filename': '王少萍_人力资源主管_20250825_200711.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王少萍_人力资源主管_20250825_200711.png', 'size': 160214, 'created_time': 1756123631.0580726, 'candidate_name': '王少萍'}]}
2025-08-25 20:07:46 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 3, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '李潭_人力资源主管_20250825_200512.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李潭_人力资源主管_20250825_200512.png', 'size': 64462, 'created_time': 1756123512.6452193, 'candidate_name': '李潭'}, {'filename': '黄女士_人力资源主管_20250825_200612.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黄女士_人力资源主管_20250825_200612.png', 'size': 195558, 'created_time': 1756123572.7359817, 'candidate_name': '黄女士'}, {'filename': '王少萍_人力资源主管_20250825_200711.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王少萍_人力资源主管_20250825_200711.png', 'size': 160214, 'created_time': 1756123631.0580726, 'candidate_name': '王少萍'}]}
2025-08-25 20:07:58 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 3, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '李潭_人力资源主管_20250825_200512.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李潭_人力资源主管_20250825_200512.png', 'size': 64462, 'created_time': 1756123512.6452193, 'candidate_name': '李潭'}, {'filename': '黄女士_人力资源主管_20250825_200612.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黄女士_人力资源主管_20250825_200612.png', 'size': 195558, 'created_time': 1756123572.7359817, 'candidate_name': '黄女士'}, {'filename': '王少萍_人力资源主管_20250825_200711.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王少萍_人力资源主管_20250825_200711.png', 'size': 160214, 'created_time': 1756123631.0580726, 'candidate_name': '王少萍'}]}
2025-08-25 20:08:13 - ERROR - 监控爬取进度异常: 
2025-08-25 20:08:33 - ERROR - 监控爬取进度异常: 
2025-08-25 20:08:45 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 80.0, 'results_count': 4, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '李潭_人力资源主管_20250825_200512.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李潭_人力资源主管_20250825_200512.png', 'size': 64462, 'created_time': 1756123512.6452193, 'candidate_name': '李潭'}, {'filename': '黄女士_人力资源主管_20250825_200612.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黄女士_人力资源主管_20250825_200612.png', 'size': 195558, 'created_time': 1756123572.7359817, 'candidate_name': '黄女士'}, {'filename': '王少萍_人力资源主管_20250825_200711.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王少萍_人力资源主管_20250825_200711.png', 'size': 160214, 'created_time': 1756123631.0580726, 'candidate_name': '王少萍'}, {'filename': '王淇_人力资源主管_20250825_200810.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王淇_人力资源主管_20250825_200810.png', 'size': 183223, 'created_time': 1756123690.6784935, 'candidate_name': '王淇'}]}
2025-08-25 20:08:57 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 80.0, 'results_count': 4, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '李潭_人力资源主管_20250825_200512.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李潭_人力资源主管_20250825_200512.png', 'size': 64462, 'created_time': 1756123512.6452193, 'candidate_name': '李潭'}, {'filename': '黄女士_人力资源主管_20250825_200612.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黄女士_人力资源主管_20250825_200612.png', 'size': 195558, 'created_time': 1756123572.7359817, 'candidate_name': '黄女士'}, {'filename': '王少萍_人力资源主管_20250825_200711.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王少萍_人力资源主管_20250825_200711.png', 'size': 160214, 'created_time': 1756123631.0580726, 'candidate_name': '王少萍'}, {'filename': '王淇_人力资源主管_20250825_200810.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王淇_人力资源主管_20250825_200810.png', 'size': 183223, 'created_time': 1756123690.6784935, 'candidate_name': '王淇'}]}
2025-08-25 20:09:13 - ERROR - 监控爬取进度异常: 
2025-08-25 20:09:23 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'completed', 'progress': 100, 'results_count': 5, 'target_count': 5, 'current_action': '收集完成', 'current_resumes': [{'filename': '李潭_人力资源主管_20250825_200512.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李潭_人力资源主管_20250825_200512.png', 'size': 64462, 'created_time': 1756123512.6452193, 'candidate_name': '李潭'}, {'filename': '黄女士_人力资源主管_20250825_200612.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黄女士_人力资源主管_20250825_200612.png', 'size': 195558, 'created_time': 1756123572.7359817, 'candidate_name': '黄女士'}, {'filename': '王少萍_人力资源主管_20250825_200711.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王少萍_人力资源主管_20250825_200711.png', 'size': 160214, 'created_time': 1756123631.0580726, 'candidate_name': '王少萍'}, {'filename': '王淇_人力资源主管_20250825_200810.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王淇_人力资源主管_20250825_200810.png', 'size': 183223, 'created_time': 1756123690.6784935, 'candidate_name': '王淇'}, {'filename': '戈女士_人力资源主管_20250825_200908.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\戈女士_人力资源主管_20250825_200908.png', 'size': 132174, 'created_time': 1756123748.4267347, 'candidate_name': '戈女士'}]}
2025-08-25 20:09:23 - INFO - 爬取完成，获取简历列表...
2025-08-25 20:09:23 - INFO - 获取到 5 份简历
2025-08-25 20:09:23 - INFO - 爬取任务完成: 03f466bb-ea6d-44bb-91f3-3e8a967c07bd, 共获取 5 份简历
2025-08-25 20:09:27 - INFO - 开始转换MD格式JD: 人力资源主管.md
2025-08-25 20:09:27 - INFO - ✅ 发现已存在的JSON文件: ./user/未知用户/jd\人力资源主管.json
2025-08-25 20:09:27 - INFO - ✅ JSON文件格式验证通过
2025-08-25 20:09:27 - INFO - 开始转换MD格式JD: 人力资源主管.md
2025-08-25 20:09:27 - INFO - ✅ 发现已存在的JSON文件: ./user/杨锐雄/jd\人力资源主管.json
2025-08-25 20:09:27 - INFO - ✅ JSON文件格式验证通过
2025-08-25 20:09:27 - INFO - ✅ 保存新候选人简历到VL目录: ./文本简历/VL_Result_人力资源主管\戈女士_VL识别结果.txt
2025-08-25 20:09:27 - INFO - 开始处理简历: 戈女士
2025-08-25 20:09:27 - INFO - 开始第1轮评估: 戈女士
2025-08-25 20:09:27 - INFO - 岗位名称: 人力资源主管
2025-08-25 20:09:27 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 20:09:27 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 20:09:49 - INFO - ✅ 第1轮评估完成: 戈女士 | 分数: 0.0
2025-08-25 20:09:49 - INFO - 检测到不合格关键词: 不匹配
2025-08-25 20:09:49 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_20：09_人力资源主管_一二点五三轮筛选结果/初筛不合格\未通过_戈女士_第1轮_评估报告.md
2025-08-25 20:09:49 - INFO - 开始提取候选人 戈女士 的初筛报告信息
2025-08-25 20:09:49 - INFO - 找到优势文本: **优势:**
    - 学历符合要求，专业对口。
    - 具备HR基础工作能力（招聘、培训、绩效管理、员工关系）。
    - 拥有相关证书，具备一定专业素养。

- 
2025-08-25 20:09:49 - INFO - 找到风险文本: **风险与不足:**
    - 期望薪资远高于JD薪资范围，超出1.2倍，属于**硬性门槛不匹配项**。
    - 缺乏统筹整体人力资源管理、员工生命周期管理及薪酬体系设计方面的经验描述。
    - 工作年限仅为2年，可能在组织变革推动、复杂人事问题处理等方面经验不足。

**4. 初筛结论**

- **淘汰条件：**
    1. ✅ 硬性门槛审查 中有任意一项结果为 "**不匹配**"。（期望薪资不匹配）
    2. ❌ 核心职责匹配度 评估结果为 "**低**"。（实际为“中”）

- **通过条件：**
    1. ❌ 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. ✅ 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人期望薪资远高于JD薪资上限，超出20%容忍范围，属于硬性门槛“不匹配”项。虽然其在基础HR工作方面有一定经验，但因薪资不匹配，直接淘汰。
2025-08-25 20:09:49 - INFO - 提取的优势和风险: 优势=学历符合要求，专业对口。；具备HR基础工作能力（招聘、培训、绩效管理、员工关系）。；拥有相关证书，具备一定专业素养。, 风险=期望薪资远高于JD薪资范围，超出1.2倍，属于**硬性门槛不匹配项**。；缺乏统筹整体人力资源管理、员工生命周期管理及薪酬体系设计方面的经验描述。
2025-08-25 20:09:49 - INFO - 提取到姓名: 戈女士
2025-08-25 20:09:49 - INFO - 提取到年龄: 25岁
2025-08-25 20:09:49 - INFO - 提取到工作经验: 2年
2025-08-25 20:09:49 - INFO - 提取到学历: 本科
2025-08-25 20:09:49 - INFO - 提取到当前职位: HR专员
2025-08-25 20:09:49 - INFO - 提取到期望薪资: 15K-20K
2025-08-25 20:09:49 - INFO - 从初筛报告提取的基本信息: {'name': '戈女士', 'age': '25岁', 'experience': '2年', 'education': '本科', 'current_position': 'HR专员', 'expected_salary': '15K-20K'}
2025-08-25 20:09:49 - INFO - 候选人 戈女士 初筛报告提取完成: advantages=学历符合要求，专业对口。；具备HR基础工作能力（招聘、培训、绩效管理、员工关系）。；拥有相关证书，具备一定专业素养。, risks=期望薪资远高于JD薪资范围，超出1.2倍，属于**硬性门槛不匹配项**。；缺乏统筹整体人力资源管理、员工生命周期管理及薪酬体系设计方面的经验描述。, basic_info={'name': '戈女士', 'age': '25岁', 'experience': '2年', 'education': '本科', 'current_position': 'HR专员', 'expected_salary': '15K-20K'}
2025-08-25 20:09:49 - INFO - ❌ 戈女士 未通过第1轮筛选，得分: 0.0
2025-08-25 20:09:49 - INFO - ✅ 保存新候选人简历到VL目录: ./文本简历/VL_Result_人力资源主管\王淇_VL识别结果.txt
2025-08-25 20:09:49 - INFO - 开始处理简历: 王淇
2025-08-25 20:09:49 - INFO - 开始第1轮评估: 王淇
2025-08-25 20:09:49 - INFO - 岗位名称: 人力资源主管
2025-08-25 20:09:49 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 20:09:49 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 20:10:09 - INFO - ✅ 第1轮评估完成: 王淇 | 分数: 0.0
2025-08-25 20:10:09 - INFO - 检测到不合格关键词: 不匹配
2025-08-25 20:10:09 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_20：09_人力资源主管_一二点五三轮筛选结果/初筛不合格\未通过_王淇_第1轮_评估报告.md
2025-08-25 20:10:09 - INFO - 开始提取候选人 王淇 的初筛报告信息
2025-08-25 20:10:09 - INFO - 找到优势文本: **优势:**  
    - 学历符合要求，具备人力资源管理专业背景  
    - 拥有招聘、培训、绩效、员工关系等基础模块经验

- 
2025-08-25 20:10:09 - INFO - 找到风险文本: **风险与不足:**  
    - 期望薪资远高于JD薪资上限，存在重大不匹配项  
    - 工作经验为HR专员，缺乏人力资源主管所需的统筹与战略层面经验  
    - 无薪酬福利体系设计经验，与JD第三条核心职责不匹配  
    - 未体现出员工生命周期管理的系统性经验

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人期望薪资远高于JD薪资上限（>7K × 1.2），构成“不匹配”项；同时在核心职责评估中，候选人缺乏统筹管理经验、员工生命周期管理能力和薪酬体系设计经验，匹配度为“低”。因此，不符合通过条件。
2025-08-25 20:10:09 - INFO - 提取的优势和风险: 优势=学历符合要求，具备人力资源管理专业背景  ；拥有招聘、培训、绩效、员工关系等基础模块经验, 风险=同时在核心职责评估中，候选人缺乏统筹管理经验、员工生命周期管理能力和薪酬体系设计经验，匹配度为“低”。因此，不符合通过条件。
2025-08-25 20:10:09 - INFO - 提取到姓名: 王淇
2025-08-25 20:10:09 - INFO - 提取到年龄: 25岁
2025-08-25 20:10:09 - INFO - 提取到工作经验: 2年
2025-08-25 20:10:09 - INFO - 提取到学历: 本科
2025-08-25 20:10:09 - INFO - 提取到当前职位: HR专员（2020-2023）
2025-08-25 20:10:09 - INFO - 提取到期望薪资: 15K-20K
2025-08-25 20:10:09 - INFO - 从初筛报告提取的基本信息: {'name': '王淇', 'age': '25岁', 'experience': '2年', 'education': '本科', 'current_position': 'HR专员（2020-2023）', 'expected_salary': '15K-20K'}
2025-08-25 20:10:09 - INFO - 候选人 王淇 初筛报告提取完成: advantages=学历符合要求，具备人力资源管理专业背景  ；拥有招聘、培训、绩效、员工关系等基础模块经验, risks=同时在核心职责评估中，候选人缺乏统筹管理经验、员工生命周期管理能力和薪酬体系设计经验，匹配度为“低”。因此，不符合通过条件。, basic_info={'name': '王淇', 'age': '25岁', 'experience': '2年', 'education': '本科', 'current_position': 'HR专员（2020-2023）', 'expected_salary': '15K-20K'}
2025-08-25 20:10:09 - INFO - ❌ 王淇 未通过第1轮筛选，得分: 0.0
2025-08-25 20:10:09 - INFO - ✅ 保存新候选人简历到VL目录: ./文本简历/VL_Result_人力资源主管\王少萍_VL识别结果.txt
2025-08-25 20:10:09 - INFO - 开始处理简历: 王少萍
2025-08-25 20:10:09 - INFO - 开始第1轮评估: 王少萍
2025-08-25 20:10:09 - INFO - 岗位名称: 人力资源主管
2025-08-25 20:10:09 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 20:10:09 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 20:10:35 - INFO - ✅ 第1轮评估完成: 王少萍 | 分数: 0.0
2025-08-25 20:10:35 - INFO - 检测到不合格关键词: 不匹配
2025-08-25 20:10:35 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_20：09_人力资源主管_一二点五三轮筛选结果/初筛不合格\未通过_王少萍_第1轮_评估报告.md
2025-08-25 20:10:35 - INFO - 开始提取候选人 王少萍 的初筛报告信息
2025-08-25 20:10:35 - INFO - 找到优势文本: **优势:**  
    - 学历符合要求，专业背景对口  
    - 具备招聘、培训、绩效管理、员工关系等基础人力资源模块经验  
    - 持有人力资源管理师证书，具备一定专业资质  

- 
2025-08-25 20:10:35 - INFO - 找到风险文本: **风险与不足:**  
    - 期望薪资远高于JD薪资上限（15K-20K vs 6-7K），存在不匹配问题  
    - 缺乏统筹人力资源管理工作的经验  
    - 无员工生命周期管理、组织变革推动、薪酬福利体系设计等相关经验  
    - 当前职位为HR专员，职级与“主管”岗位要求存在差距  

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人虽学历匹配、具备基础HR经验，但其期望薪资远超JD薪资上限（不匹配），且在核心职责匹配度方面评估结果为“低”，未能体现岗位所需的统筹管理能力与薪酬体系设计经验。
2025-08-25 20:10:35 - INFO - 提取的优势和风险: 优势=学历符合要求，专业背景对口  ；具备招聘、培训、绩效管理、员工关系等基础人力资源模块经验  ；持有人力资源管理师证书，具备一定专业资质  , 风险=期望薪资远高于JD薪资上限（15K-20K vs 6-7K），存在不匹配问题  ；缺乏统筹人力资源管理工作的经验  
2025-08-25 20:10:35 - INFO - 提取到姓名: 王少萍
2025-08-25 20:10:35 - INFO - 提取到年龄: 25岁
2025-08-25 20:10:35 - INFO - 提取到工作经验: 2年
2025-08-25 20:10:35 - INFO - 提取到学历: 本科
2025-08-25 20:10:35 - INFO - 提取到当前职位: HR专员
2025-08-25 20:10:35 - INFO - 提取到期望薪资: 15K-20K
2025-08-25 20:10:35 - INFO - 从初筛报告提取的基本信息: {'name': '王少萍', 'age': '25岁', 'experience': '2年', 'education': '本科', 'current_position': 'HR专员', 'expected_salary': '15K-20K'}
2025-08-25 20:10:35 - INFO - 候选人 王少萍 初筛报告提取完成: advantages=学历符合要求，专业背景对口  ；具备招聘、培训、绩效管理、员工关系等基础人力资源模块经验  ；持有人力资源管理师证书，具备一定专业资质  , risks=期望薪资远高于JD薪资上限（15K-20K vs 6-7K），存在不匹配问题  ；缺乏统筹人力资源管理工作的经验  , basic_info={'name': '王少萍', 'age': '25岁', 'experience': '2年', 'education': '本科', 'current_position': 'HR专员', 'expected_salary': '15K-20K'}
2025-08-25 20:10:35 - INFO - ❌ 王少萍 未通过第1轮筛选，得分: 0.0
2025-08-25 20:10:35 - INFO - ✅ 保存新候选人简历到VL目录: ./文本简历/VL_Result_人力资源主管\黄女士_VL识别结果.txt
2025-08-25 20:10:35 - INFO - 开始处理简历: 黄女士
2025-08-25 20:10:35 - INFO - 开始第1轮评估: 黄女士
2025-08-25 20:10:35 - INFO - 岗位名称: 人力资源主管
2025-08-25 20:10:35 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 20:10:35 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 20:10:53 - INFO - ✅ 第1轮评估完成: 黄女士 | 分数: 0.0
2025-08-25 20:10:53 - INFO - 检测到不合格关键词: 不匹配
2025-08-25 20:10:53 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_20：09_人力资源主管_一二点五三轮筛选结果/初筛不合格\未通过_黄女士_第1轮_评估报告.md
2025-08-25 20:10:53 - INFO - 开始提取候选人 黄女士 的初筛报告信息
2025-08-25 20:10:53 - INFO - 找到优势文本: **优势:**
    - 学历符合要求，专业对口；
    - 拥有2年人力资源相关工作经验，具备招聘、培训、绩效管理、员工关系等基础能力；
    - 持有人力资源管理师证书，专业性较强。

- 
2025-08-25 20:10:53 - INFO - 找到风险文本: **风险与不足:**
    - 期望薪资远高于岗位预算，存在严重不匹配；
    - 当前职位为HR专员，缺乏主管层级所需的统筹管理、制度设计及组织变革推动经验；
    - 简历中未体现薪酬福利体系设计、员工生命周期管理等关键职责的相关经验。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人期望薪资（15K-20K）远高于岗位预算（6-7K），超过1.2倍上限，判定为“不匹配”，因此直接淘汰。尽管学历和职责匹配度尚可，但硬性门槛不满足，无法进入下一轮。
2025-08-25 20:10:53 - INFO - 提取的优势和风险: 优势=- 拥有2年人力资源相关工作经验，具备招聘、培训、绩效管理、员工关系等基础能力；- 持有人力资源管理师证书，专业性较强。

-, 风险=- 当前职位为HR专员，缺乏主管层级所需的统筹管理、制度设计及组织变革推动经验；- 简历中未体现薪酬福利体系设计、员工生命周期管理等关键职责的相关经验。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人期望薪资（15K-20K）远高于岗位预算（6-7K），超过1.2倍上限，判定为“不匹配”，因此直接淘汰。尽管学历和职责匹配度尚可，但硬性门槛不满足，无法进入下一轮。
2025-08-25 20:10:53 - INFO - 提取到姓名: 黄女士
2025-08-25 20:10:53 - INFO - 提取到年龄: 25岁
2025-08-25 20:10:53 - INFO - 提取到工作经验: 2年
2025-08-25 20:10:53 - INFO - 提取到学历: 本科
2025-08-25 20:10:53 - INFO - 提取到当前职位: HR专员
2025-08-25 20:10:53 - INFO - 提取到期望薪资: 15K-20K
2025-08-25 20:10:53 - INFO - 从初筛报告提取的基本信息: {'name': '黄女士', 'age': '25岁', 'experience': '2年', 'education': '本科', 'current_position': 'HR专员', 'expected_salary': '15K-20K'}
2025-08-25 20:10:53 - INFO - 候选人 黄女士 初筛报告提取完成: advantages=- 拥有2年人力资源相关工作经验，具备招聘、培训、绩效管理、员工关系等基础能力；- 持有人力资源管理师证书，专业性较强。

-, risks=- 当前职位为HR专员，缺乏主管层级所需的统筹管理、制度设计及组织变革推动经验；- 简历中未体现薪酬福利体系设计、员工生命周期管理等关键职责的相关经验。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人期望薪资（15K-20K）远高于岗位预算（6-7K），超过1.2倍上限，判定为“不匹配”，因此直接淘汰。尽管学历和职责匹配度尚可，但硬性门槛不满足，无法进入下一轮。, basic_info={'name': '黄女士', 'age': '25岁', 'experience': '2年', 'education': '本科', 'current_position': 'HR专员', 'expected_salary': '15K-20K'}
2025-08-25 20:10:53 - INFO - ❌ 黄女士 未通过第1轮筛选，得分: 0.0
2025-08-25 20:10:53 - INFO - ✅ 保存新候选人简历到VL目录: ./文本简历/VL_Result_人力资源主管\李潭_VL识别结果.txt
2025-08-25 20:10:53 - INFO - 开始处理简历: 李潭
2025-08-25 20:10:53 - INFO - 开始第1轮评估: 李潭
2025-08-25 20:10:53 - INFO - 岗位名称: 人力资源主管
2025-08-25 20:10:53 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 20:10:53 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 20:11:11 - INFO - ✅ 第1轮评估完成: 李潭 | 分数: 0.0
2025-08-25 20:11:11 - INFO - 检测到不合格关键词: 不匹配
2025-08-25 20:11:11 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_20：09_人力资源主管_一二点五三轮筛选结果/初筛不合格\未通过_李潭_第1轮_评估报告.md
2025-08-25 20:11:11 - INFO - 开始提取候选人 李潭 的初筛报告信息
2025-08-25 20:11:11 - INFO - 找到优势文本: **优势:** 
    - 专业对口，具备人力资源管理相关证书；
    - 有招聘、绩效、员工关系等模块实操经验；
    - 年龄符合常规招聘预期。

- 
2025-08-25 20:11:11 - INFO - 找到风险文本: **风险与不足:** 
    - 期望薪资远高于JD薪资上限，超出20%阈值；
    - 缺乏统筹管理经验，难以胜任“主管”级别的职责；
    - 无薪酬福利体系设计经验，与JD核心职责不匹配。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人期望薪资远高于JD薪资上限（超出20%），且缺乏岗位所需统筹管理与薪酬体系设计经验，核心职责匹配度为“低”，因此淘汰。
2025-08-25 20:11:11 - INFO - 提取的优势和风险: 优势=- 有招聘、绩效、员工关系等模块实操经验；- 年龄符合常规招聘预期。

-, 风险=- 缺乏统筹管理经验，难以胜任“主管”级别的职责；- 无薪酬福利体系设计经验，与JD核心职责不匹配。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人期望薪资远高于JD薪资上限（超出20%），且缺乏岗位所需统筹管理与薪酬体系设计经验，核心职责匹配度为“低”，因此淘汰。
2025-08-25 20:11:11 - INFO - 提取到姓名: 李潭
2025-08-25 20:11:11 - INFO - 提取到年龄: 25岁
2025-08-25 20:11:11 - INFO - 提取到工作经验: 2年
2025-08-25 20:11:11 - INFO - 提取到学历: 本科
2025-08-25 20:11:11 - INFO - 提取到当前职位: HR专员
2025-08-25 20:11:11 - INFO - 提取到期望薪资: 15K-20K
2025-08-25 20:11:11 - INFO - 从初筛报告提取的基本信息: {'name': '李潭', 'age': '25岁', 'experience': '2年', 'education': '本科', 'current_position': 'HR专员', 'expected_salary': '15K-20K'}
2025-08-25 20:11:11 - INFO - 候选人 李潭 初筛报告提取完成: advantages=- 有招聘、绩效、员工关系等模块实操经验；- 年龄符合常规招聘预期。

-, risks=- 缺乏统筹管理经验，难以胜任“主管”级别的职责；- 无薪酬福利体系设计经验，与JD核心职责不匹配。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人期望薪资远高于JD薪资上限（超出20%），且缺乏岗位所需统筹管理与薪酬体系设计经验，核心职责匹配度为“低”，因此淘汰。, basic_info={'name': '李潭', 'age': '25岁', 'experience': '2年', 'education': '本科', 'current_position': 'HR专员', 'expected_salary': '15K-20K'}
2025-08-25 20:11:11 - INFO - ❌ 李潭 未通过第1轮筛选，得分: 0.0
2025-08-25 20:11:39 - INFO - 找到报告: 未通过_李潭_第1轮_评估报告.md, 轮次: 1
2025-08-25 20:11:39 - INFO - 候选人 李潭 找到 1 个报告
2025-08-25 20:27:39 - INFO - 🚀 启动智能简历评估系统
2025-08-25 20:27:39 - INFO - 📋 系统功能:
2025-08-25 20:27:39 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 20:27:39 - INFO -   - 多线程并发处理
2025-08-25 20:27:39 - INFO -   - 实时进度监控
2025-08-25 20:27:39 - INFO -   - 集成Boss直聘爬虫系统
2025-08-25 20:27:39 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 20:27:39 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 20:27:39 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-08-25 20:27:39 - INFO - 多进程任务管理已初始化
2025-08-25 20:27:45 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 20:27:45 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 20:27:45 - INFO - 查找历史记录路径: D:\project\智能招聘系统_\user\杨锐雄\Result
2025-08-25 20:27:48 - INFO - 爬取任务已启动: 96109aac-eed7-4c74-b91c-ef6b5b1b4c9c, 职位: 人力资源主管
2025-08-25 20:27:48 - INFO - 🚀 开始调用爬虫程序: 96109aac-eed7-4c74-b91c-ef6b5b1b4c9c, 职位: 人力资源主管, 目标数量: 5
2025-08-25 20:27:48 - INFO - 🔍 检查岗位匹配...
2025-08-25 20:27:57 - INFO - 岗位匹配结果: {'success': True, 'matched': True, 'current_job': '人力资源主管', 'recommendation': '人力资源主管 _ 珠海 10-12K', 'match_reason': '完全匹配', 'message': '岗位匹配成功！当前推荐: 人力资源主管 _ 珠海 10-12K'}
2025-08-25 20:27:57 - INFO - ✅ 岗位匹配成功，开始爬取简历...
2025-08-25 20:27:58 - INFO - 爬取任务启动结果: {'task_id': 'task_1', 'message': '收集任务已启动'}
2025-08-25 20:27:58 - INFO - ✅ 爬取任务已启动: task_1
2025-08-25 20:27:58 - INFO - 开始监控爬取进度: task_1
2025-08-25 20:28:08 - ERROR - 监控爬取进度异常: 
2025-08-25 20:28:28 - ERROR - 监控爬取进度异常: 
2025-08-25 20:28:48 - ERROR - 监控爬取进度异常: 
2025-08-25 20:29:01 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 100, 'results_count': 6, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '李潭_人力资源主管_20250825_200512.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李潭_人力资源主管_20250825_200512.png', 'size': 64462, 'created_time': 1756123512.6452193, 'candidate_name': '李潭'}, {'filename': '黄女士_人力资源主管_20250825_200612.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黄女士_人力资源主管_20250825_200612.png', 'size': 195558, 'created_time': 1756123572.7359817, 'candidate_name': '黄女士'}, {'filename': '王少萍_人力资源主管_20250825_200711.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王少萍_人力资源主管_20250825_200711.png', 'size': 160214, 'created_time': 1756123631.0580726, 'candidate_name': '王少萍'}, {'filename': '王淇_人力资源主管_20250825_200810.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王淇_人力资源主管_20250825_200810.png', 'size': 183223, 'created_time': 1756123690.6784935, 'candidate_name': '王淇'}, {'filename': '戈女士_人力资源主管_20250825_200908.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\戈女士_人力资源主管_20250825_200908.png', 'size': 132174, 'created_time': 1756123748.4267347, 'candidate_name': '戈女士'}, {'filename': '苏女士_人力资源主管_20250825_202826.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\苏女士_人力资源主管_20250825_202826.png', 'size': 327858, 'created_time': 1756124906.2056544, 'candidate_name': '苏女士'}]}
2025-08-25 20:29:13 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 100, 'results_count': 6, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '李潭_人力资源主管_20250825_200512.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李潭_人力资源主管_20250825_200512.png', 'size': 64462, 'created_time': 1756123512.6452193, 'candidate_name': '李潭'}, {'filename': '黄女士_人力资源主管_20250825_200612.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黄女士_人力资源主管_20250825_200612.png', 'size': 195558, 'created_time': 1756123572.7359817, 'candidate_name': '黄女士'}, {'filename': '王少萍_人力资源主管_20250825_200711.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王少萍_人力资源主管_20250825_200711.png', 'size': 160214, 'created_time': 1756123631.0580726, 'candidate_name': '王少萍'}, {'filename': '王淇_人力资源主管_20250825_200810.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王淇_人力资源主管_20250825_200810.png', 'size': 183223, 'created_time': 1756123690.6784935, 'candidate_name': '王淇'}, {'filename': '戈女士_人力资源主管_20250825_200908.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\戈女士_人力资源主管_20250825_200908.png', 'size': 132174, 'created_time': 1756123748.4267347, 'candidate_name': '戈女士'}, {'filename': '苏女士_人力资源主管_20250825_202826.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\苏女士_人力资源主管_20250825_202826.png', 'size': 327858, 'created_time': 1756124906.2056544, 'candidate_name': '苏女士'}]}
2025-08-25 20:29:29 - ERROR - 监控爬取进度异常: 
2025-08-25 20:29:47 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 100, 'results_count': 7, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '李潭_人力资源主管_20250825_200512.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李潭_人力资源主管_20250825_200512.png', 'size': 64462, 'created_time': 1756123512.6452193, 'candidate_name': '李潭'}, {'filename': '黄女士_人力资源主管_20250825_200612.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黄女士_人力资源主管_20250825_200612.png', 'size': 195558, 'created_time': 1756123572.7359817, 'candidate_name': '黄女士'}, {'filename': '王少萍_人力资源主管_20250825_200711.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王少萍_人力资源主管_20250825_200711.png', 'size': 160214, 'created_time': 1756123631.0580726, 'candidate_name': '王少萍'}, {'filename': '王淇_人力资源主管_20250825_200810.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王淇_人力资源主管_20250825_200810.png', 'size': 183223, 'created_time': 1756123690.6784935, 'candidate_name': '王淇'}, {'filename': '戈女士_人力资源主管_20250825_200908.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\戈女士_人力资源主管_20250825_200908.png', 'size': 132174, 'created_time': 1756123748.4267347, 'candidate_name': '戈女士'}, {'filename': '苏女士_人力资源主管_20250825_202826.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\苏女士_人力资源主管_20250825_202826.png', 'size': 327858, 'created_time': 1756124906.2056544, 'candidate_name': '苏女士'}, {'filename': '王少萍_人力资源主管_20250825_202924.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王少萍_人力资源主管_20250825_202924.png', 'size': 160241, 'created_time': 1756124964.615629, 'candidate_name': '王少萍'}]}
2025-08-25 20:29:59 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 100, 'results_count': 7, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '李潭_人力资源主管_20250825_200512.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李潭_人力资源主管_20250825_200512.png', 'size': 64462, 'created_time': 1756123512.6452193, 'candidate_name': '李潭'}, {'filename': '黄女士_人力资源主管_20250825_200612.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黄女士_人力资源主管_20250825_200612.png', 'size': 195558, 'created_time': 1756123572.7359817, 'candidate_name': '黄女士'}, {'filename': '王少萍_人力资源主管_20250825_200711.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王少萍_人力资源主管_20250825_200711.png', 'size': 160214, 'created_time': 1756123631.0580726, 'candidate_name': '王少萍'}, {'filename': '王淇_人力资源主管_20250825_200810.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王淇_人力资源主管_20250825_200810.png', 'size': 183223, 'created_time': 1756123690.6784935, 'candidate_name': '王淇'}, {'filename': '戈女士_人力资源主管_20250825_200908.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\戈女士_人力资源主管_20250825_200908.png', 'size': 132174, 'created_time': 1756123748.4267347, 'candidate_name': '戈女士'}, {'filename': '苏女士_人力资源主管_20250825_202826.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\苏女士_人力资源主管_20250825_202826.png', 'size': 327858, 'created_time': 1756124906.2056544, 'candidate_name': '苏女士'}, {'filename': '王少萍_人力资源主管_20250825_202924.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王少萍_人力资源主管_20250825_202924.png', 'size': 160241, 'created_time': 1756124964.615629, 'candidate_name': '王少萍'}]}
2025-08-25 20:30:11 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 100, 'results_count': 7, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '李潭_人力资源主管_20250825_200512.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李潭_人力资源主管_20250825_200512.png', 'size': 64462, 'created_time': 1756123512.6452193, 'candidate_name': '李潭'}, {'filename': '黄女士_人力资源主管_20250825_200612.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黄女士_人力资源主管_20250825_200612.png', 'size': 195558, 'created_time': 1756123572.7359817, 'candidate_name': '黄女士'}, {'filename': '王少萍_人力资源主管_20250825_200711.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王少萍_人力资源主管_20250825_200711.png', 'size': 160214, 'created_time': 1756123631.0580726, 'candidate_name': '王少萍'}, {'filename': '王淇_人力资源主管_20250825_200810.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王淇_人力资源主管_20250825_200810.png', 'size': 183223, 'created_time': 1756123690.6784935, 'candidate_name': '王淇'}, {'filename': '戈女士_人力资源主管_20250825_200908.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\戈女士_人力资源主管_20250825_200908.png', 'size': 132174, 'created_time': 1756123748.4267347, 'candidate_name': '戈女士'}, {'filename': '苏女士_人力资源主管_20250825_202826.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\苏女士_人力资源主管_20250825_202826.png', 'size': 327858, 'created_time': 1756124906.2056544, 'candidate_name': '苏女士'}, {'filename': '王少萍_人力资源主管_20250825_202924.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王少萍_人力资源主管_20250825_202924.png', 'size': 160241, 'created_time': 1756124964.615629, 'candidate_name': '王少萍'}]}
2025-08-25 20:30:27 - ERROR - 监控爬取进度异常: 
2025-08-25 20:30:47 - ERROR - 监控爬取进度异常: 
2025-08-25 20:31:02 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 100, 'results_count': 8, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '李潭_人力资源主管_20250825_200512.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李潭_人力资源主管_20250825_200512.png', 'size': 64462, 'created_time': 1756123512.6452193, 'candidate_name': '李潭'}, {'filename': '黄女士_人力资源主管_20250825_200612.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黄女士_人力资源主管_20250825_200612.png', 'size': 195558, 'created_time': 1756123572.7359817, 'candidate_name': '黄女士'}, {'filename': '王少萍_人力资源主管_20250825_200711.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王少萍_人力资源主管_20250825_200711.png', 'size': 160214, 'created_time': 1756123631.0580726, 'candidate_name': '王少萍'}, {'filename': '王淇_人力资源主管_20250825_200810.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王淇_人力资源主管_20250825_200810.png', 'size': 183223, 'created_time': 1756123690.6784935, 'candidate_name': '王淇'}, {'filename': '戈女士_人力资源主管_20250825_200908.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\戈女士_人力资源主管_20250825_200908.png', 'size': 132174, 'created_time': 1756123748.4267347, 'candidate_name': '戈女士'}, {'filename': '苏女士_人力资源主管_20250825_202826.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\苏女士_人力资源主管_20250825_202826.png', 'size': 327858, 'created_time': 1756124906.2056544, 'candidate_name': '苏女士'}, {'filename': '王少萍_人力资源主管_20250825_202924.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王少萍_人力资源主管_20250825_202924.png', 'size': 160241, 'created_time': 1756124964.615629, 'candidate_name': '王少萍'}, {'filename': '麦女士_人力资源主管_20250825_203027.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\麦女士_人力资源主管_20250825_203027.png', 'size': 211247, 'created_time': 1756125027.1171732, 'candidate_name': '麦女士'}]}
2025-08-25 20:31:14 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 100, 'results_count': 8, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '李潭_人力资源主管_20250825_200512.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李潭_人力资源主管_20250825_200512.png', 'size': 64462, 'created_time': 1756123512.6452193, 'candidate_name': '李潭'}, {'filename': '黄女士_人力资源主管_20250825_200612.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黄女士_人力资源主管_20250825_200612.png', 'size': 195558, 'created_time': 1756123572.7359817, 'candidate_name': '黄女士'}, {'filename': '王少萍_人力资源主管_20250825_200711.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王少萍_人力资源主管_20250825_200711.png', 'size': 160214, 'created_time': 1756123631.0580726, 'candidate_name': '王少萍'}, {'filename': '王淇_人力资源主管_20250825_200810.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王淇_人力资源主管_20250825_200810.png', 'size': 183223, 'created_time': 1756123690.6784935, 'candidate_name': '王淇'}, {'filename': '戈女士_人力资源主管_20250825_200908.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\戈女士_人力资源主管_20250825_200908.png', 'size': 132174, 'created_time': 1756123748.4267347, 'candidate_name': '戈女士'}, {'filename': '苏女士_人力资源主管_20250825_202826.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\苏女士_人力资源主管_20250825_202826.png', 'size': 327858, 'created_time': 1756124906.2056544, 'candidate_name': '苏女士'}, {'filename': '王少萍_人力资源主管_20250825_202924.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王少萍_人力资源主管_20250825_202924.png', 'size': 160241, 'created_time': 1756124964.615629, 'candidate_name': '王少萍'}, {'filename': '麦女士_人力资源主管_20250825_203027.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\麦女士_人力资源主管_20250825_203027.png', 'size': 211247, 'created_time': 1756125027.1171732, 'candidate_name': '麦女士'}]}
2025-08-25 20:31:29 - ERROR - 监控爬取进度异常: 
2025-08-25 20:31:49 - ERROR - 监控爬取进度异常: 
2025-08-25 20:32:01 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 100, 'results_count': 9, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '李潭_人力资源主管_20250825_200512.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李潭_人力资源主管_20250825_200512.png', 'size': 64462, 'created_time': 1756123512.6452193, 'candidate_name': '李潭'}, {'filename': '黄女士_人力资源主管_20250825_200612.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黄女士_人力资源主管_20250825_200612.png', 'size': 195558, 'created_time': 1756123572.7359817, 'candidate_name': '黄女士'}, {'filename': '王少萍_人力资源主管_20250825_200711.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王少萍_人力资源主管_20250825_200711.png', 'size': 160214, 'created_time': 1756123631.0580726, 'candidate_name': '王少萍'}, {'filename': '王淇_人力资源主管_20250825_200810.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王淇_人力资源主管_20250825_200810.png', 'size': 183223, 'created_time': 1756123690.6784935, 'candidate_name': '王淇'}, {'filename': '戈女士_人力资源主管_20250825_200908.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\戈女士_人力资源主管_20250825_200908.png', 'size': 132174, 'created_time': 1756123748.4267347, 'candidate_name': '戈女士'}, {'filename': '苏女士_人力资源主管_20250825_202826.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\苏女士_人力资源主管_20250825_202826.png', 'size': 327858, 'created_time': 1756124906.2056544, 'candidate_name': '苏女士'}, {'filename': '王少萍_人力资源主管_20250825_202924.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王少萍_人力资源主管_20250825_202924.png', 'size': 160241, 'created_time': 1756124964.615629, 'candidate_name': '王少萍'}, {'filename': '麦女士_人力资源主管_20250825_203027.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\麦女士_人力资源主管_20250825_203027.png', 'size': 211247, 'created_time': 1756125027.1171732, 'candidate_name': '麦女士'}, {'filename': '李佩萍_人力资源主管_20250825_203126.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李佩萍_人力资源主管_20250825_203126.png', 'size': 173677, 'created_time': 1756125086.658317, 'candidate_name': '李佩萍'}]}
2025-08-25 20:32:13 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 100, 'results_count': 9, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '李潭_人力资源主管_20250825_200512.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李潭_人力资源主管_20250825_200512.png', 'size': 64462, 'created_time': 1756123512.6452193, 'candidate_name': '李潭'}, {'filename': '黄女士_人力资源主管_20250825_200612.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黄女士_人力资源主管_20250825_200612.png', 'size': 195558, 'created_time': 1756123572.7359817, 'candidate_name': '黄女士'}, {'filename': '王少萍_人力资源主管_20250825_200711.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王少萍_人力资源主管_20250825_200711.png', 'size': 160214, 'created_time': 1756123631.0580726, 'candidate_name': '王少萍'}, {'filename': '王淇_人力资源主管_20250825_200810.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王淇_人力资源主管_20250825_200810.png', 'size': 183223, 'created_time': 1756123690.6784935, 'candidate_name': '王淇'}, {'filename': '戈女士_人力资源主管_20250825_200908.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\戈女士_人力资源主管_20250825_200908.png', 'size': 132174, 'created_time': 1756123748.4267347, 'candidate_name': '戈女士'}, {'filename': '苏女士_人力资源主管_20250825_202826.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\苏女士_人力资源主管_20250825_202826.png', 'size': 327858, 'created_time': 1756124906.2056544, 'candidate_name': '苏女士'}, {'filename': '王少萍_人力资源主管_20250825_202924.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王少萍_人力资源主管_20250825_202924.png', 'size': 160241, 'created_time': 1756124964.615629, 'candidate_name': '王少萍'}, {'filename': '麦女士_人力资源主管_20250825_203027.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\麦女士_人力资源主管_20250825_203027.png', 'size': 211247, 'created_time': 1756125027.1171732, 'candidate_name': '麦女士'}, {'filename': '李佩萍_人力资源主管_20250825_203126.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李佩萍_人力资源主管_20250825_203126.png', 'size': 173677, 'created_time': 1756125086.658317, 'candidate_name': '李佩萍'}]}
2025-08-25 20:32:29 - ERROR - 监控爬取进度异常: 
2025-08-25 20:32:39 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'completed', 'progress': 100, 'results_count': 10, 'target_count': 5, 'current_action': '收集完成', 'current_resumes': [{'filename': '李潭_人力资源主管_20250825_200512.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李潭_人力资源主管_20250825_200512.png', 'size': 64462, 'created_time': 1756123512.6452193, 'candidate_name': '李潭'}, {'filename': '黄女士_人力资源主管_20250825_200612.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\黄女士_人力资源主管_20250825_200612.png', 'size': 195558, 'created_time': 1756123572.7359817, 'candidate_name': '黄女士'}, {'filename': '王少萍_人力资源主管_20250825_200711.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王少萍_人力资源主管_20250825_200711.png', 'size': 160214, 'created_time': 1756123631.0580726, 'candidate_name': '王少萍'}, {'filename': '王淇_人力资源主管_20250825_200810.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王淇_人力资源主管_20250825_200810.png', 'size': 183223, 'created_time': 1756123690.6784935, 'candidate_name': '王淇'}, {'filename': '戈女士_人力资源主管_20250825_200908.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\戈女士_人力资源主管_20250825_200908.png', 'size': 132174, 'created_time': 1756123748.4267347, 'candidate_name': '戈女士'}, {'filename': '苏女士_人力资源主管_20250825_202826.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\苏女士_人力资源主管_20250825_202826.png', 'size': 327858, 'created_time': 1756124906.2056544, 'candidate_name': '苏女士'}, {'filename': '王少萍_人力资源主管_20250825_202924.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王少萍_人力资源主管_20250825_202924.png', 'size': 160241, 'created_time': 1756124964.615629, 'candidate_name': '王少萍'}, {'filename': '麦女士_人力资源主管_20250825_203027.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\麦女士_人力资源主管_20250825_203027.png', 'size': 211247, 'created_time': 1756125027.1171732, 'candidate_name': '麦女士'}, {'filename': '李佩萍_人力资源主管_20250825_203126.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\李佩萍_人力资源主管_20250825_203126.png', 'size': 173677, 'created_time': 1756125086.658317, 'candidate_name': '李佩萍'}, {'filename': '尹婉舒_人力资源主管_20250825_203223.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\尹婉舒_人力资源主管_20250825_203223.png', 'size': 119074, 'created_time': 1756125143.7941334, 'candidate_name': '尹婉舒'}]}
2025-08-25 20:32:39 - INFO - 爬取完成，获取简历列表...
2025-08-25 20:32:39 - INFO - 获取到 10 份简历
2025-08-25 20:32:39 - INFO - 爬取任务完成: 96109aac-eed7-4c74-b91c-ef6b5b1b4c9c, 共获取 10 份简历
2025-08-25 20:35:20 - INFO - 爬取任务已启动: 760ef2c2-2c68-4a9d-b73b-96a5f787ee59, 职位: AI产品经理
2025-08-25 20:35:20 - INFO - 🚀 开始调用爬虫程序: 760ef2c2-2c68-4a9d-b73b-96a5f787ee59, 职位: AI产品经理, 目标数量: 5
2025-08-25 20:35:20 - INFO - 🔍 检查岗位匹配...
2025-08-25 20:35:46 - INFO - 岗位匹配结果: {'success': True, 'matched': True, 'current_job': 'ai产品经理', 'message': '成功切换到岗位: ai产品经理'}
2025-08-25 20:35:46 - INFO - ✅ 岗位匹配成功，开始爬取简历...
2025-08-25 20:35:47 - INFO - 爬取任务启动结果: {'task_id': 'task_2', 'message': '收集任务已启动'}
2025-08-25 20:35:47 - INFO - ✅ 爬取任务已启动: task_2
2025-08-25 20:35:47 - INFO - 开始监控爬取进度: task_2
2025-08-25 20:35:57 - ERROR - 监控爬取进度异常: 
2025-08-25 20:36:18 - ERROR - 监控爬取进度异常: 
2025-08-25 20:36:38 - ERROR - 监控爬取进度异常: 
2025-08-25 20:36:59 - ERROR - 监控爬取进度异常: 
2025-08-25 20:37:09 - INFO - 爬取状态: {'task_id': 'task_2', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-08-25 20:37:21 - INFO - 爬取状态: {'task_id': 'task_2', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-08-25 20:37:33 - INFO - 爬取状态: {'task_id': 'task_2', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-08-25 20:37:49 - ERROR - 监控爬取进度异常: 
2025-08-25 20:38:09 - ERROR - 监控爬取进度异常: 
2025-08-25 20:38:29 - ERROR - 监控爬取进度异常: 
2025-08-25 20:38:49 - ERROR - 监控爬取进度异常: 
2025-08-25 20:39:00 - INFO - 爬取状态: {'task_id': 'task_2', 'status': 'running', 'progress': 20.0, 'results_count': 1, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.5120854, 'candidate_name': '刘俊锋'}]}
2025-08-25 20:39:12 - INFO - 爬取状态: {'task_id': 'task_2', 'status': 'running', 'progress': 20.0, 'results_count': 1, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.5120854, 'candidate_name': '刘俊锋'}]}
2025-08-25 20:39:24 - INFO - 爬取状态: {'task_id': 'task_2', 'status': 'running', 'progress': 20.0, 'results_count': 1, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.5120854, 'candidate_name': '刘俊锋'}]}
2025-08-25 20:39:39 - ERROR - 监控爬取进度异常: 
2025-08-25 20:40:00 - ERROR - 监控爬取进度异常: 
2025-08-25 20:40:21 - ERROR - 监控爬取进度异常: 
2025-08-25 20:40:35 - INFO - 爬取状态: {'task_id': 'task_2', 'status': 'running', 'progress': 20.0, 'results_count': 1, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.5120854, 'candidate_name': '刘俊锋'}]}
2025-08-25 20:40:47 - INFO - 爬取状态: {'task_id': 'task_2', 'status': 'running', 'progress': 20.0, 'results_count': 1, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.5120854, 'candidate_name': '刘俊锋'}]}
2025-08-25 20:40:59 - INFO - 爬取状态: {'task_id': 'task_2', 'status': 'running', 'progress': 20.0, 'results_count': 1, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.5120854, 'candidate_name': '刘俊锋'}]}
2025-08-25 20:41:15 - ERROR - 监控爬取进度异常: 
2025-08-25 20:41:35 - ERROR - 监控爬取进度异常: 
2025-08-25 20:41:46 - INFO - 爬取状态: {'task_id': 'task_2', 'status': 'running', 'progress': 20.0, 'results_count': 1, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.5120854, 'candidate_name': '刘俊锋'}]}
2025-08-25 20:41:51 - INFO - 爬取状态: {'task_id': 'task_2', 'status': 'completed', 'progress': 100, 'results_count': 1, 'target_count': 5, 'current_action': '收集完成', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.5120854, 'candidate_name': '刘俊锋'}]}
2025-08-25 20:41:51 - INFO - 爬取完成，获取简历列表...
2025-08-25 20:41:51 - INFO - 获取到 1 份简历
2025-08-25 20:41:51 - INFO - 爬取任务完成: 760ef2c2-2c68-4a9d-b73b-96a5f787ee59, 共获取 1 份简历
2025-08-25 20:47:28 - INFO - 🚀 启动智能简历评估系统
2025-08-25 20:47:28 - INFO - 📋 系统功能:
2025-08-25 20:47:28 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 20:47:28 - INFO -   - 多线程并发处理
2025-08-25 20:47:28 - INFO -   - 实时进度监控
2025-08-25 20:47:28 - INFO -   - 集成Boss直聘爬虫系统
2025-08-25 20:47:28 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 20:47:28 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 20:47:28 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-08-25 20:47:29 - INFO - 多进程任务管理已初始化
2025-08-25 20:47:33 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 20:47:33 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 20:47:33 - INFO - 查找历史记录路径: D:\project\智能招聘系统_\user\杨锐雄\Result
2025-08-25 20:47:38 - INFO - 爬取任务已启动: 624e8e0b-da59-4f7a-8325-09039437b45a, 职位: 人力资源主管
2025-08-25 20:47:38 - INFO - 🚀 开始调用爬虫程序: 624e8e0b-da59-4f7a-8325-09039437b45a, 职位: 人力资源主管, 目标数量: 5
2025-08-25 20:47:38 - INFO - 🔍 检查岗位匹配...
2025-08-25 20:47:47 - INFO - 岗位匹配结果: {'success': True, 'matched': True, 'current_job': '人力资源主管', 'recommendation': '人力资源主管 _ 珠海 10-12K', 'match_reason': '完全匹配', 'message': '岗位匹配成功！当前推荐: 人力资源主管 _ 珠海 10-12K'}
2025-08-25 20:47:47 - INFO - ✅ 岗位匹配成功，开始爬取简历...
2025-08-25 20:47:47 - INFO - 爬取任务启动结果: {'task_id': 'task_1', 'message': '收集任务已启动'}
2025-08-25 20:47:47 - INFO - ✅ 爬取任务已启动: task_1
2025-08-25 20:47:47 - INFO - 开始监控爬取进度: task_1
2025-08-25 20:47:58 - ERROR - 监控爬取进度异常: 
2025-08-25 20:48:18 - ERROR - 监控爬取进度异常: 
2025-08-25 20:48:30 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 20.0, 'results_count': 1, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '周女士_人力资源主管_20250825_204807.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\周女士_人力资源主管_20250825_204807.png', 'size': 170492, 'created_time': 1756126087.580067, 'candidate_name': '周女士'}]}
2025-08-25 20:48:42 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 20.0, 'results_count': 1, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '周女士_人力资源主管_20250825_204807.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\周女士_人力资源主管_20250825_204807.png', 'size': 170492, 'created_time': 1756126087.580067, 'candidate_name': '周女士'}]}
2025-08-25 20:48:54 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 20.0, 'results_count': 1, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '周女士_人力资源主管_20250825_204807.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\周女士_人力资源主管_20250825_204807.png', 'size': 170492, 'created_time': 1756126087.580067, 'candidate_name': '周女士'}]}
2025-08-25 20:49:10 - ERROR - 监控爬取进度异常: 
2025-08-25 20:49:30 - ERROR - 监控爬取进度异常: 
2025-08-25 20:49:42 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 40.0, 'results_count': 2, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '周女士_人力资源主管_20250825_204807.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\周女士_人力资源主管_20250825_204807.png', 'size': 170492, 'created_time': 1756126087.580067, 'candidate_name': '周女士'}, {'filename': '郭女士_人力资源主管_20250825_204907.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\郭女士_人力资源主管_20250825_204907.png', 'size': 176338, 'created_time': 1756126147.7131734, 'candidate_name': '郭女士'}]}
2025-08-25 20:49:54 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 40.0, 'results_count': 2, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '周女士_人力资源主管_20250825_204807.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\周女士_人力资源主管_20250825_204807.png', 'size': 170492, 'created_time': 1756126087.580067, 'candidate_name': '周女士'}, {'filename': '郭女士_人力资源主管_20250825_204907.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\郭女士_人力资源主管_20250825_204907.png', 'size': 176338, 'created_time': 1756126147.7131734, 'candidate_name': '郭女士'}]}
2025-08-25 20:50:10 - ERROR - 监控爬取进度异常: 
2025-08-25 20:50:29 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 3, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '周女士_人力资源主管_20250825_204807.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\周女士_人力资源主管_20250825_204807.png', 'size': 170492, 'created_time': 1756126087.580067, 'candidate_name': '周女士'}, {'filename': '郭女士_人力资源主管_20250825_204907.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\郭女士_人力资源主管_20250825_204907.png', 'size': 176338, 'created_time': 1756126147.7131734, 'candidate_name': '郭女士'}, {'filename': '陈女士_人力资源主管_20250825_205006.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\陈女士_人力资源主管_20250825_205006.png', 'size': 152288, 'created_time': 1756126206.0618265, 'candidate_name': '陈女士'}]}
2025-08-25 20:50:41 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 3, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '周女士_人力资源主管_20250825_204807.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\周女士_人力资源主管_20250825_204807.png', 'size': 170492, 'created_time': 1756126087.580067, 'candidate_name': '周女士'}, {'filename': '郭女士_人力资源主管_20250825_204907.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\郭女士_人力资源主管_20250825_204907.png', 'size': 176338, 'created_time': 1756126147.7131734, 'candidate_name': '郭女士'}, {'filename': '陈女士_人力资源主管_20250825_205006.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\陈女士_人力资源主管_20250825_205006.png', 'size': 152288, 'created_time': 1756126206.0618265, 'candidate_name': '陈女士'}]}
2025-08-25 20:50:53 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 3, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '周女士_人力资源主管_20250825_204807.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\周女士_人力资源主管_20250825_204807.png', 'size': 170492, 'created_time': 1756126087.580067, 'candidate_name': '周女士'}, {'filename': '郭女士_人力资源主管_20250825_204907.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\郭女士_人力资源主管_20250825_204907.png', 'size': 176338, 'created_time': 1756126147.7131734, 'candidate_name': '郭女士'}, {'filename': '陈女士_人力资源主管_20250825_205006.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\陈女士_人力资源主管_20250825_205006.png', 'size': 152288, 'created_time': 1756126206.0618265, 'candidate_name': '陈女士'}]}
2025-08-25 20:51:08 - ERROR - 监控爬取进度异常: 
2025-08-25 20:51:28 - ERROR - 监控爬取进度异常: 
2025-08-25 20:51:40 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 80.0, 'results_count': 4, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '周女士_人力资源主管_20250825_204807.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\周女士_人力资源主管_20250825_204807.png', 'size': 170492, 'created_time': 1756126087.580067, 'candidate_name': '周女士'}, {'filename': '郭女士_人力资源主管_20250825_204907.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\郭女士_人力资源主管_20250825_204907.png', 'size': 176338, 'created_time': 1756126147.7131734, 'candidate_name': '郭女士'}, {'filename': '陈女士_人力资源主管_20250825_205006.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\陈女士_人力资源主管_20250825_205006.png', 'size': 152288, 'created_time': 1756126206.0618265, 'candidate_name': '陈女士'}, {'filename': '莫女士_人力资源主管_20250825_205105.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\莫女士_人力资源主管_20250825_205105.png', 'size': 171555, 'created_time': 1756126265.589722, 'candidate_name': '莫女士'}]}
2025-08-25 20:51:52 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 80.0, 'results_count': 4, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '周女士_人力资源主管_20250825_204807.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\周女士_人力资源主管_20250825_204807.png', 'size': 170492, 'created_time': 1756126087.580067, 'candidate_name': '周女士'}, {'filename': '郭女士_人力资源主管_20250825_204907.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\郭女士_人力资源主管_20250825_204907.png', 'size': 176338, 'created_time': 1756126147.7131734, 'candidate_name': '郭女士'}, {'filename': '陈女士_人力资源主管_20250825_205006.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\陈女士_人力资源主管_20250825_205006.png', 'size': 152288, 'created_time': 1756126206.0618265, 'candidate_name': '陈女士'}, {'filename': '莫女士_人力资源主管_20250825_205105.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\莫女士_人力资源主管_20250825_205105.png', 'size': 171555, 'created_time': 1756126265.589722, 'candidate_name': '莫女士'}]}
2025-08-25 20:52:08 - ERROR - 监控爬取进度异常: 
2025-08-25 20:52:18 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'completed', 'progress': 100, 'results_count': 5, 'target_count': 5, 'current_action': '收集完成', 'current_resumes': [{'filename': '周女士_人力资源主管_20250825_204807.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\周女士_人力资源主管_20250825_204807.png', 'size': 170492, 'created_time': 1756126087.580067, 'candidate_name': '周女士'}, {'filename': '郭女士_人力资源主管_20250825_204907.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\郭女士_人力资源主管_20250825_204907.png', 'size': 176338, 'created_time': 1756126147.7131734, 'candidate_name': '郭女士'}, {'filename': '陈女士_人力资源主管_20250825_205006.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\陈女士_人力资源主管_20250825_205006.png', 'size': 152288, 'created_time': 1756126206.0618265, 'candidate_name': '陈女士'}, {'filename': '莫女士_人力资源主管_20250825_205105.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\莫女士_人力资源主管_20250825_205105.png', 'size': 171555, 'created_time': 1756126265.589722, 'candidate_name': '莫女士'}, {'filename': '林小姐_人力资源主管_20250825_205201.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\林小姐_人力资源主管_20250825_205201.png', 'size': 85009, 'created_time': 1756126321.4866045, 'candidate_name': '林小姐'}]}
2025-08-25 20:52:18 - INFO - 爬取完成，获取简历列表...
2025-08-25 20:52:18 - INFO - 获取到 5 份简历
2025-08-25 20:52:18 - INFO - 爬取任务完成: 624e8e0b-da59-4f7a-8325-09039437b45a, 共获取 5 份简历
2025-08-25 20:54:59 - INFO - 🚀 启动智能简历评估系统
2025-08-25 20:54:59 - INFO - 📋 系统功能:
2025-08-25 20:54:59 - INFO -   - 支持TXT、PDF、图片格式简历
2025-08-25 20:54:59 - INFO -   - 多线程并发处理
2025-08-25 20:54:59 - INFO -   - 实时进度监控
2025-08-25 20:54:59 - INFO -   - 集成Boss直聘爬虫系统
2025-08-25 20:54:59 - INFO - 📱 前端访问地址: http://127.0.0.1:8008
2025-08-25 20:54:59 - INFO -  - JD来源: ./JD库/人力资源主管_JD.md
2025-08-25 20:54:59 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-08-25 20:55:00 - INFO - 多进程任务管理已初始化
2025-08-25 20:55:06 - INFO - 🔐 普通用户登录请求: 杨锐雄
2025-08-25 20:55:06 - INFO - ✅ 用户 杨锐雄 登录成功
2025-08-25 20:55:06 - INFO - 查找历史记录路径: D:\project\智能招聘系统_\user\杨锐雄\Result
2025-08-25 20:55:10 - INFO - 爬取任务已启动: bf2db77a-52b3-4755-8da9-cbdc741dee19, 职位: 人力资源主管
2025-08-25 20:55:10 - INFO - 🚀 开始调用爬虫程序: bf2db77a-52b3-4755-8da9-cbdc741dee19, 职位: 人力资源主管, 目标数量: 5
2025-08-25 20:55:10 - INFO - 🔍 检查岗位匹配...
2025-08-25 20:55:18 - INFO - 岗位匹配结果: {'success': True, 'matched': True, 'current_job': '人力资源主管', 'recommendation': '人力资源主管 _ 珠海 10-12K', 'match_reason': '完全匹配', 'message': '岗位匹配成功！当前推荐: 人力资源主管 _ 珠海 10-12K'}
2025-08-25 20:55:18 - INFO - ✅ 岗位匹配成功，开始爬取简历...
2025-08-25 20:55:19 - INFO - 爬取任务启动结果: {'task_id': 'task_1', 'message': '收集任务已启动'}
2025-08-25 20:55:19 - INFO - ✅ 爬取任务已启动: task_1
2025-08-25 20:55:19 - INFO - 开始监控爬取进度: task_1
2025-08-25 20:55:29 - ERROR - 监控爬取进度异常: 
2025-08-25 20:55:49 - ERROR - 监控爬取进度异常: 
2025-08-25 20:56:09 - ERROR - 监控爬取进度异常: 
2025-08-25 20:56:23 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 20.0, 'results_count': 1, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '王威_人力资源主管_20250825_205547.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王威_人力资源主管_20250825_205547.png', 'size': 305581, 'created_time': 1756126547.991857, 'candidate_name': '王威'}]}
2025-08-25 20:56:35 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 20.0, 'results_count': 1, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '王威_人力资源主管_20250825_205547.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王威_人力资源主管_20250825_205547.png', 'size': 305581, 'created_time': 1756126547.991857, 'candidate_name': '王威'}]}
2025-08-25 20:56:50 - ERROR - 监控爬取进度异常: 
2025-08-25 20:57:10 - ERROR - 监控爬取进度异常: 
2025-08-25 20:57:23 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 40.0, 'results_count': 2, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '王威_人力资源主管_20250825_205547.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王威_人力资源主管_20250825_205547.png', 'size': 305581, 'created_time': 1756126547.991857, 'candidate_name': '王威'}, {'filename': '郭女士_人力资源主管_20250825_205648.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\郭女士_人力资源主管_20250825_205648.png', 'size': 175575, 'created_time': 1756126608.1641302, 'candidate_name': '郭女士'}]}
2025-08-25 20:57:35 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 40.0, 'results_count': 2, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '王威_人力资源主管_20250825_205547.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王威_人力资源主管_20250825_205547.png', 'size': 305581, 'created_time': 1756126547.991857, 'candidate_name': '王威'}, {'filename': '郭女士_人力资源主管_20250825_205648.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\郭女士_人力资源主管_20250825_205648.png', 'size': 175575, 'created_time': 1756126608.1641302, 'candidate_name': '郭女士'}]}
2025-08-25 20:57:51 - ERROR - 监控爬取进度异常: 
2025-08-25 20:58:07 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 3, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '王威_人力资源主管_20250825_205547.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王威_人力资源主管_20250825_205547.png', 'size': 305581, 'created_time': 1756126547.991857, 'candidate_name': '王威'}, {'filename': '郭女士_人力资源主管_20250825_205648.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\郭女士_人力资源主管_20250825_205648.png', 'size': 175575, 'created_time': 1756126608.1641302, 'candidate_name': '郭女士'}, {'filename': '林小姐_人力资源主管_20250825_205744.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\林小姐_人力资源主管_20250825_205744.png', 'size': 84791, 'created_time': 1756126664.058836, 'candidate_name': '林小姐'}]}
2025-08-25 20:58:19 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 3, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '王威_人力资源主管_20250825_205547.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王威_人力资源主管_20250825_205547.png', 'size': 305581, 'created_time': 1756126547.991857, 'candidate_name': '王威'}, {'filename': '郭女士_人力资源主管_20250825_205648.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\郭女士_人力资源主管_20250825_205648.png', 'size': 175575, 'created_time': 1756126608.1641302, 'candidate_name': '郭女士'}, {'filename': '林小姐_人力资源主管_20250825_205744.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\林小姐_人力资源主管_20250825_205744.png', 'size': 84791, 'created_time': 1756126664.058836, 'candidate_name': '林小姐'}]}
2025-08-25 20:58:31 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 3, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '王威_人力资源主管_20250825_205547.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王威_人力资源主管_20250825_205547.png', 'size': 305581, 'created_time': 1756126547.991857, 'candidate_name': '王威'}, {'filename': '郭女士_人力资源主管_20250825_205648.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\郭女士_人力资源主管_20250825_205648.png', 'size': 175575, 'created_time': 1756126608.1641302, 'candidate_name': '郭女士'}, {'filename': '林小姐_人力资源主管_20250825_205744.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\林小姐_人力资源主管_20250825_205744.png', 'size': 84791, 'created_time': 1756126664.058836, 'candidate_name': '林小姐'}]}
2025-08-25 20:58:46 - ERROR - 监控爬取进度异常: 
2025-08-25 20:59:06 - ERROR - 监控爬取进度异常: 
2025-08-25 20:59:19 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 80.0, 'results_count': 4, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '王威_人力资源主管_20250825_205547.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王威_人力资源主管_20250825_205547.png', 'size': 305581, 'created_time': 1756126547.991857, 'candidate_name': '王威'}, {'filename': '郭女士_人力资源主管_20250825_205648.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\郭女士_人力资源主管_20250825_205648.png', 'size': 175575, 'created_time': 1756126608.1641302, 'candidate_name': '郭女士'}, {'filename': '林小姐_人力资源主管_20250825_205744.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\林小姐_人力资源主管_20250825_205744.png', 'size': 84791, 'created_time': 1756126664.058836, 'candidate_name': '林小姐'}, {'filename': '梁滴翠_人力资源主管_20250825_205844.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\梁滴翠_人力资源主管_20250825_205844.png', 'size': 212255, 'created_time': 1756126724.2177646, 'candidate_name': '梁滴翠'}]}
2025-08-25 20:59:31 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 80.0, 'results_count': 4, 'target_count': 5, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '王威_人力资源主管_20250825_205547.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王威_人力资源主管_20250825_205547.png', 'size': 305581, 'created_time': 1756126547.991857, 'candidate_name': '王威'}, {'filename': '郭女士_人力资源主管_20250825_205648.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\郭女士_人力资源主管_20250825_205648.png', 'size': 175575, 'created_time': 1756126608.1641302, 'candidate_name': '郭女士'}, {'filename': '林小姐_人力资源主管_20250825_205744.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\林小姐_人力资源主管_20250825_205744.png', 'size': 84791, 'created_time': 1756126664.058836, 'candidate_name': '林小姐'}, {'filename': '梁滴翠_人力资源主管_20250825_205844.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\梁滴翠_人力资源主管_20250825_205844.png', 'size': 212255, 'created_time': 1756126724.2177646, 'candidate_name': '梁滴翠'}]}
2025-08-25 20:59:47 - ERROR - 监控爬取进度异常: 
2025-08-25 20:59:58 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'completed', 'progress': 100, 'results_count': 5, 'target_count': 5, 'current_action': '收集完成', 'current_resumes': [{'filename': '王威_人力资源主管_20250825_205547.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王威_人力资源主管_20250825_205547.png', 'size': 305581, 'created_time': 1756126547.991857, 'candidate_name': '王威'}, {'filename': '郭女士_人力资源主管_20250825_205648.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\郭女士_人力资源主管_20250825_205648.png', 'size': 175575, 'created_time': 1756126608.1641302, 'candidate_name': '郭女士'}, {'filename': '林小姐_人力资源主管_20250825_205744.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\林小姐_人力资源主管_20250825_205744.png', 'size': 84791, 'created_time': 1756126664.058836, 'candidate_name': '林小姐'}, {'filename': '梁滴翠_人力资源主管_20250825_205844.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\梁滴翠_人力资源主管_20250825_205844.png', 'size': 212255, 'created_time': 1756126724.2177646, 'candidate_name': '梁滴翠'}, {'filename': '王相元_人力资源主管_20250825_205946.png', 'file_path': 'D:\\project\\智能招聘系统_\\high-rpa-boss\\resumes\\人力资源主管\\王相元_人力资源主管_20250825_205946.png', 'size': 230521, 'created_time': 1756126786.2548559, 'candidate_name': '王相元'}]}
2025-08-25 20:59:58 - INFO - 爬取完成，获取简历列表...
2025-08-25 20:59:58 - INFO - 获取到 5 份简历
2025-08-25 20:59:58 - INFO - 爬取任务完成: bf2db77a-52b3-4755-8da9-cbdc741dee19, 共获取 5 份简历
2025-08-25 20:59:59 - INFO - 开始转换MD格式JD: 人力资源主管.md
2025-08-25 20:59:59 - INFO - ✅ 发现已存在的JSON文件: ./user/未知用户/jd\人力资源主管.json
2025-08-25 20:59:59 - INFO - ✅ JSON文件格式验证通过
2025-08-25 21:00:19 - INFO - VL识别成功，识别内容长度: 2335
2025-08-25 21:00:19 - INFO - ✅ 保存VL识别结果: ./文本简历/VL_Result_人力资源主管/王相元_VL识别结果.txt
2025-08-25 21:00:46 - INFO - VL识别成功，识别内容长度: 3307
2025-08-25 21:00:46 - INFO - ✅ 保存VL识别结果: ./文本简历/VL_Result_人力资源主管/梁滴翠_VL识别结果.txt
2025-08-25 21:00:57 - INFO - VL识别成功，识别内容长度: 1233
2025-08-25 21:00:57 - INFO - ✅ 保存VL识别结果: ./文本简历/VL_Result_人力资源主管/林小姐_VL识别结果.txt
2025-08-25 21:02:55 - INFO - VL识别成功，识别内容长度: 20089
2025-08-25 21:02:55 - INFO - ✅ 保存VL识别结果: ./文本简历/VL_Result_人力资源主管/郭女士_VL识别结果.txt
2025-08-25 21:03:06 - INFO - VL识别成功，识别内容长度: 1104
2025-08-25 21:03:06 - INFO - ✅ 保存VL识别结果: ./文本简历/VL_Result_人力资源主管/王威_VL识别结果.txt
2025-08-25 21:03:06 - INFO - 开始转换MD格式JD: 人力资源主管.md
2025-08-25 21:03:06 - INFO - ✅ 发现已存在的JSON文件: ./user/杨锐雄/jd\人力资源主管.json
2025-08-25 21:03:06 - INFO - ✅ JSON文件格式验证通过
2025-08-25 21:03:06 - INFO - 提取的岗位名称: '人力资源主管'
2025-08-25 21:03:06 - INFO - 最终使用的岗位名称: '人力资源主管'
2025-08-25 21:03:25 - INFO - VL识别成功，识别内容长度: 2180
2025-08-25 21:03:25 - INFO - ✅ 保存VL识别结果: ./文本简历/VL_Result_人力资源主管/王相元_VL识别结果.txt
2025-08-25 21:03:25 - INFO - 开始处理简历: 王相元
2025-08-25 21:03:25 - INFO - 开始第1轮评估: 王相元
2025-08-25 21:03:25 - INFO - 岗位名称: 人力资源主管
2025-08-25 21:03:25 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 21:03:25 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 21:03:51 - INFO - ✅ 第1轮评估完成: 王相元 | 分数: 0.0
2025-08-25 21:03:51 - INFO - 检测到明确通过标识: ✅ 通过
2025-08-25 21:03:51 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_21：03_人力资源主管_一二点五三轮筛选结果/初筛合格\通过_王相元_第1轮_评估报告.md
2025-08-25 21:03:51 - INFO - 开始提取候选人 王相元 的初筛报告信息
2025-08-25 21:03:51 - INFO - 找到优势文本: **优势:**
    - 12年丰富的人力资源从业经验，涵盖招聘、员工关系、绩效、培训、福利等多个模块。
    - 具备HRBP和高级人事主管双重角色经验，能有效统筹HR管理工作。
    - 拥有人力资源管理师三级、劳动关系管理师等专业资质，具备体系化设计能力。

- 
2025-08-25 21:03:51 - INFO - 找到风险文本: **风险与不足:**
    - 期望薪资为“面议”，若实际期望高于JD薪资上限（7K），可能存在薪资不匹配风险。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历满足要求，年龄未设上限，核心职责匹配度高，具备人力资源统筹管理、员工生命周期管理及薪酬福利优化的完整经验。虽期望薪资未明确，但暂未触发不匹配条件，建议在下一轮面试中进一步确认薪资期望。
2025-08-25 21:03:51 - INFO - 提取的优势和风险: 优势=12年丰富的人力资源从业经验，涵盖招聘、员工关系、绩效、培训、福利等多个模块。；具备HRBP和高级人事主管双重角色经验，能有效统筹HR管理工作。；拥有人力资源管理师三级、劳动关系管理师等专业资质，具备体系化设计能力。, 风险=期望薪资为“面议”，若实际期望高于JD薪资上限（7K），可能存在薪资不匹配风险。；**淘汰条件：**
2025-08-25 21:03:51 - INFO - 提取到姓名: 王相元
2025-08-25 21:03:51 - INFO - 提取到年龄: 38岁
2025-08-25 21:03:51 - INFO - 提取到工作经验: 12年
2025-08-25 21:03:51 - INFO - 提取到学历: 本科
2025-08-25 21:03:51 - INFO - 提取到当前职位: 高级人事主管
2025-08-25 21:03:51 - INFO - 提取到期望薪资: 面议
2025-08-25 21:03:51 - INFO - 从初筛报告提取的基本信息: {'name': '王相元', 'age': '38岁', 'experience': '12年', 'education': '本科', 'current_position': '高级人事主管', 'expected_salary': '面议'}
2025-08-25 21:03:51 - INFO - 候选人 王相元 初筛报告提取完成: advantages=12年丰富的人力资源从业经验，涵盖招聘、员工关系、绩效、培训、福利等多个模块。；具备HRBP和高级人事主管双重角色经验，能有效统筹HR管理工作。；拥有人力资源管理师三级、劳动关系管理师等专业资质，具备体系化设计能力。, risks=期望薪资为“面议”，若实际期望高于JD薪资上限（7K），可能存在薪资不匹配风险。；**淘汰条件：**, basic_info={'name': '王相元', 'age': '38岁', 'experience': '12年', 'education': '本科', 'current_position': '高级人事主管', 'expected_salary': '面议'}
2025-08-25 21:03:51 - INFO - 开始第2轮评估: 王相元
2025-08-25 21:03:51 - INFO - 岗位名称: 人力资源主管
2025-08-25 21:03:51 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/专业画像分析.md
2025-08-25 21:03:51 - INFO - 第2轮评估使用模型: qwen-plus
2025-08-25 21:04:10 - WARNING - 分数不一致: 总分=82.0, 计算总分=0.0 (A:0.0+B:0.0+C:0.0+D:0.0+E:0.0)
2025-08-25 21:04:10 - WARNING - 候选人王相元分数不一致: 总分=82.0, 计算总分=0.0
2025-08-25 21:04:10 - INFO - ✅ 第2轮评估完成: 王相元 | 分数: 82.0
2025-08-25 21:04:10 - INFO - 未检测到明确关键词，默认通过
2025-08-25 21:04:10 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_21：03_人力资源主管_一二点五三轮筛选结果/画像分析结果\王相元_第2轮_画像分析.md
2025-08-25 21:04:10 - INFO - 开始第2.5轮评估: 王相元
2025-08-25 21:04:10 - INFO - 岗位名称: 人力资源主管
2025-08-25 21:04:10 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/专业画像匹配.md
2025-08-25 21:04:10 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/专业画像匹配.md
2025-08-25 21:04:10 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-08-25 21:04:12 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-08-25 21:04:17 - INFO - ✅ 第2.5轮评估完成: 王相元 | 分数: 0.0
2025-08-25 21:04:17 - INFO - 第2.5轮检测到明确通过标识: ✅ 通过
2025-08-25 21:04:17 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_21：03_人力资源主管_一二点五三轮筛选结果/二筛合格\通过_王相元_第2.5轮_评估报告.md
2025-08-25 21:04:17 - INFO - 开始第3轮评估: 王相元
2025-08-25 21:04:17 - INFO - 岗位名称: 人力资源主管
2025-08-25 21:04:17 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/终极筛选_打分规则.md
2025-08-25 21:04:17 - INFO - 第3轮评估使用模型: qwen-plus
2025-08-25 21:05:08 - INFO - ✅ 第3轮评估完成: 王相元 | 分数: 74.0
2025-08-25 21:05:08 - INFO - 第3轮检测到合格关键词: 通过
2025-08-25 21:05:08 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_21：03_人力资源主管_一二点五三轮筛选结果/最终通过\74.0分_王相元_第3轮_评估报告.md
2025-08-25 21:05:08 - INFO - ✅ 王相元 完成三轮筛选，最终得分: 74.0, 是否通过: True
2025-08-25 21:05:08 - INFO - 候选人 王相元 最终返回信息: advantages=12年丰富的人力资源从业经验，涵盖招聘、员工关系、绩效、培训、福利等多个模块。；具备HRBP和高级人事主管双重角色经验，能有效统筹HR管理工作。；拥有人力资源管理师三级、劳动关系管理师等专业资质，具备体系化设计能力。, risks=期望薪资为“面议”，若实际期望高于JD薪资上限（7K），可能存在薪资不匹配风险。；**淘汰条件：**, basic_info={'name': '王相元', 'age': '38岁', 'experience': '12年', 'education': '本科', 'current_position': '高级人事主管', 'expected_salary': '面议'}
2025-08-25 21:05:37 - INFO - VL识别成功，识别内容长度: 3308
2025-08-25 21:05:37 - INFO - ✅ 保存VL识别结果: ./文本简历/VL_Result_人力资源主管/梁滴翠_VL识别结果.txt
2025-08-25 21:05:37 - INFO - 开始处理简历: 梁滴翠
2025-08-25 21:05:37 - INFO - 开始第1轮评估: 梁滴翠
2025-08-25 21:05:37 - INFO - 岗位名称: 人力资源主管
2025-08-25 21:05:37 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 21:05:37 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 21:06:13 - INFO - ✅ 第1轮评估完成: 梁滴翠 | 分数: 0.0
2025-08-25 21:06:13 - INFO - 检测到不合格关键词: 不匹配
2025-08-25 21:06:13 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_21：03_人力资源主管_一二点五三轮筛选结果/初筛不合格\未通过_梁滴翠_第1轮_评估报告.md
2025-08-25 21:06:13 - INFO - 开始提取候选人 梁滴翠 的初筛报告信息
2025-08-25 21:06:13 - INFO - 找到优势文本: **优势:**
    1. 拥有10年以上人力资源相关经验，涵盖招聘、绩效、薪酬、员工关系等全流程管理；
    2. 曾在500强企业（道达尔）和上市公司（珠海博闻）担任HR岗位，具备较强的实际操作能力；
    3. 有从零到一搭建人力资源体系的经验，具备战略执行和组织变革推动能力。

- 
2025-08-25 21:06:13 - INFO - 找到风险文本: **风险与不足:**
    1. **薪资期望严重超限**（10-11K vs JD要求6-7K），且超出容忍阈值（20%），为**不匹配项**；
    2. 在薪酬福利体系设计方面，缺乏员工满意度提升的具体成果描述。

---

**4. 初筛结论**

- **淘汰条件：**
    1. ✅ 硬性门槛审查 中有任意一项结果为 "**不匹配**"（薪资不匹配）。
    2. ❌ 核心职责匹配度 评估结果为 "**低**"（未达该条件）。

- **通过条件：**
    1. ❌ 硬性门槛审查 结果中**没有** "**不匹配**" 项（存在薪资不匹配）。
    2. ✅ 核心职责匹配度 评估结果为 "**中**" 或 "**高**"（评估为“中”）。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人具备较强的综合人力资源管理能力，且核心职责匹配度为“中”。但由于其期望薪资（10-11K）**远高于岗位薪资上限（7K）且超过20%容忍阈值**，因此在**硬性门槛审查中被淘汰**。
2025-08-25 21:06:13 - INFO - 提取的优势和风险: 优势=2. 曾在500强企业（道达尔）和上市公司（珠海博闻）担任HR岗位，具备较强的实际操作能力；3. 有从零到一搭建人力资源体系的经验，具备战略执行和组织变革推动能力。

-, 风险=2. 在薪酬福利体系设计方面，缺乏员工满意度提升的具体成果描述。

---

**4. 初筛结论**

- **淘汰条件：**
    1. ✅ 硬性门槛审查 中有任意一项结果为 "**不匹配**"（薪资不匹配）。
    2. ❌ 核心职责匹配度 评估结果为 "**低**"（未达该条件）。

- **通过条件：**
    1. ❌ 硬性门槛审查 结果中**没有** "**不匹配**" 项（存在薪资不匹配）。
    2. ✅ 核心职责匹配度 评估结果为 "**中**" 或 "**高**"（评估为“中”）。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人具备较强的综合人力资源管理能力，且核心职责匹配度为“中”。但由于其期望薪资（10-11K）**远高于岗位薪资上限（7K）且超过20%容忍阈值**，因此在**硬性门槛审查中被淘汰**。
2025-08-25 21:06:13 - INFO - 提取到姓名: 梁滴翠
2025-08-25 21:06:13 - INFO - 提取到年龄: 37岁
2025-08-25 21:06:13 - INFO - 提取到工作经验: 10年以上
2025-08-25 21:06:13 - INFO - 提取到学历: 本科
2025-08-25 21:06:13 - INFO - 提取到当前职位: HRBP
2025-08-25 21:06:13 - INFO - 提取到期望薪资: 10-11K
2025-08-25 21:06:13 - INFO - 从初筛报告提取的基本信息: {'name': '梁滴翠', 'age': '37岁', 'experience': '10年以上', 'education': '本科', 'current_position': 'HRBP', 'expected_salary': '10-11K'}
2025-08-25 21:06:13 - INFO - 候选人 梁滴翠 初筛报告提取完成: advantages=2. 曾在500强企业（道达尔）和上市公司（珠海博闻）担任HR岗位，具备较强的实际操作能力；3. 有从零到一搭建人力资源体系的经验，具备战略执行和组织变革推动能力。

-, risks=2. 在薪酬福利体系设计方面，缺乏员工满意度提升的具体成果描述。

---

**4. 初筛结论**

- **淘汰条件：**
    1. ✅ 硬性门槛审查 中有任意一项结果为 "**不匹配**"（薪资不匹配）。
    2. ❌ 核心职责匹配度 评估结果为 "**低**"（未达该条件）。

- **通过条件：**
    1. ❌ 硬性门槛审查 结果中**没有** "**不匹配**" 项（存在薪资不匹配）。
    2. ✅ 核心职责匹配度 评估结果为 "**中**" 或 "**高**"（评估为“中”）。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人具备较强的综合人力资源管理能力，且核心职责匹配度为“中”。但由于其期望薪资（10-11K）**远高于岗位薪资上限（7K）且超过20%容忍阈值**，因此在**硬性门槛审查中被淘汰**。, basic_info={'name': '梁滴翠', 'age': '37岁', 'experience': '10年以上', 'education': '本科', 'current_position': 'HRBP', 'expected_salary': '10-11K'}
2025-08-25 21:06:13 - INFO - ❌ 梁滴翠 未通过第1轮筛选，得分: 0.0
2025-08-25 21:06:27 - INFO - VL识别成功，识别内容长度: 1233
2025-08-25 21:06:27 - INFO - ✅ 保存VL识别结果: ./文本简历/VL_Result_人力资源主管/林小姐_VL识别结果.txt
2025-08-25 21:06:27 - INFO - 开始处理简历: 林小姐
2025-08-25 21:06:27 - INFO - 开始第1轮评估: 林小姐
2025-08-25 21:06:27 - INFO - 岗位名称: 人力资源主管
2025-08-25 21:06:27 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 21:06:27 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 21:06:48 - INFO - ✅ 第1轮评估完成: 林小姐 | 分数: 0.0
2025-08-25 21:06:48 - INFO - 检测到不合格关键词: 不匹配
2025-08-25 21:06:48 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_21：03_人力资源主管_一二点五三轮筛选结果/初筛不合格\未通过_林小姐_第1轮_评估报告.md
2025-08-25 21:06:48 - INFO - 开始提取候选人 林小姐 的初筛报告信息
2025-08-25 21:06:48 - INFO - 找到优势文本: **优势:**  
    - 具备管理岗位经验，熟悉公司运营流程，有跨部门协调能力；
    - 拥有行政管理、客户管理、公关管理等多方面能力。

- 
2025-08-25 21:06:48 - INFO - 找到风险文本: **风险与不足:**  
    - 期望薪资超出JD薪资范围上限的20%，属于**硬性门槛不匹配**；
    - 缺乏与人力资源主管岗位直接相关的职责执行证据，尤其是薪酬福利体系设计、员工生命周期管理等方面；
    - 简历中虽列出“人力资源管理”技能，但未提供具体经验支撑。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：**  
- **期望薪资超出JD上限20%（7K*1.2=8.4K，候选人期望为8-12K），构成硬性门槛不匹配**；  
- 核心职责匹配度评估为“低”，简历中缺乏与人力资源主管岗位直接相关的工作经验，尤其是薪酬福利体系设计、员工生命周期管理、组织变革支持等职责的执行证据。
2025-08-25 21:06:48 - INFO - 提取的优势和风险: 优势=- 拥有行政管理、客户管理、公关管理等多方面能力。

-, 风险=- 缺乏与人力资源主管岗位直接相关的职责执行证据，尤其是薪酬福利体系设计、员工生命周期管理等方面；- 简历中虽列出“人力资源管理”技能，但未提供具体经验支撑。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：**  
- **期望薪资超出JD上限20%（7K*1.2=8.4K，候选人期望为8-12K），构成硬性门槛不匹配**
2025-08-25 21:06:48 - INFO - 提取到姓名: 林小姐
2025-08-25 21:06:48 - INFO - 提取到年龄: 35岁
2025-08-25 21:06:48 - INFO - 提取到工作经验: 12年5个月
2025-08-25 21:06:48 - INFO - 提取到学历: 法学本科（非全日制）
2025-08-25 21:06:48 - INFO - 提取到当前职位: 总助/CEO助理/董事长助理
2025-08-25 21:06:48 - INFO - 提取到期望薪资: 8-12K
2025-08-25 21:06:48 - INFO - 从初筛报告提取的基本信息: {'name': '林小姐', 'age': '35岁', 'experience': '12年5个月', 'education': '法学本科（非全日制）', 'current_position': '总助/CEO助理/董事长助理', 'expected_salary': '8-12K'}
2025-08-25 21:06:48 - INFO - 候选人 林小姐 初筛报告提取完成: advantages=- 拥有行政管理、客户管理、公关管理等多方面能力。

-, risks=- 缺乏与人力资源主管岗位直接相关的职责执行证据，尤其是薪酬福利体系设计、员工生命周期管理等方面；- 简历中虽列出“人力资源管理”技能，但未提供具体经验支撑。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：**  
- **期望薪资超出JD上限20%（7K*1.2=8.4K，候选人期望为8-12K），构成硬性门槛不匹配**, basic_info={'name': '林小姐', 'age': '35岁', 'experience': '12年5个月', 'education': '法学本科（非全日制）', 'current_position': '总助/CEO助理/董事长助理', 'expected_salary': '8-12K'}
2025-08-25 21:06:48 - INFO - ❌ 林小姐 未通过第1轮筛选，得分: 0.0
2025-08-25 21:08:45 - INFO - VL识别成功，识别内容长度: 24811
2025-08-25 21:08:45 - INFO - ✅ 保存VL识别结果: ./文本简历/VL_Result_人力资源主管/郭女士_VL识别结果.txt
2025-08-25 21:08:45 - INFO - 开始处理简历: 郭女士
2025-08-25 21:08:45 - INFO - 开始第1轮评估: 郭女士
2025-08-25 21:08:45 - INFO - 岗位名称: 人力资源主管
2025-08-25 21:08:45 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 21:08:45 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 21:09:15 - INFO - ✅ 第1轮评估完成: 郭女士 | 分数: 0.0
2025-08-25 21:09:15 - INFO - 检测到不合格关键词: 不匹配
2025-08-25 21:09:15 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_21：03_人力资源主管_一二点五三轮筛选结果/初筛不合格\未通过_郭女士_第1轮_评估报告.md
2025-08-25 21:09:15 - INFO - 开始提取候选人 郭女士 的初筛报告信息
2025-08-25 21:09:15 - INFO - 找到优势文本: **优势:**
    - 拥有10年以上人事相关工作经验，符合岗位经验要求。
    - 拥有本科文凭，满足学历门槛。
    - 技能列表中涵盖了JD要求的多项核心技能，如招聘、绩效、员工关系、薪酬福利等。

- 
2025-08-25 21:09:15 - INFO - 找到风险文本: **风险与不足:**
    - 期望薪资超出JD上限 41.67%，超过允许浮动范围（1.2倍），判定为“不匹配”。
    - 未提供薪酬体系设计与优化的具体案例，相关职责匹配度较弱。
    - JD中未提及年龄限制，无年龄风险。

---

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人期望薪资为10K，远高于JD薪资上限7K，超出1.2倍阈值，硬性门槛审查结果为“不匹配”。尽管核心职责匹配度为“中”，但由于薪资不匹配，仍判定为淘汰。
2025-08-25 21:09:15 - INFO - 提取的优势和风险: 优势=拥有10年以上人事相关工作经验，符合岗位经验要求。；拥有本科文凭，满足学历门槛。；技能列表中涵盖了JD要求的多项核心技能，如招聘、绩效、员工关系、薪酬福利等。, 风险=期望薪资超出JD上限 41.67%，超过允许浮动范围（1.2倍），判定为“不匹配”。；未提供薪酬体系设计与优化的具体案例，相关职责匹配度较弱。
2025-08-25 21:09:15 - INFO - 提取到姓名: 郭女士
2025-08-25 21:09:15 - INFO - 提取到年龄: 35岁
2025-08-25 21:09:15 - INFO - 提取到工作经验: 10年及以上
2025-08-25 21:09:15 - INFO - 提取到学历: 本科
2025-08-25 21:09:15 - INFO - 提取到当前职位: 人事主管
2025-08-25 21:09:15 - INFO - 提取到期望薪资: 7-10K
2025-08-25 21:09:15 - INFO - 从初筛报告提取的基本信息: {'name': '郭女士', 'age': '35岁', 'experience': '10年及以上', 'education': '本科', 'current_position': '人事主管', 'expected_salary': '7-10K'}
2025-08-25 21:09:15 - INFO - 候选人 郭女士 初筛报告提取完成: advantages=拥有10年以上人事相关工作经验，符合岗位经验要求。；拥有本科文凭，满足学历门槛。；技能列表中涵盖了JD要求的多项核心技能，如招聘、绩效、员工关系、薪酬福利等。, risks=期望薪资超出JD上限 41.67%，超过允许浮动范围（1.2倍），判定为“不匹配”。；未提供薪酬体系设计与优化的具体案例，相关职责匹配度较弱。, basic_info={'name': '郭女士', 'age': '35岁', 'experience': '10年及以上', 'education': '本科', 'current_position': '人事主管', 'expected_salary': '7-10K'}
2025-08-25 21:09:15 - INFO - ❌ 郭女士 未通过第1轮筛选，得分: 0.0
2025-08-25 21:09:33 - INFO - VL识别成功，识别内容长度: 2158
2025-08-25 21:09:33 - INFO - ✅ 保存VL识别结果: ./文本简历/VL_Result_人力资源主管/王威_VL识别结果.txt
2025-08-25 21:09:33 - INFO - 开始处理简历: 王威
2025-08-25 21:09:33 - INFO - 开始第1轮评估: 王威
2025-08-25 21:09:33 - INFO - 岗位名称: 人力资源主管
2025-08-25 21:09:33 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/初筛1_自由版.md
2025-08-25 21:09:33 - INFO - 第1轮评估使用模型: qwen-plus
2025-08-25 21:10:00 - INFO - ✅ 第1轮评估完成: 王威 | 分数: 0.0
2025-08-25 21:10:00 - INFO - 检测到明确通过标识: ✅ 通过
2025-08-25 21:10:00 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_21：03_人力资源主管_一二点五三轮筛选结果/初筛合格\通过_王威_第1轮_评估报告.md
2025-08-25 21:10:00 - INFO - 开始提取候选人 王威 的初筛报告信息
2025-08-25 21:10:00 - INFO - 找到优势文本: **优势:**
    1. 拥有人力资源经理岗位经验，具备统筹管理能力；
    2. 具备员工生命周期管理全流程经验，覆盖招聘、培训、绩效、关系等；
    3. 持有中级人力资源管理师证书，具备专业资质。

- 
2025-08-25 21:10:00 - INFO - 找到风险文本: **风险与不足:**
    1. 薪资期望为“面议”，存在超出JD薪资上限（7K）的潜在风险；
    2. 薪酬福利体系优化方面的经验描述较为笼统，缺乏具体项目或成果支撑。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。 ❌
    2. 核心职责匹配度 评估结果为 "**低**"。 ❌
- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。 ✅
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。 ✅

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历满足要求，年龄未设限，虽薪资期望为“面议”，但存在潜在超限风险，需在下一轮确认。其具备人力资源主管岗位所需的核心职责经验，尤其在员工生命周期管理方面经验扎实，匹配度为“中”。综合判断可进入下一轮面试。
2025-08-25 21:10:00 - INFO - 提取的优势和风险: 优势=2. 具备员工生命周期管理全流程经验，覆盖招聘、培训、绩效、关系等；3. 持有中级人力资源管理师证书，具备专业资质。

-, 风险=2. 薪酬福利体系优化方面的经验描述较为笼统，缺乏具体项目或成果支撑。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。 ❌
    2. 核心职责匹配度 评估结果为 "**低**"。 ❌
- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。 ✅
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。 ✅

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历满足要求，年龄未设限，虽薪资期望为“面议”，但存在潜在超限风险，需在下一轮确认。其具备人力资源主管岗位所需的核心职责经验，尤其在员工生命周期管理方面经验扎实，匹配度为“中”。综合判断可进入下一轮面试。
2025-08-25 21:10:00 - INFO - 提取到姓名: 王成
2025-08-25 21:10:00 - INFO - 提取到年龄: 38岁（1986年出生，2024年评估）
2025-08-25 21:10:00 - INFO - 提取到工作经验: 5年（2019年至今）
2025-08-25 21:10:00 - INFO - 提取到学历: 本科（吉林农林大学，财务管理专业）
2025-08-25 21:10:00 - INFO - 提取到当前职位: 人力资源经理（绿地集团物业服务有限公司）
2025-08-25 21:10:00 - INFO - 提取到期望薪资: 面议
2025-08-25 21:10:00 - INFO - 从初筛报告提取的基本信息: {'name': '王成', 'age': '38岁（1986年出生，2024年评估）', 'experience': '5年（2019年至今）', 'education': '本科（吉林农林大学，财务管理专业）', 'current_position': '人力资源经理（绿地集团物业服务有限公司）', 'expected_salary': '面议'}
2025-08-25 21:10:00 - INFO - 候选人 王威 初筛报告提取完成: advantages=2. 具备员工生命周期管理全流程经验，覆盖招聘、培训、绩效、关系等；3. 持有中级人力资源管理师证书，具备专业资质。

-, risks=2. 薪酬福利体系优化方面的经验描述较为笼统，缺乏具体项目或成果支撑。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。 ❌
    2. 核心职责匹配度 评估结果为 "**低**"。 ❌
- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。 ✅
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。 ✅

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历满足要求，年龄未设限，虽薪资期望为“面议”，但存在潜在超限风险，需在下一轮确认。其具备人力资源主管岗位所需的核心职责经验，尤其在员工生命周期管理方面经验扎实，匹配度为“中”。综合判断可进入下一轮面试。, basic_info={'name': '王成', 'age': '38岁（1986年出生，2024年评估）', 'experience': '5年（2019年至今）', 'education': '本科（吉林农林大学，财务管理专业）', 'current_position': '人力资源经理（绿地集团物业服务有限公司）', 'expected_salary': '面议'}
2025-08-25 21:10:00 - INFO - 开始第2轮评估: 王威
2025-08-25 21:10:00 - INFO - 岗位名称: 人力资源主管
2025-08-25 21:10:00 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/专业画像分析.md
2025-08-25 21:10:00 - INFO - 第2轮评估使用模型: qwen-plus
2025-08-25 21:10:22 - WARNING - 分数不一致: 总分=85.0, 计算总分=0.0 (A:0.0+B:0.0+C:0.0+D:0.0+E:0.0)
2025-08-25 21:10:22 - WARNING - 候选人王威分数不一致: 总分=85.0, 计算总分=0.0
2025-08-25 21:10:22 - INFO - ✅ 第2轮评估完成: 王威 | 分数: 85.0
2025-08-25 21:10:22 - INFO - 未检测到明确关键词，默认通过
2025-08-25 21:10:22 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_21：03_人力资源主管_一二点五三轮筛选结果/画像分析结果\王威_第2轮_画像分析.md
2025-08-25 21:10:22 - INFO - 开始第2.5轮评估: 王威
2025-08-25 21:10:22 - INFO - 岗位名称: 人力资源主管
2025-08-25 21:10:22 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/专业画像匹配.md
2025-08-25 21:10:22 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/专业画像匹配.md
2025-08-25 21:10:22 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-08-25 21:10:25 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-08-25 21:10:28 - INFO - ✅ 第2.5轮评估完成: 王威 | 分数: 0.0
2025-08-25 21:10:28 - INFO - 第2.5轮检测到明确通过标识: ✅ 通过
2025-08-25 21:10:28 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_21：03_人力资源主管_一二点五三轮筛选结果/二筛合格\通过_王威_第2.5轮_评估报告.md
2025-08-25 21:10:28 - INFO - 开始第3轮评估: 王威
2025-08-25 21:10:28 - INFO - 岗位名称: 人力资源主管
2025-08-25 21:10:28 - INFO - ✅ 使用评估提示词: ./Prompt/最新准则组_8.16/终极筛选_打分规则.md
2025-08-25 21:10:28 - INFO - 第3轮评估使用模型: qwen-plus
2025-08-25 21:11:24 - INFO - ✅ 第3轮评估完成: 王威 | 分数: 90.0
2025-08-25 21:11:24 - INFO - 第3轮检测到合格关键词: 通过
2025-08-25 21:11:24 - INFO - ✅ 保存评估报告: ./user/杨锐雄/Result/8.25_21：03_人力资源主管_一二点五三轮筛选结果/最终通过\90.0分_王威_第3轮_评估报告.md
2025-08-25 21:11:24 - INFO - ✅ 王威 完成三轮筛选，最终得分: 90.0, 是否通过: True
2025-08-25 21:11:24 - INFO - 候选人 王威 最终返回信息: advantages=2. 具备员工生命周期管理全流程经验，覆盖招聘、培训、绩效、关系等；3. 持有中级人力资源管理师证书，具备专业资质。

-, risks=2. 薪酬福利体系优化方面的经验描述较为笼统，缺乏具体项目或成果支撑。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。 ❌
    2. 核心职责匹配度 评估结果为 "**低**"。 ❌
- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。 ✅
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。 ✅

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历满足要求，年龄未设限，虽薪资期望为“面议”，但存在潜在超限风险，需在下一轮确认。其具备人力资源主管岗位所需的核心职责经验，尤其在员工生命周期管理方面经验扎实，匹配度为“中”。综合判断可进入下一轮面试。, basic_info={'name': '王成', 'age': '38岁（1986年出生，2024年评估）', 'experience': '5年（2019年至今）', 'education': '本科（吉林农林大学，财务管理专业）', 'current_position': '人力资源经理（绿地集团物业服务有限公司）', 'expected_salary': '面议'}
