2025-09-01 09:05:46 - INFO - 🚀 启动智能简历评估系统
2025-09-01 09:05:46 - INFO - 📋 系统功能:
2025-09-01 09:05:46 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 09:05:46 - INFO -   - 多线程并发处理
2025-09-01 09:05:46 - INFO -   - 实时进度监控
2025-09-01 09:05:46 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 09:05:46 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 09:05:46 - INFO -  - JD来源: D:\project\智能招聘系统_version1.0.1_简化\智能招聘系统_version1.0.3_08.31\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 09:05:46 - INFO - 🔗 爬虫系统地址: http://localhost:8012
2025-09-01 09:06:02 - INFO - 🔐 统一登录请求: normal
2025-09-01 09:06:02 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 09:06:07 - INFO - 🔐 统一登录请求: boss
2025-09-01 09:06:07 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 09:06:07 - INFO - DrissionPage浏览器初始化成功
2025-09-01 09:06:22 - ERROR - 获取用户信息失败: 
与页面的连接已断开。
版本: *******
2025-09-01 09:06:22 - WARNING - ⚠️ Boss自动化失败，无法提取用户信息
2025-09-01 09:06:58 - INFO - 🔐 统一登录请求: boss
2025-09-01 09:06:58 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 09:06:59 - INFO - DrissionPage浏览器初始化成功
2025-09-01 09:07:00 - INFO - 已访问登录页面，请手动登录
2025-09-01 09:07:00 - INFO - 等待用户登录...
2025-09-01 09:07:02 - INFO - 用户已登录
2025-09-01 09:07:02 - INFO - 开始提取用户信息
2025-09-01 09:07:02 - INFO - 用户名: 杨锐雄
2025-09-01 09:07:02 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 09:07:02 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 09:07:31 - INFO - 🔐 统一登录请求: boss
2025-09-01 09:07:31 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 09:07:31 - INFO - DrissionPage浏览器初始化成功
2025-09-01 09:07:32 - INFO - 已访问登录页面，请手动登录
2025-09-01 09:07:32 - INFO - 等待用户登录...
2025-09-01 09:07:34 - INFO - 用户已登录
2025-09-01 09:07:34 - INFO - 开始提取用户信息
2025-09-01 09:07:34 - INFO - 用户名: 杨锐雄
2025-09-01 09:07:34 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 09:07:34 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 09:07:45 - INFO - 未检测到弹窗
2025-09-01 09:07:45 - INFO - 开始提取职位信息...
2025-09-01 09:07:46 - INFO - 成功进入职位管理页面
2025-09-01 09:07:47 - INFO - 已切换到iframe
2025-09-01 09:07:47 - INFO - 一共获取到3个职位
2025-09-01 09:07:47 - INFO - 开始提取第1个职位信息...
2025-09-01 09:07:47 - INFO - 第1次进入iframe
2025-09-01 09:07:47 - INFO - 职位名称: AI全栈工程师
2025-09-01 09:07:49 - INFO - 岗位信息: {'职位索引': 1, '职位名称': 'AI全栈工程师', '工作地点': '珠海', '工作经验': '1-3年', '学历要求': '本科', '薪资范围': '15-20K', '招聘类型': '全职', '职位详情': '岗位职责:\n1. 负责AI全栈工程相关工作\n2. 与团队合作，确保项目按时完成\n3. 持续优化工作流程，提升工作效率\n\n任职要求:\n1. 具备出色的沟通能力，能够有效协调项目资源\n2. 能够快速适应新环境，融入多功能团队\n3. 对AI技术有深入理解，能够不断学习和应用新知识'}
2025-09-01 09:07:49 - INFO - 第1个职位信息提取完成
2025-09-01 09:07:49 - INFO - 开始提取第2个职位信息...
2025-09-01 09:07:49 - INFO - 第2次进入iframe
2025-09-01 09:07:50 - INFO - 职位名称: AI产品经理
2025-09-01 09:07:51 - INFO - 岗位信息: {'职位索引': 2, '职位名称': 'AI产品经理', '工作地点': '珠海', '工作经验': '3-5年', '学历要求': '本科', '薪资范围': '14-20K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 负责AI技术在图帕斯户外产品中的应用规划，推动智能户外装备的创新与发展；\n2. 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平；\n3. 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行；\n4. 持续跟踪市场趋势和技术发展，为产品迭代提供科学依据；\n5. 与研发、设计、市场团队紧密合作，优化产品功能，增强用户体验。\n\n任职要求：\n1. 对AI技术及其应用场景有深刻理解，具备相关项目经验者优先；\n2. 熟悉户外用品市场，了解用户痛点和需求，能够转化为产品特性；\n3. 优秀的跨部门沟通与协调能力，能够推动多方合作达成目标；\n4. 强烈的责任心和执行力，能够在快节奏环境中解决问题；\n5. 具备良好的数据分析能力，能够通过数据驱动产品决策。'}
2025-09-01 09:07:51 - INFO - 第2个职位信息提取完成
2025-09-01 09:07:51 - INFO - 开始提取第3个职位信息...
2025-09-01 09:07:52 - INFO - 第3次进入iframe
2025-09-01 09:07:52 - INFO - 职位名称: 人力资源主管
2025-09-01 09:07:54 - INFO - 岗位信息: {'职位索引': 3, '职位名称': '人力资源主管', '工作地点': '珠海', '工作经验': '5-10年', '学历要求': '本科', '薪资范围': '6-7K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 统筹公司人力资源管理工作，确保人力资源政策的有效执行；\n2. 负责招聘、培训、绩效评估及员工关系等全员工生命周期的管理；\n3. 设计和优化薪酬福利体系，提升员工满意度和公司竞争力；\n4. 参与制定人力资源战略规划，支持公司业务发展及组织变革；\n5. 监控部门运作效率，提出改进建议，优化工作流程。\n\n任职要求：\n1. 人力资源管理或相关专业本科及以上学历；\n2. 5年以上人力资源相关工作经验，有主管或以上职位经验；\n3. 熟悉人力资源各大模块，具备优秀的人才招聘、培训及绩效管理能力；\n4. 具备良好的沟通协调能力和团队领导力，能够处理复杂的人事问题；\n5. 对人力资源法律法规有深入了解，能够为公司提供合规的人力资源解决方案。'}
2025-09-01 09:07:54 - INFO - 第3个职位信息提取完成
2025-09-01 09:08:11 - INFO - 用户 杨锐雄 删除了JD文件: AI产品经理.md
2025-09-01 09:08:14 - INFO - 🔐 统一登录请求: boss
2025-09-01 09:08:14 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 09:08:14 - INFO - DrissionPage浏览器初始化成功
2025-09-01 09:08:15 - INFO - 已访问登录页面，请手动登录
2025-09-01 09:08:15 - INFO - 等待用户登录...
2025-09-01 09:08:17 - INFO - 用户已登录
2025-09-01 09:08:17 - INFO - 开始提取用户信息
2025-09-01 09:08:17 - INFO - 用户名: 杨锐雄
2025-09-01 09:08:17 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 09:08:17 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 09:08:32 - INFO - 未检测到弹窗
2025-09-01 09:08:32 - INFO - 开始提取职位信息...
2025-09-01 09:08:32 - INFO - 成功进入职位管理页面
2025-09-01 09:08:33 - INFO - 已切换到iframe
2025-09-01 09:08:33 - INFO - 一共获取到3个职位
2025-09-01 09:08:33 - INFO - 开始提取第1个职位信息...
2025-09-01 09:08:33 - INFO - 第1次进入iframe
2025-09-01 09:08:33 - INFO - 职位名称: AI全栈工程师
2025-09-01 09:08:35 - INFO - 岗位信息: {'职位索引': 1, '职位名称': 'AI全栈工程师', '工作地点': '珠海', '工作经验': '1-3年', '学历要求': '本科', '薪资范围': '15-20K', '招聘类型': '全职', '职位详情': '岗位职责:\n1. 负责AI全栈工程相关工作\n2. 与团队合作，确保项目按时完成\n3. 持续优化工作流程，提升工作效率\n\n任职要求:\n1. 具备出色的沟通能力，能够有效协调项目资源\n2. 能够快速适应新环境，融入多功能团队\n3. 对AI技术有深入理解，能够不断学习和应用新知识'}
2025-09-01 09:08:35 - INFO - 第1个职位信息提取完成
2025-09-01 09:08:35 - INFO - 开始提取第2个职位信息...
2025-09-01 09:08:35 - INFO - 第2次进入iframe
2025-09-01 09:08:36 - INFO - 职位名称: AI产品经理
2025-09-01 09:08:38 - INFO - 岗位信息: {'职位索引': 2, '职位名称': 'AI产品经理', '工作地点': '珠海', '工作经验': '3-5年', '学历要求': '本科', '薪资范围': '14-20K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 负责AI技术在图帕斯户外产品中的应用规划，推动智能户外装备的创新与发展；\n2. 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平；\n3. 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行；\n4. 持续跟踪市场趋势和技术发展，为产品迭代提供科学依据；\n5. 与研发、设计、市场团队紧密合作，优化产品功能，增强用户体验。\n\n任职要求：\n1. 对AI技术及其应用场景有深刻理解，具备相关项目经验者优先；\n2. 熟悉户外用品市场，了解用户痛点和需求，能够转化为产品特性；\n3. 优秀的跨部门沟通与协调能力，能够推动多方合作达成目标；\n4. 强烈的责任心和执行力，能够在快节奏环境中解决问题；\n5. 具备良好的数据分析能力，能够通过数据驱动产品决策。'}
2025-09-01 09:08:38 - INFO - 第2个职位信息提取完成
2025-09-01 09:08:38 - INFO - 开始提取第3个职位信息...
2025-09-01 09:08:38 - INFO - 第3次进入iframe
2025-09-01 09:08:38 - INFO - 职位名称: 人力资源主管
2025-09-01 09:08:40 - INFO - 岗位信息: {'职位索引': 3, '职位名称': '人力资源主管', '工作地点': '珠海', '工作经验': '5-10年', '学历要求': '本科', '薪资范围': '6-7K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 统筹公司人力资源管理工作，确保人力资源政策的有效执行；\n2. 负责招聘、培训、绩效评估及员工关系等全员工生命周期的管理；\n3. 设计和优化薪酬福利体系，提升员工满意度和公司竞争力；\n4. 参与制定人力资源战略规划，支持公司业务发展及组织变革；\n5. 监控部门运作效率，提出改进建议，优化工作流程。\n\n任职要求：\n1. 人力资源管理或相关专业本科及以上学历；\n2. 5年以上人力资源相关工作经验，有主管或以上职位经验；\n3. 熟悉人力资源各大模块，具备优秀的人才招聘、培训及绩效管理能力；\n4. 具备良好的沟通协调能力和团队领导力，能够处理复杂的人事问题；\n5. 对人力资源法律法规有深入了解，能够为公司提供合规的人力资源解决方案。'}
2025-09-01 09:08:40 - INFO - 第3个职位信息提取完成
2025-09-01 09:09:34 - INFO - 🔐 统一登录请求: normal
2025-09-01 09:09:34 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 09:09:48 - INFO - 🔐 统一登录请求: normal
2025-09-01 09:09:48 - INFO - ✅ 普通用户 123131231312 登录成功
2025-09-01 09:09:57 - INFO - 🔐 统一登录请求: boss
2025-09-01 09:09:57 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 09:09:57 - INFO - DrissionPage浏览器初始化成功
2025-09-01 09:09:58 - INFO - 已访问登录页面，请手动登录
2025-09-01 09:09:58 - INFO - 等待用户登录...
2025-09-01 09:10:00 - INFO - 用户已登录
2025-09-01 09:10:00 - INFO - 开始提取用户信息
2025-09-01 09:10:00 - INFO - 用户名: 杨锐雄
2025-09-01 09:10:00 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 09:10:00 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 09:10:15 - INFO - 未检测到弹窗
2025-09-01 09:10:15 - INFO - 开始提取职位信息...
2025-09-01 09:10:15 - INFO - 成功进入职位管理页面
2025-09-01 09:10:16 - INFO - 已切换到iframe
2025-09-01 09:10:16 - INFO - 一共获取到3个职位
2025-09-01 09:10:16 - INFO - 开始提取第1个职位信息...
2025-09-01 09:10:17 - INFO - 第1次进入iframe
2025-09-01 09:10:17 - INFO - 职位名称: AI全栈工程师
2025-09-01 09:10:18 - INFO - 岗位信息: {'职位索引': 1, '职位名称': 'AI全栈工程师', '工作地点': '珠海', '工作经验': '1-3年', '学历要求': '本科', '薪资范围': '15-20K', '招聘类型': '全职', '职位详情': '岗位职责:\n1. 负责AI全栈工程相关工作\n2. 与团队合作，确保项目按时完成\n3. 持续优化工作流程，提升工作效率\n\n任职要求:\n1. 具备出色的沟通能力，能够有效协调项目资源\n2. 能够快速适应新环境，融入多功能团队\n3. 对AI技术有深入理解，能够不断学习和应用新知识'}
2025-09-01 09:10:18 - INFO - 第1个职位信息提取完成
2025-09-01 09:10:18 - INFO - 开始提取第2个职位信息...
2025-09-01 09:10:19 - INFO - 第2次进入iframe
2025-09-01 09:10:19 - INFO - 职位名称: AI产品经理
2025-09-01 09:10:21 - INFO - 岗位信息: {'职位索引': 2, '职位名称': 'AI产品经理', '工作地点': '珠海', '工作经验': '3-5年', '学历要求': '本科', '薪资范围': '14-20K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 负责AI技术在图帕斯户外产品中的应用规划，推动智能户外装备的创新与发展；\n2. 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平；\n3. 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行；\n4. 持续跟踪市场趋势和技术发展，为产品迭代提供科学依据；\n5. 与研发、设计、市场团队紧密合作，优化产品功能，增强用户体验。\n\n任职要求：\n1. 对AI技术及其应用场景有深刻理解，具备相关项目经验者优先；\n2. 熟悉户外用品市场，了解用户痛点和需求，能够转化为产品特性；\n3. 优秀的跨部门沟通与协调能力，能够推动多方合作达成目标；\n4. 强烈的责任心和执行力，能够在快节奏环境中解决问题；\n5. 具备良好的数据分析能力，能够通过数据驱动产品决策。'}
2025-09-01 09:10:21 - INFO - 第2个职位信息提取完成
2025-09-01 09:10:21 - INFO - 开始提取第3个职位信息...
2025-09-01 09:10:21 - INFO - 第3次进入iframe
2025-09-01 09:10:22 - INFO - 职位名称: 人力资源主管
2025-09-01 09:10:24 - INFO - 岗位信息: {'职位索引': 3, '职位名称': '人力资源主管', '工作地点': '珠海', '工作经验': '5-10年', '学历要求': '本科', '薪资范围': '6-7K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 统筹公司人力资源管理工作，确保人力资源政策的有效执行；\n2. 负责招聘、培训、绩效评估及员工关系等全员工生命周期的管理；\n3. 设计和优化薪酬福利体系，提升员工满意度和公司竞争力；\n4. 参与制定人力资源战略规划，支持公司业务发展及组织变革；\n5. 监控部门运作效率，提出改进建议，优化工作流程。\n\n任职要求：\n1. 人力资源管理或相关专业本科及以上学历；\n2. 5年以上人力资源相关工作经验，有主管或以上职位经验；\n3. 熟悉人力资源各大模块，具备优秀的人才招聘、培训及绩效管理能力；\n4. 具备良好的沟通协调能力和团队领导力，能够处理复杂的人事问题；\n5. 对人力资源法律法规有深入了解，能够为公司提供合规的人力资源解决方案。'}
2025-09-01 09:10:24 - INFO - 第3个职位信息提取完成
2025-09-01 09:10:39 - INFO - 未检测到弹窗
2025-09-01 09:10:39 - INFO - 开始提取职位信息...
2025-09-01 09:10:39 - INFO - 成功进入职位管理页面
2025-09-01 09:10:40 - INFO - 已切换到iframe
2025-09-01 09:10:40 - INFO - 一共获取到3个职位
2025-09-01 09:10:40 - INFO - 开始提取第1个职位信息...
2025-09-01 09:10:41 - INFO - 第1次进入iframe
2025-09-01 09:10:41 - INFO - 职位名称: AI全栈工程师
2025-09-01 09:10:42 - INFO - 岗位信息: {'职位索引': 1, '职位名称': 'AI全栈工程师', '工作地点': '珠海', '工作经验': '1-3年', '学历要求': '本科', '薪资范围': '15-20K', '招聘类型': '全职', '职位详情': '岗位职责:\n1. 负责AI全栈工程相关工作\n2. 与团队合作，确保项目按时完成\n3. 持续优化工作流程，提升工作效率\n\n任职要求:\n1. 具备出色的沟通能力，能够有效协调项目资源\n2. 能够快速适应新环境，融入多功能团队\n3. 对AI技术有深入理解，能够不断学习和应用新知识'}
2025-09-01 09:10:42 - INFO - 第1个职位信息提取完成
2025-09-01 09:10:42 - INFO - 开始提取第2个职位信息...
2025-09-01 09:10:43 - INFO - 第2次进入iframe
2025-09-01 09:10:43 - INFO - 职位名称: AI产品经理
2025-09-01 09:10:45 - INFO - 岗位信息: {'职位索引': 2, '职位名称': 'AI产品经理', '工作地点': '珠海', '工作经验': '3-5年', '学历要求': '本科', '薪资范围': '14-20K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 负责AI技术在图帕斯户外产品中的应用规划，推动智能户外装备的创新与发展；\n2. 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平；\n3. 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行；\n4. 持续跟踪市场趋势和技术发展，为产品迭代提供科学依据；\n5. 与研发、设计、市场团队紧密合作，优化产品功能，增强用户体验。\n\n任职要求：\n1. 对AI技术及其应用场景有深刻理解，具备相关项目经验者优先；\n2. 熟悉户外用品市场，了解用户痛点和需求，能够转化为产品特性；\n3. 优秀的跨部门沟通与协调能力，能够推动多方合作达成目标；\n4. 强烈的责任心和执行力，能够在快节奏环境中解决问题；\n5. 具备良好的数据分析能力，能够通过数据驱动产品决策。'}
2025-09-01 09:10:45 - INFO - 第2个职位信息提取完成
2025-09-01 09:10:45 - INFO - 开始提取第3个职位信息...
2025-09-01 09:10:45 - INFO - 第3次进入iframe
2025-09-01 09:10:46 - INFO - 职位名称: 人力资源主管
2025-09-01 09:10:47 - INFO - 岗位信息: {'职位索引': 3, '职位名称': '人力资源主管', '工作地点': '珠海', '工作经验': '5-10年', '学历要求': '本科', '薪资范围': '6-7K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 统筹公司人力资源管理工作，确保人力资源政策的有效执行；\n2. 负责招聘、培训、绩效评估及员工关系等全员工生命周期的管理；\n3. 设计和优化薪酬福利体系，提升员工满意度和公司竞争力；\n4. 参与制定人力资源战略规划，支持公司业务发展及组织变革；\n5. 监控部门运作效率，提出改进建议，优化工作流程。\n\n任职要求：\n1. 人力资源管理或相关专业本科及以上学历；\n2. 5年以上人力资源相关工作经验，有主管或以上职位经验；\n3. 熟悉人力资源各大模块，具备优秀的人才招聘、培训及绩效管理能力；\n4. 具备良好的沟通协调能力和团队领导力，能够处理复杂的人事问题；\n5. 对人力资源法律法规有深入了解，能够为公司提供合规的人力资源解决方案。'}
2025-09-01 09:10:47 - INFO - 第3个职位信息提取完成
2025-09-01 09:10:54 - INFO - 🔐 统一登录请求: boss
2025-09-01 09:10:54 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 09:10:54 - INFO - DrissionPage浏览器初始化成功
2025-09-01 09:10:54 - INFO - 已访问登录页面，请手动登录
2025-09-01 09:10:54 - INFO - 等待用户登录...
2025-09-01 09:10:56 - INFO - 用户已登录
2025-09-01 09:10:56 - INFO - 开始提取用户信息
2025-09-01 09:10:56 - INFO - 用户名: 杨锐雄
2025-09-01 09:10:56 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 09:10:56 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 09:11:18 - INFO - 🔐 统一登录请求: boss
2025-09-01 09:11:18 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 09:11:18 - INFO - DrissionPage浏览器初始化成功
2025-09-01 09:11:18 - INFO - 已访问登录页面，请手动登录
2025-09-01 09:11:18 - INFO - 等待用户登录...
2025-09-01 09:11:20 - INFO - 用户已登录
2025-09-01 09:11:20 - INFO - 开始提取用户信息
2025-09-01 09:11:20 - INFO - 用户名: 杨锐雄
2025-09-01 09:11:20 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 09:11:20 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 09:11:31 - INFO - 未检测到弹窗
2025-09-01 09:11:31 - INFO - 开始提取职位信息...
2025-09-01 09:11:32 - INFO - 成功进入职位管理页面
2025-09-01 09:11:33 - INFO - 已切换到iframe
2025-09-01 09:11:33 - INFO - 一共获取到3个职位
2025-09-01 09:11:33 - INFO - 开始提取第1个职位信息...
2025-09-01 09:11:33 - INFO - 第1次进入iframe
2025-09-01 09:11:33 - INFO - 职位名称: AI全栈工程师
2025-09-01 09:11:35 - INFO - 岗位信息: {'职位索引': 1, '职位名称': 'AI全栈工程师', '工作地点': '珠海', '工作经验': '1-3年', '学历要求': '本科', '薪资范围': '15-20K', '招聘类型': '全职', '职位详情': '岗位职责:\n1. 负责AI全栈工程相关工作\n2. 与团队合作，确保项目按时完成\n3. 持续优化工作流程，提升工作效率\n\n任职要求:\n1. 具备出色的沟通能力，能够有效协调项目资源\n2. 能够快速适应新环境，融入多功能团队\n3. 对AI技术有深入理解，能够不断学习和应用新知识'}
2025-09-01 09:11:35 - INFO - 第1个职位信息提取完成
2025-09-01 09:11:35 - INFO - 开始提取第2个职位信息...
2025-09-01 09:11:35 - INFO - 第2次进入iframe
2025-09-01 09:11:36 - INFO - 职位名称: AI产品经理
2025-09-01 09:11:37 - INFO - 岗位信息: {'职位索引': 2, '职位名称': 'AI产品经理', '工作地点': '珠海', '工作经验': '3-5年', '学历要求': '本科', '薪资范围': '14-20K', '招聘类型': '全职', '职位详情': ''}
2025-09-01 09:11:37 - INFO - 第2个职位信息提取完成
2025-09-01 09:11:37 - INFO - 开始提取第3个职位信息...
2025-09-01 09:11:38 - INFO - 第3次进入iframe
2025-09-01 09:11:38 - INFO - 职位名称: 人力资源主管
2025-09-01 09:11:40 - INFO - 岗位信息: {'职位索引': 3, '职位名称': '人力资源主管', '工作地点': '珠海', '工作经验': '5-10年', '学历要求': '本科', '薪资范围': '6-7K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 统筹公司人力资源管理工作，确保人力资源政策的有效执行；\n2. 负责招聘、培训、绩效评估及员工关系等全员工生命周期的管理；\n3. 设计和优化薪酬福利体系，提升员工满意度和公司竞争力；\n4. 参与制定人力资源战略规划，支持公司业务发展及组织变革；\n5. 监控部门运作效率，提出改进建议，优化工作流程。\n\n任职要求：\n1. 人力资源管理或相关专业本科及以上学历；\n2. 5年以上人力资源相关工作经验，有主管或以上职位经验；\n3. 熟悉人力资源各大模块，具备优秀的人才招聘、培训及绩效管理能力；\n4. 具备良好的沟通协调能力和团队领导力，能够处理复杂的人事问题；\n5. 对人力资源法律法规有深入了解，能够为公司提供合规的人力资源解决方案。'}
2025-09-01 09:11:40 - INFO - 第3个职位信息提取完成
2025-09-01 09:45:35 - INFO - 🚀 启动智能简历评估系统
2025-09-01 09:45:35 - INFO - 📋 系统功能:
2025-09-01 09:45:35 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 09:45:35 - INFO -   - 多线程并发处理
2025-09-01 09:45:35 - INFO -   - 实时进度监控
2025-09-01 09:45:35 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 09:45:35 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 09:45:35 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 09:45:35 - INFO - 🔗 爬虫系统地址: http://localhost:8012
2025-09-01 09:46:35 - INFO - 🔐 统一登录请求: normal
2025-09-01 09:46:35 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 10:02:10 - INFO - 🚀 启动智能简历评估系统
2025-09-01 10:02:10 - INFO - 📋 系统功能:
2025-09-01 10:02:10 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 10:02:10 - INFO -   - 多线程并发处理
2025-09-01 10:02:10 - INFO -   - 实时进度监控
2025-09-01 10:02:10 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 10:02:10 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 10:02:10 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 10:02:10 - INFO - 🔗 爬虫系统地址: http://localhost:8012
2025-09-01 10:02:16 - INFO - 🔐 统一登录请求: normal
2025-09-01 10:02:16 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 10:02:21 - INFO - 爬取任务已启动: 06a15c6c-4e63-419d-bd9a-03e250ba7ea7, 职位: 人力资源主管
2025-09-01 10:02:21 - INFO - 🚀 开始调用爬虫程序: 06a15c6c-4e63-419d-bd9a-03e250ba7ea7, 职位: 人力资源主管, 目标数量: 20
2025-09-01 10:02:21 - INFO - 🔍 检查岗位匹配...
2025-09-01 10:02:24 - ERROR - 岗位匹配失败: Cannot connect to host localhost:8000 ssl:default [Multiple exceptions: [Errno 10061] Connect call failed ('::1', 8000, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 8000)]
2025-09-01 10:02:24 - ERROR - 调用爬虫程序失败: 岗位匹配失败: 岗位匹配失败: Cannot connect to host localhost:8000 ssl:default [Multiple exceptions: [Errno 10061] Connect call failed ('::1', 8000, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 8000)]
2025-09-01 10:04:33 - INFO - 🚀 启动智能简历评估系统
2025-09-01 10:04:33 - INFO - 📋 系统功能:
2025-09-01 10:04:33 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 10:04:33 - INFO -   - 多线程并发处理
2025-09-01 10:04:33 - INFO -   - 实时进度监控
2025-09-01 10:04:33 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 10:04:33 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 10:04:33 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 10:04:33 - INFO - 🔗 爬虫系统地址: http://localhost:8012
2025-09-01 10:04:38 - INFO - 🔐 统一登录请求: normal
2025-09-01 10:04:38 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 10:04:42 - INFO - 爬取任务已启动: c3cb3153-6b50-4098-9640-96a2fdcf012e, 职位: 人力资源主管
2025-09-01 10:04:42 - INFO - 🚀 开始调用爬虫程序: c3cb3153-6b50-4098-9640-96a2fdcf012e, 职位: 人力资源主管, 目标数量: 5
2025-09-01 10:04:42 - INFO - 🔍 检查岗位匹配...
2025-09-01 10:04:45 - ERROR - 岗位匹配失败: Cannot connect to host localhost:8000 ssl:default [Multiple exceptions: [Errno 10061] Connect call failed ('::1', 8000, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 8000)]
2025-09-01 10:04:45 - ERROR - 调用爬虫程序失败: 岗位匹配失败: 岗位匹配失败: Cannot connect to host localhost:8000 ssl:default [Multiple exceptions: [Errno 10061] Connect call failed ('::1', 8000, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 8000)]
2025-09-01 10:05:30 - INFO - 爬取任务已启动: 1491661e-5b3a-4e79-a7ec-c4044514d6ae, 职位: 人力资源主管
2025-09-01 10:05:30 - INFO - 🚀 开始调用爬虫程序: 1491661e-5b3a-4e79-a7ec-c4044514d6ae, 职位: 人力资源主管, 目标数量: 5
2025-09-01 10:05:30 - INFO - 🔍 检查岗位匹配...
2025-09-01 10:05:32 - ERROR - 岗位匹配失败: Cannot connect to host localhost:8000 ssl:default [Multiple exceptions: [Errno 10061] Connect call failed ('::1', 8000, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 8000)]
2025-09-01 10:05:32 - ERROR - 调用爬虫程序失败: 岗位匹配失败: 岗位匹配失败: Cannot connect to host localhost:8000 ssl:default [Multiple exceptions: [Errno 10061] Connect call failed ('::1', 8000, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 8000)]
2025-09-01 10:09:53 - INFO - 🚀 启动智能简历评估系统
2025-09-01 10:09:53 - INFO - 📋 系统功能:
2025-09-01 10:09:53 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 10:09:53 - INFO -   - 多线程并发处理
2025-09-01 10:09:53 - INFO -   - 实时进度监控
2025-09-01 10:09:53 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 10:09:53 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 10:09:53 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 10:09:53 - INFO - 🔗 爬虫系统地址: http://localhost:8012
2025-09-01 10:11:05 - INFO - 🔐 统一登录请求: normal
2025-09-01 10:11:05 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 10:11:07 - INFO - 爬取任务已启动: dec3aa50-f476-49b3-997c-f2be86686a92, 职位: 人力资源主管
2025-09-01 10:11:07 - INFO - 🚀 开始调用爬虫程序: dec3aa50-f476-49b3-997c-f2be86686a92, 职位: 人力资源主管, 目标数量: 20
2025-09-01 10:11:07 - INFO - 🔍 检查岗位匹配...
2025-09-01 10:11:10 - ERROR - 岗位匹配失败: Cannot connect to host localhost:8000 ssl:default [Multiple exceptions: [Errno 10061] Connect call failed ('::1', 8000, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 8000)]
2025-09-01 10:11:10 - ERROR - 调用爬虫程序失败: 岗位匹配失败: 岗位匹配失败: Cannot connect to host localhost:8000 ssl:default [Multiple exceptions: [Errno 10061] Connect call failed ('::1', 8000, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 8000)]
2025-09-01 10:11:30 - INFO - 爬取任务已启动: 68959dcf-7c19-4407-8801-d2c70405e175, 职位: 人力资源主管
2025-09-01 10:11:30 - INFO - 🚀 开始调用爬虫程序: 68959dcf-7c19-4407-8801-d2c70405e175, 职位: 人力资源主管, 目标数量: 20
2025-09-01 10:11:30 - INFO - 🔍 检查岗位匹配...
2025-09-01 10:11:33 - ERROR - 岗位匹配失败: Cannot connect to host localhost:8000 ssl:default [Multiple exceptions: [Errno 10061] Connect call failed ('::1', 8000, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 8000)]
2025-09-01 10:11:33 - ERROR - 调用爬虫程序失败: 岗位匹配失败: 岗位匹配失败: Cannot connect to host localhost:8000 ssl:default [Multiple exceptions: [Errno 10061] Connect call failed ('::1', 8000, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 8000)]
2025-09-01 10:13:26 - INFO - 🚀 启动智能简历评估系统
2025-09-01 10:13:26 - INFO - 📋 系统功能:
2025-09-01 10:13:26 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 10:13:26 - INFO -   - 多线程并发处理
2025-09-01 10:13:26 - INFO -   - 实时进度监控
2025-09-01 10:13:26 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 10:13:26 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 10:13:26 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 10:13:26 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 10:27:14 - INFO - 🚀 启动智能简历评估系统
2025-09-01 10:27:14 - INFO - 📋 系统功能:
2025-09-01 10:27:14 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 10:27:14 - INFO -   - 多线程并发处理
2025-09-01 10:27:14 - INFO -   - 实时进度监控
2025-09-01 10:27:14 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 10:27:14 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 10:27:14 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 10:27:14 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 10:29:02 - INFO - 🚀 启动智能简历评估系统
2025-09-01 10:29:02 - INFO - 📋 系统功能:
2025-09-01 10:29:02 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 10:29:02 - INFO -   - 多线程并发处理
2025-09-01 10:29:02 - INFO -   - 实时进度监控
2025-09-01 10:29:02 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 10:29:02 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 10:29:02 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 10:29:02 - INFO - 🔗 爬虫系统地址: http://localhost:8012
2025-09-01 10:29:02 - INFO - 任务管理已初始化
2025-09-01 10:29:10 - INFO - 🚀 启动智能简历评估系统
2025-09-01 10:29:10 - INFO - 📋 系统功能:
2025-09-01 10:29:10 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 10:29:10 - INFO -   - 多线程并发处理
2025-09-01 10:29:10 - INFO -   - 实时进度监控
2025-09-01 10:29:10 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 10:29:10 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 10:29:10 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 10:29:10 - INFO - 🔗 爬虫系统地址: http://localhost:8012
2025-09-01 10:29:10 - INFO - 任务管理已初始化
2025-09-01 10:32:58 - INFO - 🚀 启动智能简历评估系统
2025-09-01 10:32:58 - INFO - 📋 系统功能:
2025-09-01 10:32:58 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 10:32:58 - INFO -   - 多线程并发处理
2025-09-01 10:32:58 - INFO -   - 实时进度监控
2025-09-01 10:32:58 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 10:32:58 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 10:32:58 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 10:32:58 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 10:33:02 - INFO - 🔐 统一登录请求: normal
2025-09-01 10:33:02 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 10:33:03 - INFO - 爬取任务已启动: 3e1fbf05-c531-4cf7-b55b-e76621452042, 职位: 人力资源主管
2025-09-01 10:33:03 - INFO - 🚀 开始调用爬虫程序: 3e1fbf05-c531-4cf7-b55b-e76621452042, 职位: 人力资源主管, 目标数量: 20
2025-09-01 10:33:03 - INFO - 🔍 检查岗位匹配...
2025-09-01 10:33:23 - INFO - 岗位匹配结果: {'success': False, 'message': '未找到岗位推荐信息，请确保页面已正确加载'}
2025-09-01 10:33:23 - ERROR - 调用爬虫程序失败: 岗位匹配失败: 未找到岗位推荐信息，请确保页面已正确加载
2025-09-01 10:34:25 - INFO - 🔐 统一登录请求: boss
2025-09-01 10:34:25 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 10:34:25 - INFO - DrissionPage浏览器初始化成功
2025-09-01 10:34:25 - INFO - 已访问登录页面，请手动登录
2025-09-01 10:34:25 - INFO - 等待用户登录...
2025-09-01 10:34:27 - INFO - 用户已登录
2025-09-01 10:34:27 - INFO - 开始提取用户信息
2025-09-01 10:34:27 - INFO - 用户名: 杨锐雄
2025-09-01 10:34:27 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 10:34:27 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 10:34:34 - INFO - 爬取任务已启动: c2cf54ae-d965-4f33-813c-1ba5b8caf497, 职位: AI全栈工程师
2025-09-01 10:34:34 - INFO - 🚀 开始调用爬虫程序: c2cf54ae-d965-4f33-813c-1ba5b8caf497, 职位: AI全栈工程师, 目标数量: 20
2025-09-01 10:34:34 - INFO - 🔍 检查岗位匹配...
2025-09-01 10:34:42 - INFO - 岗位匹配结果: {'success': True, 'matched': True, 'current_job': 'AI全栈工程师', 'recommendation': 'AI全栈工程师 _ 珠海 15-20K', 'match_reason': '完全匹配', 'message': '岗位匹配成功！当前推荐: AI全栈工程师 _ 珠海 15-20K'}
2025-09-01 10:34:42 - INFO - ✅ 岗位匹配成功，开始爬取简历...
2025-09-01 10:34:42 - INFO - 爬取任务启动结果: {'task_id': 'task_1', 'message': '收集任务已启动'}
2025-09-01 10:34:42 - INFO - ✅ 爬取任务已启动: task_1
2025-09-01 10:34:42 - INFO - 开始监控爬取进度: task_1
2025-09-01 10:34:52 - ERROR - 监控爬取进度异常: 
2025-09-01 10:35:13 - ERROR - 监控爬取进度异常: 
2025-09-01 10:35:34 - ERROR - 监控爬取进度异常: 
2025-09-01 10:35:47 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:35:59 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:36:11 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:36:27 - ERROR - 监控爬取进度异常: 
2025-09-01 10:36:45 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:36:57 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:37:13 - ERROR - 监控爬取进度异常: 
2025-09-01 10:37:31 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:37:43 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:37:59 - ERROR - 监控爬取进度异常: 
2025-09-01 10:38:17 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:38:29 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:38:45 - ERROR - 监控爬取进度异常: 
2025-09-01 10:39:04 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:39:16 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:39:31 - ERROR - 监控爬取进度异常: 
2025-09-01 10:39:50 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:40:02 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:40:17 - ERROR - 监控爬取进度异常: 
2025-09-01 10:40:36 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:40:48 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:41:03 - ERROR - 监控爬取进度异常: 
2025-09-01 10:41:22 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:41:34 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:41:49 - ERROR - 监控爬取进度异常: 
2025-09-01 10:42:08 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:42:20 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:42:35 - ERROR - 监控爬取进度异常: 
2025-09-01 10:42:54 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:43:06 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:43:22 - ERROR - 监控爬取进度异常: 
2025-09-01 10:43:40 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:43:52 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:44:08 - ERROR - 监控爬取进度异常: 
2025-09-01 10:44:26 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:44:38 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:44:54 - ERROR - 监控爬取进度异常: 
2025-09-01 10:45:12 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:45:24 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:45:40 - ERROR - 监控爬取进度异常: 
2025-09-01 10:45:58 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:46:10 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:46:26 - ERROR - 监控爬取进度异常: 
2025-09-01 10:49:01 - INFO - 🚀 启动智能简历评估系统
2025-09-01 10:49:01 - INFO - 📋 系统功能:
2025-09-01 10:49:01 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 10:49:01 - INFO -   - 多线程并发处理
2025-09-01 10:49:01 - INFO -   - 实时进度监控
2025-09-01 10:49:01 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 10:49:01 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 10:49:01 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 10:49:01 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 10:49:08 - INFO - 🔐 统一登录请求: boss
2025-09-01 10:49:08 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 10:49:09 - INFO - DrissionPage浏览器初始化成功
2025-09-01 10:49:09 - INFO - 已访问登录页面，请手动登录
2025-09-01 10:49:09 - INFO - 等待用户登录...
2025-09-01 10:49:11 - INFO - 用户已登录
2025-09-01 10:49:11 - INFO - 开始提取用户信息
2025-09-01 10:49:11 - INFO - 用户名: 杨锐雄
2025-09-01 10:49:11 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 10:49:11 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 10:49:17 - INFO - 爬取任务已启动: 605df213-de8f-46e8-8356-3f162b2832de, 职位: AI产品经理
2025-09-01 10:49:17 - INFO - 🚀 开始调用爬虫程序: 605df213-de8f-46e8-8356-3f162b2832de, 职位: AI产品经理, 目标数量: 20
2025-09-01 10:49:17 - INFO - 🔍 检查岗位匹配...
2025-09-01 10:49:43 - INFO - 岗位匹配结果: {'success': True, 'matched': True, 'current_job': 'AI产品经理', 'message': '成功切换到岗位: AI产品经理'}
2025-09-01 10:49:43 - INFO - ✅ 岗位匹配成功，开始爬取简历...
2025-09-01 10:49:44 - INFO - 爬取任务启动结果: {'task_id': 'task_1', 'message': '收集任务已启动'}
2025-09-01 10:49:44 - INFO - ✅ 爬取任务已启动: task_1
2025-09-01 10:49:44 - INFO - 开始监控爬取进度: task_1
2025-09-01 10:49:54 - ERROR - 监控爬取进度异常: 
2025-09-01 10:50:15 - ERROR - 监控爬取进度异常: 
2025-09-01 10:50:26 - INFO - 🔐 统一登录请求: boss
2025-09-01 10:50:26 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 10:50:26 - INFO - DrissionPage浏览器初始化成功
2025-09-01 10:50:27 - INFO - 已访问登录页面，请手动登录
2025-09-01 10:50:27 - INFO - 等待用户登录...
2025-09-01 10:50:37 - INFO - 用户已登录
2025-09-01 10:50:37 - INFO - 开始提取用户信息
2025-09-01 10:50:37 - INFO - 用户名: 杨锐雄
2025-09-01 10:50:37 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 10:50:37 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 10:50:37 - ERROR - 监控爬取进度异常: 
2025-09-01 10:50:47 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 12, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.0, 'candidate_name': '刘俊锋'}, {'filename': '姚博_AI产品经理_20250827_161710.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250827_161710.png', 'size': 257381, 'created_time': 1756282632.0, 'candidate_name': '姚博'}, {'filename': '林丽丽_AI产品经理_20250827_161811.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林丽丽_AI产品经理_20250827_161811.png', 'size': 217050, 'created_time': 1756282692.0, 'candidate_name': '林丽丽'}, {'filename': '杨志海_AI产品经理_20250827_161912.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\杨志海_AI产品经理_20250827_161912.png', 'size': 179759, 'created_time': 1756282754.0, 'candidate_name': '杨志海'}, {'filename': '丁嘉亮_AI产品经理_20250827_162012.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丁嘉亮_AI产品经理_20250827_162012.png', 'size': 212004, 'created_time': 1756282814.0, 'candidate_name': '丁嘉亮'}, {'filename': '潘玉玲_AI产品经理_20250827_162151.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\潘玉玲_AI产品经理_20250827_162151.png', 'size': 1196651, 'created_time': 1756282912.0, 'candidate_name': '潘玉玲'}, {'filename': '丘伟航_AI产品经理_20250827_162258.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丘伟航_AI产品经理_20250827_162258.png', 'size': 363723, 'created_time': 1756282980.0, 'candidate_name': '丘伟航'}, {'filename': '谭文杰_AI产品经理_20250827_162357.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\谭文杰_AI产品经理_20250827_162357.png', 'size': 173111, 'created_time': 1756283040.0, 'candidate_name': '谭文杰'}, {'filename': '叶利广_AI产品经理_20250827_162457.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\叶利广_AI产品经理_20250827_162457.png', 'size': 181559, 'created_time': 1756283098.0, 'candidate_name': '叶利广'}, {'filename': '张轩颖_AI产品经理_20250827_162556.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\张轩颖_AI产品经理_20250827_162556.png', 'size': 191507, 'created_time': 1756283158.0, 'candidate_name': '张轩颖'}, {'filename': '林先生_AI产品经理_20250827_162700.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林先生_AI产品经理_20250827_162700.png', 'size': 278584, 'created_time': 1756283222.0, 'candidate_name': '林先生'}, {'filename': '姚博_AI产品经理_20250901_105002.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250901_105002.png', 'size': 81500, 'created_time': 1756695002.581857, 'candidate_name': '姚博'}]}
2025-09-01 10:50:52 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 12, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.0, 'candidate_name': '刘俊锋'}, {'filename': '姚博_AI产品经理_20250827_161710.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250827_161710.png', 'size': 257381, 'created_time': 1756282632.0, 'candidate_name': '姚博'}, {'filename': '林丽丽_AI产品经理_20250827_161811.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林丽丽_AI产品经理_20250827_161811.png', 'size': 217050, 'created_time': 1756282692.0, 'candidate_name': '林丽丽'}, {'filename': '杨志海_AI产品经理_20250827_161912.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\杨志海_AI产品经理_20250827_161912.png', 'size': 179759, 'created_time': 1756282754.0, 'candidate_name': '杨志海'}, {'filename': '丁嘉亮_AI产品经理_20250827_162012.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丁嘉亮_AI产品经理_20250827_162012.png', 'size': 212004, 'created_time': 1756282814.0, 'candidate_name': '丁嘉亮'}, {'filename': '潘玉玲_AI产品经理_20250827_162151.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\潘玉玲_AI产品经理_20250827_162151.png', 'size': 1196651, 'created_time': 1756282912.0, 'candidate_name': '潘玉玲'}, {'filename': '丘伟航_AI产品经理_20250827_162258.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丘伟航_AI产品经理_20250827_162258.png', 'size': 363723, 'created_time': 1756282980.0, 'candidate_name': '丘伟航'}, {'filename': '谭文杰_AI产品经理_20250827_162357.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\谭文杰_AI产品经理_20250827_162357.png', 'size': 173111, 'created_time': 1756283040.0, 'candidate_name': '谭文杰'}, {'filename': '叶利广_AI产品经理_20250827_162457.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\叶利广_AI产品经理_20250827_162457.png', 'size': 181559, 'created_time': 1756283098.0, 'candidate_name': '叶利广'}, {'filename': '张轩颖_AI产品经理_20250827_162556.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\张轩颖_AI产品经理_20250827_162556.png', 'size': 191507, 'created_time': 1756283158.0, 'candidate_name': '张轩颖'}, {'filename': '林先生_AI产品经理_20250827_162700.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林先生_AI产品经理_20250827_162700.png', 'size': 278584, 'created_time': 1756283222.0, 'candidate_name': '林先生'}, {'filename': '姚博_AI产品经理_20250901_105002.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250901_105002.png', 'size': 81500, 'created_time': 1756695002.581857, 'candidate_name': '姚博'}]}
2025-09-01 10:50:58 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 12, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.0, 'candidate_name': '刘俊锋'}, {'filename': '姚博_AI产品经理_20250827_161710.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250827_161710.png', 'size': 257381, 'created_time': 1756282632.0, 'candidate_name': '姚博'}, {'filename': '林丽丽_AI产品经理_20250827_161811.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林丽丽_AI产品经理_20250827_161811.png', 'size': 217050, 'created_time': 1756282692.0, 'candidate_name': '林丽丽'}, {'filename': '杨志海_AI产品经理_20250827_161912.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\杨志海_AI产品经理_20250827_161912.png', 'size': 179759, 'created_time': 1756282754.0, 'candidate_name': '杨志海'}, {'filename': '丁嘉亮_AI产品经理_20250827_162012.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丁嘉亮_AI产品经理_20250827_162012.png', 'size': 212004, 'created_time': 1756282814.0, 'candidate_name': '丁嘉亮'}, {'filename': '潘玉玲_AI产品经理_20250827_162151.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\潘玉玲_AI产品经理_20250827_162151.png', 'size': 1196651, 'created_time': 1756282912.0, 'candidate_name': '潘玉玲'}, {'filename': '丘伟航_AI产品经理_20250827_162258.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丘伟航_AI产品经理_20250827_162258.png', 'size': 363723, 'created_time': 1756282980.0, 'candidate_name': '丘伟航'}, {'filename': '谭文杰_AI产品经理_20250827_162357.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\谭文杰_AI产品经理_20250827_162357.png', 'size': 173111, 'created_time': 1756283040.0, 'candidate_name': '谭文杰'}, {'filename': '叶利广_AI产品经理_20250827_162457.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\叶利广_AI产品经理_20250827_162457.png', 'size': 181559, 'created_time': 1756283098.0, 'candidate_name': '叶利广'}, {'filename': '张轩颖_AI产品经理_20250827_162556.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\张轩颖_AI产品经理_20250827_162556.png', 'size': 191507, 'created_time': 1756283158.0, 'candidate_name': '张轩颖'}, {'filename': '林先生_AI产品经理_20250827_162700.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林先生_AI产品经理_20250827_162700.png', 'size': 278584, 'created_time': 1756283222.0, 'candidate_name': '林先生'}, {'filename': '姚博_AI产品经理_20250901_105002.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250901_105002.png', 'size': 81500, 'created_time': 1756695002.581857, 'candidate_name': '姚博'}]}
2025-09-01 10:51:02 - INFO - 爬取任务已启动: f1df43c2-c2b0-4888-a3dc-97c7bb9795d7, 职位: 人力资源主管
2025-09-01 10:51:02 - INFO - 🚀 开始调用爬虫程序: f1df43c2-c2b0-4888-a3dc-97c7bb9795d7, 职位: 人力资源主管, 目标数量: 5
2025-09-01 10:51:02 - INFO - 🔍 检查岗位匹配...
2025-09-01 10:51:14 - ERROR - 监控爬取进度异常: 
2025-09-01 10:51:29 - INFO - 岗位匹配结果: {'success': True, 'matched': True, 'current_job': '人力资源主管', 'message': '成功切换到岗位: 人力资源主管'}
2025-09-01 10:51:29 - INFO - ✅ 岗位匹配成功，开始爬取简历...
2025-09-01 10:51:30 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 12, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.0, 'candidate_name': '刘俊锋'}, {'filename': '姚博_AI产品经理_20250827_161710.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250827_161710.png', 'size': 257381, 'created_time': 1756282632.0, 'candidate_name': '姚博'}, {'filename': '林丽丽_AI产品经理_20250827_161811.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林丽丽_AI产品经理_20250827_161811.png', 'size': 217050, 'created_time': 1756282692.0, 'candidate_name': '林丽丽'}, {'filename': '杨志海_AI产品经理_20250827_161912.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\杨志海_AI产品经理_20250827_161912.png', 'size': 179759, 'created_time': 1756282754.0, 'candidate_name': '杨志海'}, {'filename': '丁嘉亮_AI产品经理_20250827_162012.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丁嘉亮_AI产品经理_20250827_162012.png', 'size': 212004, 'created_time': 1756282814.0, 'candidate_name': '丁嘉亮'}, {'filename': '潘玉玲_AI产品经理_20250827_162151.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\潘玉玲_AI产品经理_20250827_162151.png', 'size': 1196651, 'created_time': 1756282912.0, 'candidate_name': '潘玉玲'}, {'filename': '丘伟航_AI产品经理_20250827_162258.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丘伟航_AI产品经理_20250827_162258.png', 'size': 363723, 'created_time': 1756282980.0, 'candidate_name': '丘伟航'}, {'filename': '谭文杰_AI产品经理_20250827_162357.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\谭文杰_AI产品经理_20250827_162357.png', 'size': 173111, 'created_time': 1756283040.0, 'candidate_name': '谭文杰'}, {'filename': '叶利广_AI产品经理_20250827_162457.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\叶利广_AI产品经理_20250827_162457.png', 'size': 181559, 'created_time': 1756283098.0, 'candidate_name': '叶利广'}, {'filename': '张轩颖_AI产品经理_20250827_162556.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\张轩颖_AI产品经理_20250827_162556.png', 'size': 191507, 'created_time': 1756283158.0, 'candidate_name': '张轩颖'}, {'filename': '林先生_AI产品经理_20250827_162700.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林先生_AI产品经理_20250827_162700.png', 'size': 278584, 'created_time': 1756283222.0, 'candidate_name': '林先生'}, {'filename': '姚博_AI产品经理_20250901_105002.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250901_105002.png', 'size': 81500, 'created_time': 1756695002.581857, 'candidate_name': '姚博'}]}
2025-09-01 10:51:30 - INFO - 爬取任务启动结果: {'task_id': 'task_2', 'message': '收集任务已启动'}
2025-09-01 10:51:30 - INFO - ✅ 爬取任务已启动: task_2
2025-09-01 10:51:30 - INFO - 开始监控爬取进度: task_2
2025-09-01 10:51:40 - ERROR - 监控爬取进度异常: 
2025-09-01 10:51:45 - ERROR - 监控爬取进度异常: 
2025-09-01 10:52:01 - ERROR - 监控爬取进度异常: 
2025-09-01 10:52:06 - ERROR - 监控爬取进度异常: 
2025-09-01 10:52:22 - ERROR - 监控爬取进度异常: 
2025-09-01 10:52:26 - ERROR - 监控爬取进度异常: 
2025-09-01 10:52:43 - ERROR - 监控爬取进度异常: 
2025-09-01 10:52:47 - ERROR - 监控爬取进度异常: 
2025-09-01 10:53:04 - ERROR - 监控爬取进度异常: 
2025-09-01 10:53:07 - ERROR - 监控爬取进度异常: 
2025-09-01 10:53:24 - ERROR - 监控爬取进度异常: 
2025-09-01 10:53:28 - ERROR - 监控爬取进度异常: 
2025-09-01 10:53:45 - ERROR - 监控爬取进度异常: 
2025-09-01 10:53:48 - ERROR - 监控爬取进度异常: 
2025-09-01 10:54:05 - ERROR - 监控爬取进度异常: 
2025-09-01 10:54:09 - ERROR - 监控爬取进度异常: 
2025-09-01 10:54:25 - ERROR - 监控爬取进度异常: 
2025-09-01 10:54:30 - ERROR - 监控爬取进度异常: 
2025-09-01 10:54:46 - ERROR - 监控爬取进度异常: 
2025-09-01 10:54:51 - ERROR - 监控爬取进度异常: 
2025-09-01 10:55:07 - ERROR - 监控爬取进度异常: 
2025-09-01 10:55:12 - ERROR - 监控爬取进度异常: 
2025-09-01 10:55:28 - ERROR - 监控爬取进度异常: 
2025-09-01 10:55:33 - ERROR - 监控爬取进度异常: 
2025-09-01 10:55:49 - ERROR - 监控爬取进度异常: 
2025-09-01 10:55:54 - ERROR - 监控爬取进度异常: 
2025-09-01 10:56:10 - ERROR - 监控爬取进度异常: 
2025-09-01 10:56:15 - ERROR - 监控爬取进度异常: 
2025-09-01 10:56:24 - INFO - 爬取状态: {'task_id': 'task_2', 'status': 'completed', 'progress': 100, 'results_count': 10, 'target_count': 5, 'current_action': '收集完成', 'current_resumes': [{'filename': '王威_人力资源主管_20250825_205547.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\人力资源主管\\王威_人力资源主管_20250825_205547.png', 'size': 305581, 'created_time': 1756126548.0, 'candidate_name': '王威'}, {'filename': '郭女士_人力资源主管_20250825_205648.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\人力资源主管\\郭女士_人力资源主管_20250825_205648.png', 'size': 175575, 'created_time': 1756126608.0, 'candidate_name': '郭女士'}, {'filename': '林小姐_人力资源主管_20250825_205744.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\人力资源主管\\林小姐_人力资源主管_20250825_205744.png', 'size': 84791, 'created_time': 1756126664.0, 'candidate_name': '林小姐'}, {'filename': '梁滴翠_人力资源主管_20250825_205844.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\人力资源主管\\梁滴翠_人力资源主管_20250825_205844.png', 'size': 212255, 'created_time': 1756126724.0, 'candidate_name': '梁滴翠'}, {'filename': '王相元_人力资源主管_20250825_205946.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\人力资源主管\\王相元_人力资源主管_20250825_205946.png', 'size': 230521, 'created_time': 1756126786.0, 'candidate_name': '王相元'}, {'filename': '范女士_人力资源主管_20250901_105221.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\人力资源主管\\范女士_人力资源主管_20250901_105221.png', 'size': 179147, 'created_time': 1756695141.4254563, 'candidate_name': '范女士'}, {'filename': '刘丹_人力资源主管_20250901_105318.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\人力资源主管\\刘丹_人力资源主管_20250901_105318.png', 'size': 110696, 'created_time': 1756695198.9682763, 'candidate_name': '刘丹'}, {'filename': '刘春秀_人力资源主管_20250901_105417.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\人力资源主管\\刘春秀_人力资源主管_20250901_105417.png', 'size': 132205, 'created_time': 1756695257.106364, 'candidate_name': '刘春秀'}, {'filename': '李杏娜_人力资源主管_20250901_105514.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\人力资源主管\\李杏娜_人力资源主管_20250901_105514.png', 'size': 94396, 'created_time': 1756695314.0349405, 'candidate_name': '李杏娜'}, {'filename': '金女士_人力资源主管_20250901_105611.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\人力资源主管\\金女士_人力资源主管_20250901_105611.png', 'size': 124249, 'created_time': 1756695371.5424595, 'candidate_name': '金女士'}]}
2025-09-01 10:56:24 - INFO - 爬取完成，获取简历列表...
2025-09-01 10:56:25 - INFO - 获取到 10 份简历
2025-09-01 10:56:25 - INFO - 爬取任务完成: f1df43c2-c2b0-4888-a3dc-97c7bb9795d7, 共获取 10 份简历
2025-09-01 10:56:25 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 12, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.0, 'candidate_name': '刘俊锋'}, {'filename': '姚博_AI产品经理_20250827_161710.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250827_161710.png', 'size': 257381, 'created_time': 1756282632.0, 'candidate_name': '姚博'}, {'filename': '林丽丽_AI产品经理_20250827_161811.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林丽丽_AI产品经理_20250827_161811.png', 'size': 217050, 'created_time': 1756282692.0, 'candidate_name': '林丽丽'}, {'filename': '杨志海_AI产品经理_20250827_161912.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\杨志海_AI产品经理_20250827_161912.png', 'size': 179759, 'created_time': 1756282754.0, 'candidate_name': '杨志海'}, {'filename': '丁嘉亮_AI产品经理_20250827_162012.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丁嘉亮_AI产品经理_20250827_162012.png', 'size': 212004, 'created_time': 1756282814.0, 'candidate_name': '丁嘉亮'}, {'filename': '潘玉玲_AI产品经理_20250827_162151.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\潘玉玲_AI产品经理_20250827_162151.png', 'size': 1196651, 'created_time': 1756282912.0, 'candidate_name': '潘玉玲'}, {'filename': '丘伟航_AI产品经理_20250827_162258.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丘伟航_AI产品经理_20250827_162258.png', 'size': 363723, 'created_time': 1756282980.0, 'candidate_name': '丘伟航'}, {'filename': '谭文杰_AI产品经理_20250827_162357.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\谭文杰_AI产品经理_20250827_162357.png', 'size': 173111, 'created_time': 1756283040.0, 'candidate_name': '谭文杰'}, {'filename': '叶利广_AI产品经理_20250827_162457.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\叶利广_AI产品经理_20250827_162457.png', 'size': 181559, 'created_time': 1756283098.0, 'candidate_name': '叶利广'}, {'filename': '张轩颖_AI产品经理_20250827_162556.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\张轩颖_AI产品经理_20250827_162556.png', 'size': 191507, 'created_time': 1756283158.0, 'candidate_name': '张轩颖'}, {'filename': '林先生_AI产品经理_20250827_162700.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林先生_AI产品经理_20250827_162700.png', 'size': 278584, 'created_time': 1756283222.0, 'candidate_name': '林先生'}, {'filename': '姚博_AI产品经理_20250901_105002.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250901_105002.png', 'size': 81500, 'created_time': 1756695002.581857, 'candidate_name': '姚博'}]}
2025-09-01 10:56:30 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 12, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.0, 'candidate_name': '刘俊锋'}, {'filename': '姚博_AI产品经理_20250827_161710.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250827_161710.png', 'size': 257381, 'created_time': 1756282632.0, 'candidate_name': '姚博'}, {'filename': '林丽丽_AI产品经理_20250827_161811.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林丽丽_AI产品经理_20250827_161811.png', 'size': 217050, 'created_time': 1756282692.0, 'candidate_name': '林丽丽'}, {'filename': '杨志海_AI产品经理_20250827_161912.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\杨志海_AI产品经理_20250827_161912.png', 'size': 179759, 'created_time': 1756282754.0, 'candidate_name': '杨志海'}, {'filename': '丁嘉亮_AI产品经理_20250827_162012.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丁嘉亮_AI产品经理_20250827_162012.png', 'size': 212004, 'created_time': 1756282814.0, 'candidate_name': '丁嘉亮'}, {'filename': '潘玉玲_AI产品经理_20250827_162151.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\潘玉玲_AI产品经理_20250827_162151.png', 'size': 1196651, 'created_time': 1756282912.0, 'candidate_name': '潘玉玲'}, {'filename': '丘伟航_AI产品经理_20250827_162258.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丘伟航_AI产品经理_20250827_162258.png', 'size': 363723, 'created_time': 1756282980.0, 'candidate_name': '丘伟航'}, {'filename': '谭文杰_AI产品经理_20250827_162357.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\谭文杰_AI产品经理_20250827_162357.png', 'size': 173111, 'created_time': 1756283040.0, 'candidate_name': '谭文杰'}, {'filename': '叶利广_AI产品经理_20250827_162457.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\叶利广_AI产品经理_20250827_162457.png', 'size': 181559, 'created_time': 1756283098.0, 'candidate_name': '叶利广'}, {'filename': '张轩颖_AI产品经理_20250827_162556.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\张轩颖_AI产品经理_20250827_162556.png', 'size': 191507, 'created_time': 1756283158.0, 'candidate_name': '张轩颖'}, {'filename': '林先生_AI产品经理_20250827_162700.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林先生_AI产品经理_20250827_162700.png', 'size': 278584, 'created_time': 1756283222.0, 'candidate_name': '林先生'}, {'filename': '姚博_AI产品经理_20250901_105002.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250901_105002.png', 'size': 81500, 'created_time': 1756695002.581857, 'candidate_name': '姚博'}]}
2025-09-01 10:56:35 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 12, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.0, 'candidate_name': '刘俊锋'}, {'filename': '姚博_AI产品经理_20250827_161710.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250827_161710.png', 'size': 257381, 'created_time': 1756282632.0, 'candidate_name': '姚博'}, {'filename': '林丽丽_AI产品经理_20250827_161811.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林丽丽_AI产品经理_20250827_161811.png', 'size': 217050, 'created_time': 1756282692.0, 'candidate_name': '林丽丽'}, {'filename': '杨志海_AI产品经理_20250827_161912.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\杨志海_AI产品经理_20250827_161912.png', 'size': 179759, 'created_time': 1756282754.0, 'candidate_name': '杨志海'}, {'filename': '丁嘉亮_AI产品经理_20250827_162012.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丁嘉亮_AI产品经理_20250827_162012.png', 'size': 212004, 'created_time': 1756282814.0, 'candidate_name': '丁嘉亮'}, {'filename': '潘玉玲_AI产品经理_20250827_162151.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\潘玉玲_AI产品经理_20250827_162151.png', 'size': 1196651, 'created_time': 1756282912.0, 'candidate_name': '潘玉玲'}, {'filename': '丘伟航_AI产品经理_20250827_162258.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丘伟航_AI产品经理_20250827_162258.png', 'size': 363723, 'created_time': 1756282980.0, 'candidate_name': '丘伟航'}, {'filename': '谭文杰_AI产品经理_20250827_162357.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\谭文杰_AI产品经理_20250827_162357.png', 'size': 173111, 'created_time': 1756283040.0, 'candidate_name': '谭文杰'}, {'filename': '叶利广_AI产品经理_20250827_162457.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\叶利广_AI产品经理_20250827_162457.png', 'size': 181559, 'created_time': 1756283098.0, 'candidate_name': '叶利广'}, {'filename': '张轩颖_AI产品经理_20250827_162556.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\张轩颖_AI产品经理_20250827_162556.png', 'size': 191507, 'created_time': 1756283158.0, 'candidate_name': '张轩颖'}, {'filename': '林先生_AI产品经理_20250827_162700.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林先生_AI产品经理_20250827_162700.png', 'size': 278584, 'created_time': 1756283222.0, 'candidate_name': '林先生'}, {'filename': '姚博_AI产品经理_20250901_105002.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250901_105002.png', 'size': 81500, 'created_time': 1756695002.581857, 'candidate_name': '姚博'}]}
2025-09-01 10:56:41 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 12, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.0, 'candidate_name': '刘俊锋'}, {'filename': '姚博_AI产品经理_20250827_161710.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250827_161710.png', 'size': 257381, 'created_time': 1756282632.0, 'candidate_name': '姚博'}, {'filename': '林丽丽_AI产品经理_20250827_161811.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林丽丽_AI产品经理_20250827_161811.png', 'size': 217050, 'created_time': 1756282692.0, 'candidate_name': '林丽丽'}, {'filename': '杨志海_AI产品经理_20250827_161912.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\杨志海_AI产品经理_20250827_161912.png', 'size': 179759, 'created_time': 1756282754.0, 'candidate_name': '杨志海'}, {'filename': '丁嘉亮_AI产品经理_20250827_162012.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丁嘉亮_AI产品经理_20250827_162012.png', 'size': 212004, 'created_time': 1756282814.0, 'candidate_name': '丁嘉亮'}, {'filename': '潘玉玲_AI产品经理_20250827_162151.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\潘玉玲_AI产品经理_20250827_162151.png', 'size': 1196651, 'created_time': 1756282912.0, 'candidate_name': '潘玉玲'}, {'filename': '丘伟航_AI产品经理_20250827_162258.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丘伟航_AI产品经理_20250827_162258.png', 'size': 363723, 'created_time': 1756282980.0, 'candidate_name': '丘伟航'}, {'filename': '谭文杰_AI产品经理_20250827_162357.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\谭文杰_AI产品经理_20250827_162357.png', 'size': 173111, 'created_time': 1756283040.0, 'candidate_name': '谭文杰'}, {'filename': '叶利广_AI产品经理_20250827_162457.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\叶利广_AI产品经理_20250827_162457.png', 'size': 181559, 'created_time': 1756283098.0, 'candidate_name': '叶利广'}, {'filename': '张轩颖_AI产品经理_20250827_162556.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\张轩颖_AI产品经理_20250827_162556.png', 'size': 191507, 'created_time': 1756283158.0, 'candidate_name': '张轩颖'}, {'filename': '林先生_AI产品经理_20250827_162700.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林先生_AI产品经理_20250827_162700.png', 'size': 278584, 'created_time': 1756283222.0, 'candidate_name': '林先生'}, {'filename': '姚博_AI产品经理_20250901_105002.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250901_105002.png', 'size': 81500, 'created_time': 1756695002.581857, 'candidate_name': '姚博'}]}
2025-09-01 10:56:47 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 12, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.0, 'candidate_name': '刘俊锋'}, {'filename': '姚博_AI产品经理_20250827_161710.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250827_161710.png', 'size': 257381, 'created_time': 1756282632.0, 'candidate_name': '姚博'}, {'filename': '林丽丽_AI产品经理_20250827_161811.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林丽丽_AI产品经理_20250827_161811.png', 'size': 217050, 'created_time': 1756282692.0, 'candidate_name': '林丽丽'}, {'filename': '杨志海_AI产品经理_20250827_161912.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\杨志海_AI产品经理_20250827_161912.png', 'size': 179759, 'created_time': 1756282754.0, 'candidate_name': '杨志海'}, {'filename': '丁嘉亮_AI产品经理_20250827_162012.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丁嘉亮_AI产品经理_20250827_162012.png', 'size': 212004, 'created_time': 1756282814.0, 'candidate_name': '丁嘉亮'}, {'filename': '潘玉玲_AI产品经理_20250827_162151.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\潘玉玲_AI产品经理_20250827_162151.png', 'size': 1196651, 'created_time': 1756282912.0, 'candidate_name': '潘玉玲'}, {'filename': '丘伟航_AI产品经理_20250827_162258.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丘伟航_AI产品经理_20250827_162258.png', 'size': 363723, 'created_time': 1756282980.0, 'candidate_name': '丘伟航'}, {'filename': '谭文杰_AI产品经理_20250827_162357.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\谭文杰_AI产品经理_20250827_162357.png', 'size': 173111, 'created_time': 1756283040.0, 'candidate_name': '谭文杰'}, {'filename': '叶利广_AI产品经理_20250827_162457.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\叶利广_AI产品经理_20250827_162457.png', 'size': 181559, 'created_time': 1756283098.0, 'candidate_name': '叶利广'}, {'filename': '张轩颖_AI产品经理_20250827_162556.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\张轩颖_AI产品经理_20250827_162556.png', 'size': 191507, 'created_time': 1756283158.0, 'candidate_name': '张轩颖'}, {'filename': '林先生_AI产品经理_20250827_162700.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林先生_AI产品经理_20250827_162700.png', 'size': 278584, 'created_time': 1756283222.0, 'candidate_name': '林先生'}, {'filename': '姚博_AI产品经理_20250901_105002.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250901_105002.png', 'size': 81500, 'created_time': 1756695002.581857, 'candidate_name': '姚博'}]}
2025-09-01 10:56:52 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 12, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.0, 'candidate_name': '刘俊锋'}, {'filename': '姚博_AI产品经理_20250827_161710.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250827_161710.png', 'size': 257381, 'created_time': 1756282632.0, 'candidate_name': '姚博'}, {'filename': '林丽丽_AI产品经理_20250827_161811.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林丽丽_AI产品经理_20250827_161811.png', 'size': 217050, 'created_time': 1756282692.0, 'candidate_name': '林丽丽'}, {'filename': '杨志海_AI产品经理_20250827_161912.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\杨志海_AI产品经理_20250827_161912.png', 'size': 179759, 'created_time': 1756282754.0, 'candidate_name': '杨志海'}, {'filename': '丁嘉亮_AI产品经理_20250827_162012.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丁嘉亮_AI产品经理_20250827_162012.png', 'size': 212004, 'created_time': 1756282814.0, 'candidate_name': '丁嘉亮'}, {'filename': '潘玉玲_AI产品经理_20250827_162151.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\潘玉玲_AI产品经理_20250827_162151.png', 'size': 1196651, 'created_time': 1756282912.0, 'candidate_name': '潘玉玲'}, {'filename': '丘伟航_AI产品经理_20250827_162258.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丘伟航_AI产品经理_20250827_162258.png', 'size': 363723, 'created_time': 1756282980.0, 'candidate_name': '丘伟航'}, {'filename': '谭文杰_AI产品经理_20250827_162357.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\谭文杰_AI产品经理_20250827_162357.png', 'size': 173111, 'created_time': 1756283040.0, 'candidate_name': '谭文杰'}, {'filename': '叶利广_AI产品经理_20250827_162457.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\叶利广_AI产品经理_20250827_162457.png', 'size': 181559, 'created_time': 1756283098.0, 'candidate_name': '叶利广'}, {'filename': '张轩颖_AI产品经理_20250827_162556.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\张轩颖_AI产品经理_20250827_162556.png', 'size': 191507, 'created_time': 1756283158.0, 'candidate_name': '张轩颖'}, {'filename': '林先生_AI产品经理_20250827_162700.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林先生_AI产品经理_20250827_162700.png', 'size': 278584, 'created_time': 1756283222.0, 'candidate_name': '林先生'}, {'filename': '姚博_AI产品经理_20250901_105002.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250901_105002.png', 'size': 81500, 'created_time': 1756695002.581857, 'candidate_name': '姚博'}]}
2025-09-01 10:56:57 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 12, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.0, 'candidate_name': '刘俊锋'}, {'filename': '姚博_AI产品经理_20250827_161710.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250827_161710.png', 'size': 257381, 'created_time': 1756282632.0, 'candidate_name': '姚博'}, {'filename': '林丽丽_AI产品经理_20250827_161811.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林丽丽_AI产品经理_20250827_161811.png', 'size': 217050, 'created_time': 1756282692.0, 'candidate_name': '林丽丽'}, {'filename': '杨志海_AI产品经理_20250827_161912.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\杨志海_AI产品经理_20250827_161912.png', 'size': 179759, 'created_time': 1756282754.0, 'candidate_name': '杨志海'}, {'filename': '丁嘉亮_AI产品经理_20250827_162012.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丁嘉亮_AI产品经理_20250827_162012.png', 'size': 212004, 'created_time': 1756282814.0, 'candidate_name': '丁嘉亮'}, {'filename': '潘玉玲_AI产品经理_20250827_162151.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\潘玉玲_AI产品经理_20250827_162151.png', 'size': 1196651, 'created_time': 1756282912.0, 'candidate_name': '潘玉玲'}, {'filename': '丘伟航_AI产品经理_20250827_162258.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丘伟航_AI产品经理_20250827_162258.png', 'size': 363723, 'created_time': 1756282980.0, 'candidate_name': '丘伟航'}, {'filename': '谭文杰_AI产品经理_20250827_162357.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\谭文杰_AI产品经理_20250827_162357.png', 'size': 173111, 'created_time': 1756283040.0, 'candidate_name': '谭文杰'}, {'filename': '叶利广_AI产品经理_20250827_162457.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\叶利广_AI产品经理_20250827_162457.png', 'size': 181559, 'created_time': 1756283098.0, 'candidate_name': '叶利广'}, {'filename': '张轩颖_AI产品经理_20250827_162556.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\张轩颖_AI产品经理_20250827_162556.png', 'size': 191507, 'created_time': 1756283158.0, 'candidate_name': '张轩颖'}, {'filename': '林先生_AI产品经理_20250827_162700.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林先生_AI产品经理_20250827_162700.png', 'size': 278584, 'created_time': 1756283222.0, 'candidate_name': '林先生'}, {'filename': '姚博_AI产品经理_20250901_105002.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250901_105002.png', 'size': 81500, 'created_time': 1756695002.581857, 'candidate_name': '姚博'}]}
2025-09-01 10:57:02 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 12, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.0, 'candidate_name': '刘俊锋'}, {'filename': '姚博_AI产品经理_20250827_161710.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250827_161710.png', 'size': 257381, 'created_time': 1756282632.0, 'candidate_name': '姚博'}, {'filename': '林丽丽_AI产品经理_20250827_161811.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林丽丽_AI产品经理_20250827_161811.png', 'size': 217050, 'created_time': 1756282692.0, 'candidate_name': '林丽丽'}, {'filename': '杨志海_AI产品经理_20250827_161912.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\杨志海_AI产品经理_20250827_161912.png', 'size': 179759, 'created_time': 1756282754.0, 'candidate_name': '杨志海'}, {'filename': '丁嘉亮_AI产品经理_20250827_162012.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丁嘉亮_AI产品经理_20250827_162012.png', 'size': 212004, 'created_time': 1756282814.0, 'candidate_name': '丁嘉亮'}, {'filename': '潘玉玲_AI产品经理_20250827_162151.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\潘玉玲_AI产品经理_20250827_162151.png', 'size': 1196651, 'created_time': 1756282912.0, 'candidate_name': '潘玉玲'}, {'filename': '丘伟航_AI产品经理_20250827_162258.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丘伟航_AI产品经理_20250827_162258.png', 'size': 363723, 'created_time': 1756282980.0, 'candidate_name': '丘伟航'}, {'filename': '谭文杰_AI产品经理_20250827_162357.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\谭文杰_AI产品经理_20250827_162357.png', 'size': 173111, 'created_time': 1756283040.0, 'candidate_name': '谭文杰'}, {'filename': '叶利广_AI产品经理_20250827_162457.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\叶利广_AI产品经理_20250827_162457.png', 'size': 181559, 'created_time': 1756283098.0, 'candidate_name': '叶利广'}, {'filename': '张轩颖_AI产品经理_20250827_162556.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\张轩颖_AI产品经理_20250827_162556.png', 'size': 191507, 'created_time': 1756283158.0, 'candidate_name': '张轩颖'}, {'filename': '林先生_AI产品经理_20250827_162700.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林先生_AI产品经理_20250827_162700.png', 'size': 278584, 'created_time': 1756283222.0, 'candidate_name': '林先生'}, {'filename': '姚博_AI产品经理_20250901_105002.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250901_105002.png', 'size': 81500, 'created_time': 1756695002.581857, 'candidate_name': '姚博'}]}
2025-09-01 10:57:07 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'completed', 'progress': 100, 'results_count': 12, 'target_count': 20, 'current_action': '收集完成', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.0, 'candidate_name': '刘俊锋'}, {'filename': '姚博_AI产品经理_20250827_161710.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250827_161710.png', 'size': 257381, 'created_time': 1756282632.0, 'candidate_name': '姚博'}, {'filename': '林丽丽_AI产品经理_20250827_161811.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林丽丽_AI产品经理_20250827_161811.png', 'size': 217050, 'created_time': 1756282692.0, 'candidate_name': '林丽丽'}, {'filename': '杨志海_AI产品经理_20250827_161912.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\杨志海_AI产品经理_20250827_161912.png', 'size': 179759, 'created_time': 1756282754.0, 'candidate_name': '杨志海'}, {'filename': '丁嘉亮_AI产品经理_20250827_162012.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丁嘉亮_AI产品经理_20250827_162012.png', 'size': 212004, 'created_time': 1756282814.0, 'candidate_name': '丁嘉亮'}, {'filename': '潘玉玲_AI产品经理_20250827_162151.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\潘玉玲_AI产品经理_20250827_162151.png', 'size': 1196651, 'created_time': 1756282912.0, 'candidate_name': '潘玉玲'}, {'filename': '丘伟航_AI产品经理_20250827_162258.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丘伟航_AI产品经理_20250827_162258.png', 'size': 363723, 'created_time': 1756282980.0, 'candidate_name': '丘伟航'}, {'filename': '谭文杰_AI产品经理_20250827_162357.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\谭文杰_AI产品经理_20250827_162357.png', 'size': 173111, 'created_time': 1756283040.0, 'candidate_name': '谭文杰'}, {'filename': '叶利广_AI产品经理_20250827_162457.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\叶利广_AI产品经理_20250827_162457.png', 'size': 181559, 'created_time': 1756283098.0, 'candidate_name': '叶利广'}, {'filename': '张轩颖_AI产品经理_20250827_162556.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\张轩颖_AI产品经理_20250827_162556.png', 'size': 191507, 'created_time': 1756283158.0, 'candidate_name': '张轩颖'}, {'filename': '林先生_AI产品经理_20250827_162700.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林先生_AI产品经理_20250827_162700.png', 'size': 278584, 'created_time': 1756283222.0, 'candidate_name': '林先生'}, {'filename': '姚博_AI产品经理_20250901_105002.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250901_105002.png', 'size': 81500, 'created_time': 1756695002.581857, 'candidate_name': '姚博'}]}
2025-09-01 10:57:07 - INFO - 爬取完成，获取简历列表...
2025-09-01 10:57:07 - INFO - 获取到 12 份简历
2025-09-01 10:57:07 - INFO - 爬取任务完成: 605df213-de8f-46e8-8356-3f162b2832de, 共获取 12 份简历
2025-09-01 11:15:50 - INFO - 🚀 启动智能简历评估系统
2025-09-01 11:15:50 - INFO - 📋 系统功能:
2025-09-01 11:15:50 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 11:15:50 - INFO -   - 多线程并发处理
2025-09-01 11:15:50 - INFO -   - 实时进度监控
2025-09-01 11:15:50 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 11:15:50 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 11:15:50 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 11:15:50 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 11:15:56 - INFO - 🔐 统一登录请求: boss
2025-09-01 11:15:56 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 11:15:56 - INFO - DrissionPage浏览器初始化成功
2025-09-01 11:15:56 - INFO - 已访问登录页面，请手动登录
2025-09-01 11:15:56 - INFO - 等待用户登录...
2025-09-01 11:16:10 - INFO - 用户已登录
2025-09-01 11:16:10 - INFO - 开始提取用户信息
2025-09-01 11:16:10 - INFO - 用户名: 杨锐雄
2025-09-01 11:16:10 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 11:16:10 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 11:18:53 - INFO - 🔐 统一登录请求: boss
2025-09-01 11:18:53 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 11:18:53 - INFO - DrissionPage浏览器初始化成功
2025-09-01 11:18:53 - INFO - 已访问登录页面，请手动登录
2025-09-01 11:18:53 - INFO - 等待用户登录...
2025-09-01 11:18:55 - INFO - 用户已登录
2025-09-01 11:18:55 - INFO - 开始提取用户信息
2025-09-01 11:18:55 - INFO - 用户名: 杨锐雄
2025-09-01 11:18:55 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 11:18:55 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 11:19:05 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-09-01 11:19:05 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\JD_md转json.md
2025-09-01 11:19:10 - INFO - ✅ 成功将MD格式JD转换为JSON格式并保存到用户目录: user\杨锐雄\jd\AI产品经理.json
2025-09-01 11:19:10 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-09-01 11:19:10 - INFO - ✅ 发现已存在的JSON文件: user\杨锐雄\jd\AI产品经理.json
2025-09-01 11:19:10 - INFO - ✅ JSON文件格式验证通过
2025-09-01 11:19:10 - INFO - 提取的岗位名称: 'AI产品经理'
2025-09-01 11:19:10 - INFO - 最终使用的岗位名称: 'AI产品经理'
2025-09-01 11:19:10 - INFO - ✅ 保存新候选人简历到VL目录: user\杨锐雄\vl\VL_Result_AI产品经理\曾杰_VL识别结果.txt
2025-09-01 11:19:10 - INFO - 开始处理简历: 曾杰
2025-09-01 11:19:10 - INFO - 开始第1轮评估: 曾杰
2025-09-01 11:19:10 - INFO - 岗位名称: AI产品经理
2025-09-01 11:19:10 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\初筛1_自由版.md
2025-09-01 11:19:10 - INFO - 第1轮评估使用模型: qwen-plus
2025-09-01 11:19:41 - INFO - ✅ 第1轮评估完成: 曾杰 | 分数: 0.0
2025-09-01 11:19:41 - INFO - 检测到明确通过标识: ✅ 通过
2025-09-01 11:19:41 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_11：19_AI产品经理_一二点五三轮筛选结果/初筛合格\通过_曾杰_第1轮_评估报告.md
2025-09-01 11:19:41 - INFO - 开始提取候选人 曾杰 的初筛报告信息
2025-09-01 11:19:41 - INFO - 找到优势文本: **优势:**  
  - 多年AI产品设计经验，具备全流程管理能力。  
  - 深入理解AI技术（如YOLO、RAG、Prompt工程），并能结合业务场景落地。  
  - 项目成果显著，多个项目实现成本节约与效率提升。

- 
2025-09-01 11:19:41 - INFO - 找到风险文本: **风险与不足:**  
  - 无“不匹配”项。  
  - 期望薪资未提供，可能在后续沟通中存在不确定性（潜在风险）。  
  - 年龄38岁虽在JD未设限，但在部分企业中可能被视为“偏高”，但非硬性门槛问题。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。
- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历、年龄、薪资均满足硬性门槛要求，且在AI产品设计、跨部门协作、AI技术与业务融合方面具备丰富经验，核心职责匹配度为“高”。虽然期望薪资未提供，但未构成“不匹配”项，不影响初筛通过。
2025-09-01 11:19:41 - INFO - 提取的优势和风险: 优势=多年AI产品设计经验，具备全流程管理能力。  ；深入理解AI技术（如YOLO、RAG、Prompt工程），并能结合业务场景落地。  ；项目成果显著，多个项目实现成本节约与效率提升。, 风险=无“不匹配”项。  ；期望薪资未提供，可能在后续沟通中存在不确定性（潜在风险）。  
2025-09-01 11:19:41 - INFO - 提取到姓名: 曾杰
2025-09-01 11:19:41 - INFO - 提取到年龄: 38岁
2025-09-01 11:19:41 - INFO - 提取到工作经验: 10年以上
2025-09-01 11:19:41 - INFO - 提取到学历: 硕士
2025-09-01 11:19:41 - INFO - 提取到当前职位: 产品经理
2025-09-01 11:19:41 - INFO - 提取到期望薪资: 未提供
2025-09-01 11:19:41 - INFO - 从初筛报告提取的基本信息: {'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提供'}
2025-09-01 11:19:41 - INFO - 候选人 曾杰 初筛报告提取完成: advantages=多年AI产品设计经验，具备全流程管理能力。  ；深入理解AI技术（如YOLO、RAG、Prompt工程），并能结合业务场景落地。  ；项目成果显著，多个项目实现成本节约与效率提升。, risks=无“不匹配”项。  ；期望薪资未提供，可能在后续沟通中存在不确定性（潜在风险）。  , basic_info={'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提供'}
2025-09-01 11:19:41 - INFO - 开始第2轮评估: 曾杰
2025-09-01 11:19:41 - INFO - 岗位名称: AI产品经理
2025-09-01 11:19:41 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像分析.md
2025-09-01 11:19:41 - INFO - 第2轮评估使用模型: qwen-plus
2025-09-01 11:20:19 - WARNING - 分数不一致: 总分=83.0, 计算总分=0.0 (A:0.0+B:0.0+C:0.0+D:0.0+E:0.0)
2025-09-01 11:20:19 - WARNING - 候选人曾杰分数不一致: 总分=83.0, 计算总分=0.0
2025-09-01 11:20:19 - INFO - ✅ 第2轮评估完成: 曾杰 | 分数: 83.0
2025-09-01 11:20:19 - INFO - 未检测到明确关键词，默认通过
2025-09-01 11:20:19 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_11：19_AI产品经理_一二点五三轮筛选结果/画像分析结果\曾杰_第2轮_画像分析.md
2025-09-01 11:20:19 - INFO - 开始第2.5轮评估: 曾杰
2025-09-01 11:20:19 - INFO - 岗位名称: AI产品经理
2025-09-01 11:20:19 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像匹配.md
2025-09-01 11:20:19 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像匹配.md
2025-09-01 11:20:19 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-09-01 11:20:24 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-09-01 11:20:27 - INFO - ✅ 第2.5轮评估完成: 曾杰 | 分数: 0.0
2025-09-01 11:20:27 - INFO - 第2.5轮检测到明确通过标识: ✅ 通过
2025-09-01 11:20:27 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_11：19_AI产品经理_一二点五三轮筛选结果/二筛合格\通过_曾杰_第2.5轮_评估报告.md
2025-09-01 11:20:27 - INFO - 开始第3轮评估: 曾杰
2025-09-01 11:20:27 - INFO - 岗位名称: AI产品经理
2025-09-01 11:20:27 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\终极筛选_打分规则.md
2025-09-01 11:20:27 - INFO - 第3轮评估使用模型: qwen-plus
2025-09-01 11:21:35 - WARNING - 分数不一致: 总分=21.0, 计算总分=90.0 (A:47.0+B:34.0+C:9.0+D:0.0+E:0.0)
2025-09-01 11:21:35 - WARNING - 候选人曾杰分数不一致: 总分=21.0, 计算总分=90.0
2025-09-01 11:21:35 - INFO - ✅ 第3轮评估完成: 曾杰 | 分数: 21.0
2025-09-01 11:21:35 - INFO - 第3轮检测到合格关键词: 通过
2025-09-01 11:21:35 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_11：19_AI产品经理_一二点五三轮筛选结果/最终通过\21.0分_曾杰_第3轮_评估报告.md
2025-09-01 11:21:35 - INFO - ✅ 曾杰 完成三轮筛选，最终得分: 21.0, 是否通过: True
2025-09-01 11:21:35 - INFO - 候选人 曾杰 最终返回信息: advantages=多年AI产品设计经验，具备全流程管理能力。  ；深入理解AI技术（如YOLO、RAG、Prompt工程），并能结合业务场景落地。  ；项目成果显著，多个项目实现成本节约与效率提升。, risks=无“不匹配”项。  ；期望薪资未提供，可能在后续沟通中存在不确定性（潜在风险）。  , basic_info={'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提供'}
2025-09-01 11:41:52 - INFO - 🚀 启动智能简历评估系统
2025-09-01 11:41:52 - INFO - 📋 系统功能:
2025-09-01 11:41:52 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 11:41:52 - INFO -   - 多线程并发处理
2025-09-01 11:41:52 - INFO -   - 实时进度监控
2025-09-01 11:41:52 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 11:41:52 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 11:41:52 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 11:41:52 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 11:42:23 - INFO - 🚀 启动智能简历评估系统
2025-09-01 11:42:23 - INFO - 📋 系统功能:
2025-09-01 11:42:23 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 11:42:23 - INFO -   - 多线程并发处理
2025-09-01 11:42:23 - INFO -   - 实时进度监控
2025-09-01 11:42:23 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 11:42:23 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 11:42:23 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 11:42:23 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 11:47:35 - INFO - 🚀 启动智能简历评估系统
2025-09-01 11:47:35 - INFO - 📋 系统功能:
2025-09-01 11:47:35 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 11:47:35 - INFO -   - 多线程并发处理
2025-09-01 11:47:35 - INFO -   - 实时进度监控
2025-09-01 11:47:35 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 11:47:35 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 11:47:35 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 11:47:35 - INFO - 🔗 爬虫系统地址: http://localhost:8012
2025-09-01 11:47:35 - INFO - 任务管理已初始化
2025-09-01 11:47:42 - INFO - 🚀 启动智能简历评估系统
2025-09-01 11:47:42 - INFO - 📋 系统功能:
2025-09-01 11:47:42 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 11:47:42 - INFO -   - 多线程并发处理
2025-09-01 11:47:42 - INFO -   - 实时进度监控
2025-09-01 11:47:42 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 11:47:42 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 11:47:42 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 11:47:42 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 11:49:28 - INFO - 🚀 启动智能简历评估系统
2025-09-01 11:49:28 - INFO - 📋 系统功能:
2025-09-01 11:49:28 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 11:49:28 - INFO -   - 多线程并发处理
2025-09-01 11:49:28 - INFO -   - 实时进度监控
2025-09-01 11:49:28 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 11:49:28 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 11:49:28 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 11:49:28 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 11:49:41 - INFO - 🚀 启动智能简历评估系统
2025-09-01 11:49:41 - INFO - 📋 系统功能:
2025-09-01 11:49:41 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 11:49:41 - INFO -   - 多线程并发处理
2025-09-01 11:49:41 - INFO -   - 实时进度监控
2025-09-01 11:49:41 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 11:49:41 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 11:49:41 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 11:49:41 - INFO - 🔗 爬虫系统地址: http://localhost:8012
2025-09-01 11:49:41 - INFO - 任务管理已初始化
2025-09-01 11:49:45 - INFO - 🚀 启动智能简历评估系统
2025-09-01 11:49:45 - INFO - 📋 系统功能:
2025-09-01 11:49:45 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 11:49:45 - INFO -   - 多线程并发处理
2025-09-01 11:49:45 - INFO -   - 实时进度监控
2025-09-01 11:49:45 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 11:49:45 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 11:49:45 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 11:49:45 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 11:51:20 - INFO - 🚀 启动智能简历评估系统
2025-09-01 11:51:20 - INFO - 📋 系统功能:
2025-09-01 11:51:20 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 11:51:20 - INFO -   - 多线程并发处理
2025-09-01 11:51:20 - INFO -   - 实时进度监控
2025-09-01 11:51:20 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 11:51:20 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 11:51:20 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 11:51:20 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 11:53:16 - INFO - 🚀 启动智能简历评估系统
2025-09-01 11:53:16 - INFO - 📋 系统功能:
2025-09-01 11:53:16 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 11:53:16 - INFO -   - 多线程并发处理
2025-09-01 11:53:16 - INFO -   - 实时进度监控
2025-09-01 11:53:16 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 11:53:16 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 11:53:16 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 11:53:16 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 11:53:50 - INFO - 🚀 启动智能简历评估系统
2025-09-01 11:53:50 - INFO - 📋 系统功能:
2025-09-01 11:53:50 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 11:53:50 - INFO -   - 多线程并发处理
2025-09-01 11:53:50 - INFO -   - 实时进度监控
2025-09-01 11:53:50 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 11:53:50 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 11:53:50 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 11:53:50 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 11:53:59 - INFO - 🚀 启动智能简历评估系统
2025-09-01 11:53:59 - INFO - 📋 系统功能:
2025-09-01 11:53:59 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 11:53:59 - INFO -   - 多线程并发处理
2025-09-01 11:53:59 - INFO -   - 实时进度监控
2025-09-01 11:53:59 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 11:53:59 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 11:53:59 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 11:53:59 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 11:54:23 - INFO - 🚀 启动智能简历评估系统
2025-09-01 11:54:23 - INFO - 📋 系统功能:
2025-09-01 11:54:23 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 11:54:23 - INFO -   - 多线程并发处理
2025-09-01 11:54:23 - INFO -   - 实时进度监控
2025-09-01 11:54:23 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 11:54:23 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 11:54:23 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 11:54:23 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 11:54:27 - INFO - 🔐 统一登录请求: boss
2025-09-01 11:54:27 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 11:54:27 - INFO - DrissionPage浏览器初始化成功
2025-09-01 11:54:27 - INFO - 已访问登录页面，请手动登录
2025-09-01 11:54:27 - INFO - 等待用户登录...
2025-09-01 11:54:29 - INFO - 用户已登录
2025-09-01 11:54:29 - INFO - 开始提取用户信息
2025-09-01 11:54:29 - INFO - 用户名: 杨锐雄
2025-09-01 11:54:29 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 11:54:29 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 11:54:29 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 11:54:50 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-09-01 11:54:50 - INFO - ✅ 发现已存在的JSON文件: user\杨锐雄\jd\AI产品经理.json
2025-09-01 11:54:50 - INFO - ✅ JSON文件格式验证通过
2025-09-01 11:54:50 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-09-01 11:54:50 - INFO - ✅ 发现已存在的JSON文件: user\杨锐雄\jd\AI产品经理.json
2025-09-01 11:54:50 - INFO - ✅ JSON文件格式验证通过
2025-09-01 11:54:50 - INFO - 提取的岗位名称: 'AI产品经理'
2025-09-01 11:54:50 - INFO - 最终使用的岗位名称: 'AI产品经理'
2025-09-01 11:54:50 - INFO - ✅ 保存新候选人简历到VL目录: user\杨锐雄\vl\VL_Result_AI产品经理\陈明_VL识别结果.txt
2025-09-01 11:54:50 - INFO - 开始处理简历: 陈明
2025-09-01 11:54:50 - INFO - 开始第1轮评估: 陈明
2025-09-01 11:54:50 - INFO - 岗位名称: AI产品经理
2025-09-01 11:54:50 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\初筛1_自由版.md
2025-09-01 11:54:50 - INFO - 第1轮评估使用模型: qwen-plus
2025-09-01 11:55:16 - INFO - ✅ 第1轮评估完成: 陈明 | 分数: 0.0
2025-09-01 11:55:16 - INFO - 检测到明确通过标识: ✅ 通过
2025-09-01 11:55:16 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_11：54_AI产品经理_一二点五三轮筛选结果/初筛合格\通过_陈明_第1轮_评估报告.md
2025-09-01 11:55:16 - INFO - 开始提取候选人 陈明 的初筛报告信息
2025-09-01 11:55:16 - INFO - 找到优势文本: **优势:**
    - 硕士学历，高于JD学历要求。
    - 拥有多个涉及AI系统设计的项目经验（如AUV、无人机），具备AI技术理解能力。
    - 多次参与完整项目周期，具备产品从需求到落地的全流程经验。
    - 具备良好的跨部门协作与项目管理能力。

- 
2025-09-01 11:55:16 - INFO - 找到风险文本: **风险与不足:**
    - 候选人未明确提供期望薪资，可能在后续沟通中存在不确定性。
    - 候选人过往岗位多为技术岗（算法、电气、自动化工程师），缺乏直接的“产品经理”岗位经历，需评估其是否具备产品战略思维和用户视角。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。 → 无  
    2. 核心职责匹配度 评估结果为 "**低**"。 → 无  

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。 → 满足  
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。 → 满足（评估为“高”）

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人满足学历、年龄、薪资等所有硬性门槛要求，且在核心职责方面具备全流程产品开发经验、跨部门协作能力和AI技术理解能力。尽管其未直接担任过“产品经理”职务，但其技术背景与项目经验高度契合AI产品经理岗位的技术理解与产品落地要求，建议进入下一轮面试以进一步评估其产品思维与战略能力。
2025-09-01 11:55:16 - INFO - 提取的优势和风险: 优势=硕士学历，高于JD学历要求。；拥有多个涉及AI系统设计的项目经验（如AUV、无人机），具备AI技术理解能力。；多次参与完整项目周期，具备产品从需求到落地的全流程经验。, 风险=候选人未明确提供期望薪资，可能在后续沟通中存在不确定性。；候选人过往岗位多为技术岗（算法、电气、自动化工程师），缺乏直接的“产品经理”岗位经历，需评估其是否具备产品战略思维和用户视角。
2025-09-01 11:55:16 - INFO - 提取到姓名: 陈明
2025-09-01 11:55:16 - INFO - 提取到年龄: 48岁
2025-09-01 11:55:16 - INFO - 提取到工作经验: 约14年（2009年11月至今）
2025-09-01 11:55:16 - INFO - 提取到学历: 硕士
2025-09-01 11:55:16 - INFO - 提取到当前职位: 软件工程师V6
2025-09-01 11:55:16 - INFO - 提取到期望薪资: 未明确提供
2025-09-01 11:55:16 - INFO - 从初筛报告提取的基本信息: {'name': '陈明', 'age': '48岁', 'experience': '约14年（2009年11月至今）', 'education': '硕士', 'current_position': '软件工程师V6', 'expected_salary': '未明确提供'}
2025-09-01 11:55:16 - INFO - 候选人 陈明 初筛报告提取完成: advantages=硕士学历，高于JD学历要求。；拥有多个涉及AI系统设计的项目经验（如AUV、无人机），具备AI技术理解能力。；多次参与完整项目周期，具备产品从需求到落地的全流程经验。, risks=候选人未明确提供期望薪资，可能在后续沟通中存在不确定性。；候选人过往岗位多为技术岗（算法、电气、自动化工程师），缺乏直接的“产品经理”岗位经历，需评估其是否具备产品战略思维和用户视角。, basic_info={'name': '陈明', 'age': '48岁', 'experience': '约14年（2009年11月至今）', 'education': '硕士', 'current_position': '软件工程师V6', 'expected_salary': '未明确提供'}
2025-09-01 11:55:16 - INFO - 开始第2轮评估: 陈明
2025-09-01 11:55:16 - INFO - 岗位名称: AI产品经理
2025-09-01 11:55:16 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像分析.md
2025-09-01 11:55:16 - INFO - 第2轮评估使用模型: qwen-plus
2025-09-01 11:55:34 - WARNING - 分数不一致: 总分=85.0, 计算总分=0.0 (A:0.0+B:0.0+C:0.0+D:0.0+E:0.0)
2025-09-01 11:55:34 - WARNING - 候选人陈明分数不一致: 总分=85.0, 计算总分=0.0
2025-09-01 11:55:34 - INFO - ✅ 第2轮评估完成: 陈明 | 分数: 85.0
2025-09-01 11:55:34 - INFO - 未检测到明确关键词，默认通过
2025-09-01 11:55:34 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_11：54_AI产品经理_一二点五三轮筛选结果/画像分析结果\陈明_第2轮_画像分析.md
2025-09-01 11:55:34 - INFO - 开始第2.5轮评估: 陈明
2025-09-01 11:55:34 - INFO - 岗位名称: AI产品经理
2025-09-01 11:55:34 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像匹配.md
2025-09-01 11:55:34 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像匹配.md
2025-09-01 11:55:34 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-09-01 11:55:37 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-09-01 11:55:40 - INFO - ✅ 第2.5轮评估完成: 陈明 | 分数: 0.0
2025-09-01 11:55:40 - INFO - 第2.5轮检测到明确不通过标识: ❌ 不通过
2025-09-01 11:55:40 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_11：54_AI产品经理_一二点五三轮筛选结果/二筛不合格\未通过_陈明_第2.5轮_评估报告.md
2025-09-01 11:55:40 - INFO - ❌ 陈明 未通过第2.5轮筛选，得分: 0.0
2025-09-01 11:56:59 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 11:57:02 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 11:57:14 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 11:57:15 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 11:57:24 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 11:57:25 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 11:57:52 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 11:57:52 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 12:01:05 - INFO - 🚀 启动智能简历评估系统
2025-09-01 12:01:05 - INFO - 📋 系统功能:
2025-09-01 12:01:05 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 12:01:05 - INFO -   - 多线程并发处理
2025-09-01 12:01:05 - INFO -   - 实时进度监控
2025-09-01 12:01:05 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 12:01:05 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 12:01:05 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 12:01:05 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 12:01:17 - INFO - 🔐 统一登录请求: normal
2025-09-01 12:01:17 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 12:01:17 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 12:01:17 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 12:01:31 - INFO - 🔐 统一登录请求: normal
2025-09-01 12:01:31 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 12:01:31 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 12:01:31 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 13:31:24 - INFO - 🚀 启动智能简历评估系统
2025-09-01 13:31:24 - INFO - 📋 系统功能:
2025-09-01 13:31:24 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 13:31:24 - INFO -   - 多线程并发处理
2025-09-01 13:31:24 - INFO -   - 实时进度监控
2025-09-01 13:31:24 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 13:31:24 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 13:31:24 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 13:31:24 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 13:31:33 - INFO - 🔐 统一登录请求: boss
2025-09-01 13:31:33 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 13:31:33 - INFO - DrissionPage浏览器初始化成功
2025-09-01 13:31:34 - INFO - 已访问登录页面，请手动登录
2025-09-01 13:31:34 - INFO - 等待用户登录...
2025-09-01 13:31:36 - INFO - 用户已登录
2025-09-01 13:31:36 - INFO - 开始提取用户信息
2025-09-01 13:31:36 - INFO - 用户名: 杨锐雄
2025-09-01 13:31:36 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 13:31:36 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 13:31:36 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:31:48 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:31:48 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:31:49 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:31:49 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:31:50 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:31:51 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:33:40 - INFO - 🚀 启动智能简历评估系统
2025-09-01 13:33:40 - INFO - 📋 系统功能:
2025-09-01 13:33:40 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 13:33:40 - INFO -   - 多线程并发处理
2025-09-01 13:33:40 - INFO -   - 实时进度监控
2025-09-01 13:33:40 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 13:33:40 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 13:33:40 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 13:33:40 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 13:33:46 - INFO - 🔐 统一登录请求: boss
2025-09-01 13:33:46 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 13:33:46 - INFO - DrissionPage浏览器初始化成功
2025-09-01 13:33:46 - INFO - 已访问登录页面，请手动登录
2025-09-01 13:33:46 - INFO - 等待用户登录...
2025-09-01 13:33:48 - INFO - 用户已登录
2025-09-01 13:33:48 - INFO - 开始提取用户信息
2025-09-01 13:33:48 - INFO - 用户名: 杨锐雄
2025-09-01 13:33:48 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 13:33:48 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 13:33:48 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:33:52 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:33:53 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:33:53 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:33:56 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:34:40 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:34:41 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:34:54 - INFO - 🔐 统一登录请求: boss
2025-09-01 13:34:54 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 13:34:54 - INFO - DrissionPage浏览器初始化成功
2025-09-01 13:34:54 - INFO - 已访问登录页面，请手动登录
2025-09-01 13:34:54 - INFO - 等待用户登录...
2025-09-01 13:34:56 - INFO - 用户已登录
2025-09-01 13:34:56 - INFO - 开始提取用户信息
2025-09-01 13:34:56 - INFO - 用户名: 杨锐雄
2025-09-01 13:34:56 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 13:34:56 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 13:34:56 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:36:23 - INFO - 🚀 启动智能简历评估系统
2025-09-01 13:36:23 - INFO - 📋 系统功能:
2025-09-01 13:36:23 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 13:36:23 - INFO -   - 多线程并发处理
2025-09-01 13:36:23 - INFO -   - 实时进度监控
2025-09-01 13:36:23 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 13:36:23 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 13:36:23 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 13:36:23 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 13:36:32 - INFO - 🔐 统一登录请求: boss
2025-09-01 13:36:32 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 13:36:32 - INFO - DrissionPage浏览器初始化成功
2025-09-01 13:36:32 - INFO - 已访问登录页面，请手动登录
2025-09-01 13:36:32 - INFO - 等待用户登录...
2025-09-01 13:36:34 - INFO - 用户已登录
2025-09-01 13:36:34 - INFO - 开始提取用户信息
2025-09-01 13:36:34 - INFO - 用户名: 杨锐雄
2025-09-01 13:36:34 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 13:36:34 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 13:36:34 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:36:59 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:37:04 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:37:05 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:37:08 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:37:09 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:39:09 - INFO - 🚀 启动智能简历评估系统
2025-09-01 13:39:09 - INFO - 📋 系统功能:
2025-09-01 13:39:09 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 13:39:09 - INFO -   - 多线程并发处理
2025-09-01 13:39:09 - INFO -   - 实时进度监控
2025-09-01 13:39:09 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 13:39:09 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 13:39:09 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 13:39:09 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 13:39:22 - INFO - 🔐 统一登录请求: boss
2025-09-01 13:39:22 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 13:39:22 - INFO - DrissionPage浏览器初始化成功
2025-09-01 13:39:23 - INFO - 已访问登录页面，请手动登录
2025-09-01 13:39:23 - INFO - 等待用户登录...
2025-09-01 13:39:31 - INFO - 用户已登录
2025-09-01 13:39:31 - INFO - 开始提取用户信息
2025-09-01 13:39:31 - INFO - 用户名: 杨锐雄
2025-09-01 13:39:31 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 13:39:31 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 13:39:42 - INFO - 未检测到弹窗
2025-09-01 13:39:42 - INFO - 开始提取职位信息...
2025-09-01 13:39:42 - INFO - 成功进入职位管理页面
2025-09-01 13:39:43 - INFO - 已切换到iframe
2025-09-01 13:39:43 - INFO - 一共获取到3个职位
2025-09-01 13:39:43 - INFO - 开始提取第1个职位信息...
2025-09-01 13:39:44 - INFO - 第1次进入iframe
2025-09-01 13:39:44 - INFO - 职位名称: AI全栈工程师
2025-09-01 13:39:45 - INFO - 岗位信息: {'职位索引': 1, '职位名称': 'AI全栈工程师', '工作地点': '珠海', '工作经验': '1-3年', '学历要求': '本科', '薪资范围': '15-20K', '招聘类型': '全职', '职位详情': '岗位职责:\n1. 负责AI全栈工程相关工作\n2. 与团队合作，确保项目按时完成\n3. 持续优化工作流程，提升工作效率\n\n任职要求:\n1. 具备出色的沟通能力，能够有效协调项目资源\n2. 能够快速适应新环境，融入多功能团队\n3. 对AI技术有深入理解，能够不断学习和应用新知识'}
2025-09-01 13:39:45 - INFO - 第1个职位信息提取完成
2025-09-01 13:39:45 - INFO - 开始提取第2个职位信息...
2025-09-01 13:39:46 - INFO - 第2次进入iframe
2025-09-01 13:39:46 - INFO - 职位名称: AI产品经理
2025-09-01 13:39:47 - INFO - 岗位信息: {'职位索引': 2, '职位名称': 'AI产品经理', '工作地点': '珠海', '工作经验': '3-5年', '学历要求': '本科', '薪资范围': '14-20K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 负责AI技术在图帕斯户外产品中的应用规划，推动智能户外装备的创新与发展；\n2. 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平；\n3. 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行；\n4. 持续跟踪市场趋势和技术发展，为产品迭代提供科学依据；\n5. 与研发、设计、市场团队紧密合作，优化产品功能，增强用户体验。\n\n任职要求：\n1. 对AI技术及其应用场景有深刻理解，具备相关项目经验者优先；\n2. 熟悉户外用品市场，了解用户痛点和需求，能够转化为产品特性；\n3. 优秀的跨部门沟通与协调能力，能够推动多方合作达成目标；\n4. 强烈的责任心和执行力，能够在快节奏环境中解决问题；\n5. 具备良好的数据分析能力，能够通过数据驱动产品决策。'}
2025-09-01 13:39:47 - INFO - 第2个职位信息提取完成
2025-09-01 13:39:47 - INFO - 开始提取第3个职位信息...
2025-09-01 13:39:48 - INFO - 第3次进入iframe
2025-09-01 13:39:48 - INFO - 职位名称: 人力资源主管
2025-09-01 13:39:49 - INFO - 岗位信息: {'职位索引': 3, '职位名称': '人力资源主管', '工作地点': '珠海', '工作经验': '5-10年', '学历要求': '本科', '薪资范围': '6-7K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 统筹公司人力资源管理工作，确保人力资源政策的有效执行；\n2. 负责招聘、培训、绩效评估及员工关系等全员工生命周期的管理；\n3. 设计和优化薪酬福利体系，提升员工满意度和公司竞争力；\n4. 参与制定人力资源战略规划，支持公司业务发展及组织变革；\n5. 监控部门运作效率，提出改进建议，优化工作流程。\n\n任职要求：\n1. 人力资源管理或相关专业本科及以上学历；\n2. 5年以上人力资源相关工作经验，有主管或以上职位经验；\n3. 熟悉人力资源各大模块，具备优秀的人才招聘、培训及绩效管理能力；\n4. 具备良好的沟通协调能力和团队领导力，能够处理复杂的人事问题；\n5. 对人力资源法律法规有深入了解，能够为公司提供合规的人力资源解决方案。'}
2025-09-01 13:39:49 - INFO - 第3个职位信息提取完成
2025-09-01 13:39:49 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:39:49 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:39:58 - INFO - 用户 杨锐雄 删除了JD文件: AI全栈工程师.md
2025-09-01 13:40:07 - INFO - 🔐 统一登录请求: normal
2025-09-01 13:40:07 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 13:40:07 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 13:40:07 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 13:40:14 - INFO - 🔐 统一登录请求: boss
2025-09-01 13:40:14 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 13:40:14 - INFO - DrissionPage浏览器初始化成功
2025-09-01 13:40:14 - INFO - 已访问登录页面，请手动登录
2025-09-01 13:40:14 - INFO - 等待用户登录...
2025-09-01 13:40:16 - INFO - 用户已登录
2025-09-01 13:40:16 - INFO - 开始提取用户信息
2025-09-01 13:40:16 - INFO - 用户名: 杨锐雄
2025-09-01 13:40:16 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 13:40:16 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 13:40:16 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:40:16 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:40:24 - INFO - 🔐 统一登录请求: boss
2025-09-01 13:40:24 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 13:40:24 - INFO - DrissionPage浏览器初始化成功
2025-09-01 13:40:24 - INFO - 已访问登录页面，请手动登录
2025-09-01 13:40:24 - INFO - 等待用户登录...
2025-09-01 13:40:26 - INFO - 用户已登录
2025-09-01 13:40:26 - INFO - 开始提取用户信息
2025-09-01 13:40:26 - INFO - 用户名: 杨锐雄
2025-09-01 13:40:26 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 13:40:26 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 13:40:26 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:40:26 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:40:42 - INFO - 未检测到弹窗
2025-09-01 13:40:42 - INFO - 开始提取职位信息...
2025-09-01 13:40:43 - INFO - 成功进入职位管理页面
2025-09-01 13:40:44 - INFO - 已切换到iframe
2025-09-01 13:40:44 - INFO - 一共获取到3个职位
2025-09-01 13:40:44 - INFO - 开始提取第1个职位信息...
2025-09-01 13:40:44 - INFO - 第1次进入iframe
2025-09-01 13:40:44 - INFO - 职位名称: AI全栈工程师
2025-09-01 13:40:46 - INFO - 岗位信息: {'职位索引': 1, '职位名称': 'AI全栈工程师', '工作地点': '珠海', '工作经验': '1-3年', '学历要求': '本科', '薪资范围': '15-20K', '招聘类型': '全职', '职位详情': '岗位职责:\n1. 负责AI全栈工程相关工作\n2. 与团队合作，确保项目按时完成\n3. 持续优化工作流程，提升工作效率\n\n任职要求:\n1. 具备出色的沟通能力，能够有效协调项目资源\n2. 能够快速适应新环境，融入多功能团队\n3. 对AI技术有深入理解，能够不断学习和应用新知识'}
2025-09-01 13:40:46 - INFO - 第1个职位信息提取完成
2025-09-01 13:40:46 - INFO - 开始提取第2个职位信息...
2025-09-01 13:40:46 - INFO - 第2次进入iframe
2025-09-01 13:40:46 - INFO - 职位名称: AI产品经理
2025-09-01 13:40:48 - INFO - 岗位信息: {'职位索引': 2, '职位名称': 'AI产品经理', '工作地点': '珠海', '工作经验': '3-5年', '学历要求': '本科', '薪资范围': '14-20K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 负责AI技术在图帕斯户外产品中的应用规划，推动智能户外装备的创新与发展；\n2. 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平；\n3. 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行；\n4. 持续跟踪市场趋势和技术发展，为产品迭代提供科学依据；\n5. 与研发、设计、市场团队紧密合作，优化产品功能，增强用户体验。\n\n任职要求：\n1. 对AI技术及其应用场景有深刻理解，具备相关项目经验者优先；\n2. 熟悉户外用品市场，了解用户痛点和需求，能够转化为产品特性；\n3. 优秀的跨部门沟通与协调能力，能够推动多方合作达成目标；\n4. 强烈的责任心和执行力，能够在快节奏环境中解决问题；\n5. 具备良好的数据分析能力，能够通过数据驱动产品决策。'}
2025-09-01 13:40:48 - INFO - 第2个职位信息提取完成
2025-09-01 13:40:48 - INFO - 开始提取第3个职位信息...
2025-09-01 13:40:48 - INFO - 第3次进入iframe
2025-09-01 13:40:48 - INFO - 职位名称: 人力资源主管
2025-09-01 13:40:50 - INFO - 岗位信息: {'职位索引': 3, '职位名称': '人力资源主管', '工作地点': '珠海', '工作经验': '5-10年', '学历要求': '本科', '薪资范围': '6-7K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 统筹公司人力资源管理工作，确保人力资源政策的有效执行；\n2. 负责招聘、培训、绩效评估及员工关系等全员工生命周期的管理；\n3. 设计和优化薪酬福利体系，提升员工满意度和公司竞争力；\n4. 参与制定人力资源战略规划，支持公司业务发展及组织变革；\n5. 监控部门运作效率，提出改进建议，优化工作流程。\n\n任职要求：\n1. 人力资源管理或相关专业本科及以上学历；\n2. 5年以上人力资源相关工作经验，有主管或以上职位经验；\n3. 熟悉人力资源各大模块，具备优秀的人才招聘、培训及绩效管理能力；\n4. 具备良好的沟通协调能力和团队领导力，能够处理复杂的人事问题；\n5. 对人力资源法律法规有深入了解，能够为公司提供合规的人力资源解决方案。'}
2025-09-01 13:40:50 - INFO - 第3个职位信息提取完成
2025-09-01 13:41:21 - INFO - 🔐 统一登录请求: boss
2025-09-01 13:41:21 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 13:41:21 - INFO - DrissionPage浏览器初始化成功
2025-09-01 13:41:21 - INFO - 已访问登录页面，请手动登录
2025-09-01 13:41:21 - INFO - 等待用户登录...
2025-09-01 13:41:23 - INFO - 用户已登录
2025-09-01 13:41:23 - INFO - 开始提取用户信息
2025-09-01 13:41:23 - INFO - 用户名: 杨锐雄
2025-09-01 13:41:23 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 13:41:23 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 13:41:23 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:41:23 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:41:27 - INFO - 开始转换MD格式JD: 人力资源主管.md
2025-09-01 13:41:27 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\JD_md转json.md
2025-09-01 13:41:36 - INFO - ✅ 成功将MD格式JD转换为JSON格式并保存到用户目录: user\杨锐雄\jd\人力资源主管.json
2025-09-01 13:41:36 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:41:36 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:41:36 - INFO - 开始转换MD格式JD: 人力资源主管.md
2025-09-01 13:41:36 - INFO - ✅ 发现已存在的JSON文件: user\杨锐雄\jd\人力资源主管.json
2025-09-01 13:41:36 - INFO - ✅ JSON文件格式验证通过
2025-09-01 13:41:36 - INFO - 提取的岗位名称: '人力资源主管'
2025-09-01 13:41:36 - INFO - 最终使用的岗位名称: '人力资源主管'
2025-09-01 13:41:36 - INFO - ✅ 保存新候选人简历到VL目录: user\杨锐雄\vl\VL_Result_人力资源主管\曾杰_VL识别结果.txt
2025-09-01 13:41:36 - INFO - 开始处理简历: 曾杰
2025-09-01 13:41:36 - INFO - 开始第1轮评估: 曾杰
2025-09-01 13:41:36 - INFO - 岗位名称: 人力资源主管
2025-09-01 13:41:36 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\初筛1_自由版.md
2025-09-01 13:41:36 - INFO - 第1轮评估使用模型: qwen-plus
2025-09-01 13:42:06 - INFO - ✅ 第1轮评估完成: 曾杰 | 分数: 0.0
2025-09-01 13:42:06 - INFO - 检测到不合格关键词: 淘汰
2025-09-01 13:42:06 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_13：41_人力资源主管_一二点五三轮筛选结果/初筛不合格\未通过_曾杰_第1轮_评估报告.md
2025-09-01 13:42:06 - INFO - 开始提取候选人 曾杰 的初筛报告信息
2025-09-01 13:42:06 - INFO - 找到优势文本: **优势:**  
    - 硕士学历，符合岗位学历要求。
    - 具备丰富的项目管理经验，熟悉产品设计与技术实施流程。

- 
2025-09-01 13:42:06 - INFO - 找到风险文本: **风险与不足:**  
    - 简历中**完全缺乏与人力资源相关的核心职责经验**，如招聘、培训、绩效、员工关系、薪酬体系等。
    - 所有工作经历集中在产品管理、技术实施与项目管理领域，与JD要求的HR主管岗位存在本质偏差。
    - 简历未提供期望薪资，虽默认匹配，但后续需确认是否在JD范围内。

**4. 初筛结论**

- **淘汰条件：**
    1. 核心职责匹配度评估结果为“低”。

- **通过条件：**
    1. 不满足。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人具备硕士学历且年龄匹配，但**核心职责匹配度评估为“低”**，因其简历中**未体现任何人力资源管理相关的经验或职责**，与岗位要求存在本质不符，不具备胜任该岗位的基础能力。
2025-09-01 13:42:06 - INFO - 提取的优势和风险: 优势=硕士学历，符合岗位学历要求。；具备丰富的项目管理经验，熟悉产品设计与技术实施流程。, 风险=简历中**完全缺乏与人力资源相关的核心职责经验**，如招聘、培训、绩效、员工关系、薪酬体系等。；所有工作经历集中在产品管理、技术实施与项目管理领域，与JD要求的HR主管岗位存在本质偏差。
2025-09-01 13:42:06 - INFO - 提取到姓名: 曾杰
2025-09-01 13:42:06 - INFO - 提取到年龄: 38岁
2025-09-01 13:42:06 - INFO - 提取到工作经验: 10年以上
2025-09-01 13:42:06 - INFO - 提取到学历: 硕士
2025-09-01 13:42:06 - INFO - 提取到当前职位: 高职-腾讯研究院
2025-09-01 13:42:06 - INFO - 提取到期望薪资: 未明确提供（根据JD设定为默认匹配）
2025-09-01 13:42:06 - INFO - 从初筛报告提取的基本信息: {'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '高职-腾讯研究院', 'expected_salary': '未明确提供（根据JD设定为默认匹配）'}
2025-09-01 13:42:06 - INFO - 候选人 曾杰 初筛报告提取完成: advantages=硕士学历，符合岗位学历要求。；具备丰富的项目管理经验，熟悉产品设计与技术实施流程。, risks=简历中**完全缺乏与人力资源相关的核心职责经验**，如招聘、培训、绩效、员工关系、薪酬体系等。；所有工作经历集中在产品管理、技术实施与项目管理领域，与JD要求的HR主管岗位存在本质偏差。, basic_info={'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '高职-腾讯研究院', 'expected_salary': '未明确提供（根据JD设定为默认匹配）'}
2025-09-01 13:42:06 - INFO - ❌ 曾杰 未通过第1轮筛选，得分: 0.0
2025-09-01 13:42:06 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:42:06 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:42:06 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:42:06 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:42:06 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:42:09 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:42:31 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:43:01 - INFO - 未检测到弹窗
2025-09-01 13:43:01 - INFO - 开始提取职位信息...
2025-09-01 13:43:01 - INFO - 成功进入职位管理页面
2025-09-01 13:43:02 - INFO - 已切换到iframe
2025-09-01 13:43:02 - INFO - 一共获取到3个职位
2025-09-01 13:43:02 - INFO - 开始提取第1个职位信息...
2025-09-01 13:43:02 - INFO - 第1次进入iframe
2025-09-01 13:43:02 - INFO - 职位名称: AI全栈工程师
2025-09-01 13:43:04 - INFO - 岗位信息: {'职位索引': 1, '职位名称': 'AI全栈工程师', '工作地点': '珠海', '工作经验': '1-3年', '学历要求': '本科', '薪资范围': '15-20K', '招聘类型': '全职', '职位详情': '岗位职责:\n1. 负责AI全栈工程相关工作\n2. 与团队合作，确保项目按时完成\n3. 持续优化工作流程，提升工作效率\n\n任职要求:\n1. 具备出色的沟通能力，能够有效协调项目资源\n2. 能够快速适应新环境，融入多功能团队\n3. 对AI技术有深入理解，能够不断学习和应用新知识'}
2025-09-01 13:43:04 - INFO - 第1个职位信息提取完成
2025-09-01 13:43:04 - INFO - 开始提取第2个职位信息...
2025-09-01 13:43:04 - INFO - 第2次进入iframe
2025-09-01 13:43:05 - INFO - 职位名称: AI产品经理
2025-09-01 13:43:06 - INFO - 岗位信息: {'职位索引': 2, '职位名称': 'AI产品经理', '工作地点': '珠海', '工作经验': '3-5年', '学历要求': '本科', '薪资范围': '14-20K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 负责AI技术在图帕斯户外产品中的应用规划，推动智能户外装备的创新与发展；\n2. 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平；\n3. 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行；\n4. 持续跟踪市场趋势和技术发展，为产品迭代提供科学依据；\n5. 与研发、设计、市场团队紧密合作，优化产品功能，增强用户体验。\n\n任职要求：\n1. 对AI技术及其应用场景有深刻理解，具备相关项目经验者优先；\n2. 熟悉户外用品市场，了解用户痛点和需求，能够转化为产品特性；\n3. 优秀的跨部门沟通与协调能力，能够推动多方合作达成目标；\n4. 强烈的责任心和执行力，能够在快节奏环境中解决问题；\n5. 具备良好的数据分析能力，能够通过数据驱动产品决策。'}
2025-09-01 13:43:06 - INFO - 第2个职位信息提取完成
2025-09-01 13:43:06 - INFO - 开始提取第3个职位信息...
2025-09-01 13:43:06 - INFO - 第3次进入iframe
2025-09-01 13:43:07 - INFO - 职位名称: 人力资源主管
2025-09-01 13:43:08 - INFO - 岗位信息: {'职位索引': 3, '职位名称': '人力资源主管', '工作地点': '珠海', '工作经验': '5-10年', '学历要求': '本科', '薪资范围': '6-7K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 统筹公司人力资源管理工作，确保人力资源政策的有效执行；\n2. 负责招聘、培训、绩效评估及员工关系等全员工生命周期的管理；\n3. 设计和优化薪酬福利体系，提升员工满意度和公司竞争力；\n4. 参与制定人力资源战略规划，支持公司业务发展及组织变革；\n5. 监控部门运作效率，提出改进建议，优化工作流程。\n\n任职要求：\n1. 人力资源管理或相关专业本科及以上学历；\n2. 5年以上人力资源相关工作经验，有主管或以上职位经验；\n3. 熟悉人力资源各大模块，具备优秀的人才招聘、培训及绩效管理能力；\n4. 具备良好的沟通协调能力和团队领导力，能够处理复杂的人事问题；\n5. 对人力资源法律法规有深入了解，能够为公司提供合规的人力资源解决方案。'}
2025-09-01 13:43:08 - INFO - 第3个职位信息提取完成
2025-09-01 13:43:08 - INFO - 🔐 统一登录请求: normal
2025-09-01 13:43:08 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 13:43:08 - INFO - 🔐 统一登录请求: normal
2025-09-01 13:43:08 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 13:43:08 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\未知用户\result
2025-09-01 13:43:08 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\未知用户\result
2025-09-01 13:43:09 - INFO - 🔐 统一登录请求: normal
2025-09-01 13:43:09 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 13:43:09 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 13:43:09 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 13:43:29 - INFO - 用户 123 删除了JD文件: 售前_JD.md
2025-09-01 13:43:39 - INFO - 🔐 统一登录请求: normal
2025-09-01 13:43:39 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 13:43:39 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 13:43:39 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 13:43:43 - INFO - 用户 123 删除了JD文件: 人力资源主管.md
2025-09-01 13:48:34 - INFO - 🔐 统一登录请求: normal
2025-09-01 13:48:34 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 13:48:34 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 13:48:34 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 13:48:37 - INFO - 用户 123 删除了JD文件: AI售后_JD.md
2025-09-01 13:54:50 - INFO - 🔐 统一登录请求: normal
2025-09-01 13:54:50 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 13:54:50 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 13:54:50 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 13:55:04 - INFO - 🚀 启动智能简历评估系统
2025-09-01 13:55:04 - INFO - 📋 系统功能:
2025-09-01 13:55:04 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 13:55:04 - INFO -   - 多线程并发处理
2025-09-01 13:55:04 - INFO -   - 实时进度监控
2025-09-01 13:55:04 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 13:55:04 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 13:55:04 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 13:55:04 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 13:55:10 - INFO - 🔐 统一登录请求: normal
2025-09-01 13:55:10 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 13:55:10 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 13:55:10 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 13:55:23 - INFO - 🔐 统一登录请求: normal
2025-09-01 13:55:23 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 13:55:23 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 13:55:23 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 13:58:59 - INFO - 🚀 启动智能简历评估系统
2025-09-01 13:58:59 - INFO - 📋 系统功能:
2025-09-01 13:58:59 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 13:58:59 - INFO -   - 多线程并发处理
2025-09-01 13:58:59 - INFO -   - 实时进度监控
2025-09-01 13:58:59 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 13:58:59 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 13:58:59 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 13:58:59 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 13:59:25 - INFO - 🔐 统一登录请求: normal
2025-09-01 13:59:25 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 13:59:25 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 13:59:25 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 14:02:04 - INFO - 🔐 统一登录请求: boss
2025-09-01 14:02:04 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 14:02:05 - INFO - DrissionPage浏览器初始化成功
2025-09-01 14:02:05 - INFO - 已访问登录页面，请手动登录
2025-09-01 14:02:05 - INFO - 等待用户登录...
2025-09-01 14:02:13 - INFO - 用户已登录
2025-09-01 14:02:13 - INFO - 开始提取用户信息
2025-09-01 14:02:13 - INFO - 用户名: 杨锐雄
2025-09-01 14:02:13 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 14:02:13 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 14:02:13 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 14:03:24 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 14:37:22 - INFO - 🚀 启动智能简历评估系统
2025-09-01 14:37:22 - INFO - 📋 系统功能:
2025-09-01 14:37:22 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 14:37:22 - INFO -   - 多线程并发处理
2025-09-01 14:37:22 - INFO -   - 实时进度监控
2025-09-01 14:37:22 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 14:37:22 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 14:37:22 - INFO -  - JD来源: /Users/<USER>/Desktop/智能招聘系统_version1.0.2 - 简化/user/default/jd/人力资源主管_JD.md
2025-09-01 14:37:22 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 14:41:12 - INFO - 🚀 启动智能简历评估系统
2025-09-01 14:41:12 - INFO - 📋 系统功能:
2025-09-01 14:41:12 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 14:41:12 - INFO -   - 多线程并发处理
2025-09-01 14:41:12 - INFO -   - 实时进度监控
2025-09-01 14:41:12 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 14:41:12 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 14:41:12 - INFO -  - JD来源: /Users/<USER>/Desktop/智能招聘系统_version1.0.2 - 简化/user/default/jd/人力资源主管_JD.md
2025-09-01 14:41:12 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 14:42:30 - INFO - 🚀 启动智能简历评估系统
2025-09-01 14:42:30 - INFO - 📋 系统功能:
2025-09-01 14:42:30 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 14:42:30 - INFO -   - 多线程并发处理
2025-09-01 14:42:30 - INFO -   - 实时进度监控
2025-09-01 14:42:30 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 14:42:30 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 14:42:30 - INFO -  - JD来源: /Users/<USER>/Desktop/智能招聘系统_version1.0.2 - 简化/user/default/jd/人力资源主管_JD.md
2025-09-01 14:42:30 - INFO - 🔗 爬虫系统地址: http://localhost:8000
