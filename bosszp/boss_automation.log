2025-08-21 16:07:08,538 - INFO - 日志系统初始化成功
2025-08-21 16:07:08,540 - INFO - 开始运行BOSS直聘自动化程序 V2.0...
2025-08-21 16:07:08,582 - INFO - ====== WebDriver manager ======
2025-08-21 16:07:11,616 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 16:07:12,297 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 16:07:12,881 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\139.0.7258.138\chromedriver-win32/chromedriver.exe] found in cache
2025-08-21 16:07:14,706 - INFO - 使用自动下载的chromedriver
2025-08-21 16:07:14,729 - INFO - Chrome浏览器驱动设置成功
2025-08-21 16:07:14,729 - INFO - 访问登录页面: https://www.zhipin.com/web/user/?ka=header-login
2025-08-21 16:07:15,593 - INFO - 等待用户登录（超时时间：300秒）...
2025-08-21 16:07:15,593 - INFO - 请在浏览器中完成登录操作...
2025-08-21 16:07:44,528 - INFO - 检测到用户已登录，页面已跳转到聊天页面
2025-08-21 16:07:44,528 - INFO - 等待页面完全加载（超时时间：30秒）...
2025-08-21 16:07:47,570 - INFO - 页面加载完成
2025-08-21 16:07:48,595 - INFO - 检测到弹窗
2025-08-21 16:07:48,646 - INFO - 检测到弹窗并已关闭
2025-08-21 16:07:49,159 - INFO - 尝试点击个人中心...
2025-08-21 16:07:49,229 - INFO - 成功点击个人中心
2025-08-21 16:07:52,234 - INFO - 等待页面完全加载（超时时间：30秒）...
2025-08-21 16:07:55,240 - INFO - 页面加载完成
2025-08-21 16:07:55,241 - INFO - 开始提取用户信息...
2025-08-21 16:07:57,278 - INFO - 找到姓名: 杨锐雄
2025-08-21 16:07:57,330 - WARNING - 未找到公司名称信息
2025-08-21 16:07:57,346 - INFO - 找到头像: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-08-21 16:07:57,347 - INFO - 用户信息已保存到文件: user_info_20250821_160757.json
2025-08-21 16:07:57,347 - INFO - ==================================================
2025-08-21 16:07:57,347 - INFO - 用户信息提取结果:
2025-08-21 16:07:57,347 - INFO - 姓名: 杨锐雄
2025-08-21 16:07:57,347 - INFO - 公司名称: 未找到
2025-08-21 16:07:57,347 - INFO - 头像: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-08-21 16:07:57,347 - INFO - ==================================================
2025-08-21 16:07:57,348 - INFO - 用户信息获取成功，开始点击职位管理按钮...
2025-08-21 16:07:57,348 - INFO - 尝试点击职位管理按钮...
2025-08-21 16:08:21,007 - INFO - 成功点击职位管理按钮，使用选择器: //a[contains(text(), '职位管理')]
2025-08-21 16:08:24,007 - INFO - 成功点击职位管理按钮
2025-08-21 16:08:27,008 - INFO - 开始提取职位列表信息...
2025-08-21 16:08:28,519 - INFO - 开始在iframe中查找职位列表...
2025-08-21 16:08:28,526 - INFO - 找到 1 个iframe
2025-08-21 16:08:28,536 - INFO - 已切换到第一个iframe
2025-08-21 16:08:28,546 - INFO - 第一个iframe下的div.job-list-warp内容: <div data-v-e9324162="" class="job-list-warp"><!----> <!----> <div data-v-e9324162="" class="list-inner-container"><!----> <ul data-v-e9324162="" class="job-list-content"><li data-v-37187c92="" data-v-e9324162="" data-id="41f8839f7881490003N63t6_EFJS" class="job-jobInfo-warp"><!----> <div data-v-37187c92="" class="single-check-box"></div> <div data-v-37187c92="" class="top-wrap"><div data-v-7344c8e0="" data-v-37187c92="" class="job-main-info-wrapper"><div data-v-7344c8e0="" class="job-title"><a data-v-7344c8e0="" href="javascript:;">AI产品经理</a> <div data-v-7344c8e0="" class="label-common label-normal">普</div> <!----> <!----> <!----> <!----></div> <div data-v-7344c8e0="" class="info-labels"><!----> <span data-v-7344c8e0="">珠海</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">3-5年</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">本科</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">14-20K</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">全职</span> <!----> <!----></div> <!----></div> <div data-v-fb03be90="" data-v-37187c92="" class="job-about-num-wrapper"><div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">17</div> <div data-v-fb03be90="" class="name">看过我</div></div> <div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">4</div> <div data-v-fb03be90="" class="name">沟通过</div></div> <div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">0</div> <div data-v-fb03be90="" class="name">感兴趣</div></div></div> <div data-v-6692a4a3="" data-v-37187c92="" class="job-status-wrapper"><div data-v-6692a4a3="" class="status-top"><div data-v-6692a4a3="" class="status-box status-opening"><span data-v-6692a4a3="" class="pointer" style="background: rgb(0, 190, 189);"></span>
            开放中
            <!----></div> <!----> <!----></div> <div data-v-6692a4a3="" class="status-bottom"><!----></div></div> <div data-v-37187c92="" class="jobInfo-container operation-container"><!----> <a data-v-37187c92="" href="javascript:;" class="position-edit opreat-btn">
                    编辑
                </a> <a data-v-37187c92="" href="javascript:;" class="position-close opreat-btn">
                    关闭
                </a> <!----> <div data-v-460bff2e="" data-v-37187c92="" class="job-operate-wrapper opreat-btn"><div data-v-460bff2e="" class="more-operate"><span data-v-460bff2e="" class="dot"><i data-v-460bff2e=""></i> <i data-v-460bff2e=""></i> <i data-v-460bff2e=""></i></span> <!----> <ul data-v-460bff2e="" class="job-operate-container"><li data-v-460bff2e="" class="job-operate-item">
                        预览
                        <!----></li><li data-v-460bff2e="" class="job-operate-share">
                        分享
                        <!----></li><li data-v-460bff2e="" class="job-operate-item">
                        复制
                        <span data-v-460bff2e="" class="new-icon">new</span></li><li data-v-460bff2e="" class="job-operate-item">
                        删除
                        <!----></li></ul></div></div></div></div> <!----></li><li data-v-37187c92="" data-v-e9324162="" data-id="52a786b20358e5e903N6396_GVdV" class="job-jobInfo-warp"><!----> <div data-v-37187c92="" class="single-check-box"></div> <div data-v-37187c92="" class="top-wrap"><div data-v-7344c8e0="" data-v-37187c92="" class="job-main-info-wrapper"><div data-v-7344c8e0="" class="job-title"><a data-v-7344c8e0="" href="javascript:;">人力资源主管</a> <div data-v-7344c8e0="" class="label-common label-normal">普</div> <!----> <!----> <!----> <!----></div> <div data-v-7344c8e0="" class="info-labels"><!----> <span data-v-7344c8e0="">珠海</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">5-10年</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">本科</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">6-7K</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">全职</span> <!----> <!----></div> <!----></div> <div data-v-fb03be90="" data-v-37187c92="" class="job-about-num-wrapper"><div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">61</div> <div data-v-fb03be90="" class="name">看过我</div></div> <div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">30</div> <div data-v-fb03be90="" class="name">沟通过</div></div> <div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">3</div> <div data-v-fb03be90="" class="name">感兴趣</div></div></div> <div data-v-6692a4a3="" data-v-37187c92="" class="job-status-wrapper"><div data-v-6692a4a3="" class="status-top"><div data-v-6692a4a3="" class="status-box status-opening"><span data-v-6692a4a3="" class="pointer" style="background: rgb(0, 190, 189);"></span>
            开放中
            <!----></div> <!----> <!----></div> <div data-v-6692a4a3="" class="status-bottom"><!----></div></div> <div data-v-37187c92="" class="jobInfo-container operation-container"><!----> <a data-v-37187c92="" href="javascript:;" class="position-edit opreat-btn">
                    编辑
                </a> <a data-v-37187c92="" href="javascript:;" class="position-close opreat-btn">
                    关闭
                </a> <!----> <div data-v-460bff2e="" data-v-37187c92="" class="job-operate-wrapper opreat-btn"><div data-v-460bff2e="" class="more-operate"><span data-v-460bff2e="" class="dot"><i data-v-460bff2e=""></i> <i data-v-460bff2e=""></i> <i data-v-460bff2e=""></i></span> <!----> <ul data-v-460bff2e="" class="job-operate-container"><li data-v-460bff2e="" class="job-operate-item">
                        预览
                        <!----></li><li data-v-460bff2e="" class="job-operate-share">
                        分享
                        <!----></li><li data-v-460bff2e="" class="job-operate-item">
                        复制
                        <span data-v-460bff2e="" class="new-icon">new</span></li><li data-v-460bff2e="" class="job-operate-item">
                        删除
                        <!----></li></ul></div></div></div></div> <!----></li></ul> <div data-v-e9324162="" class="page-content"><div data-v-e9324162="" class="total-num">
               共2个职位<!----></div> <div data-v-e9324162="" class="pager text-center"><!----> <div class="options-pages"><a href="javascript:;" class="disabled"><i class="ui-icon-arrow-left"></i></a> <a href="javascript:;" class="selected">1</a> <!---->  <!----> <!----> <a href="javascript:;" class="disabled"><i class="ui-icon-arrow-right"></i></a></div> <!----> <!----> <!----></div></div></div> <!----></div>
2025-08-21 16:08:28,554 - INFO - 在div.job-list-warp中找到 2 个li.job-jobInfo-warp 元素
2025-08-21 16:08:28,554 - INFO - 处理第 1 个职位...
2025-08-21 16:08:28,573 - INFO - 职位标题: AI产品经理
2025-08-21 16:08:28,618 - INFO - 职位信息: {'职位索引': 1, '职位名称': 'AI产品经理', '工作地点': '珠海', '工作经验': '3-5年', '学历要求': '本科', '薪资范围': '14-20K', '招聘类型': '全职'}
2025-08-21 16:08:28,688 - INFO - 已点击职位标题，进入职位详情页
2025-08-21 16:08:32,737 - INFO - 职位详情页内容: 岗位职责：
1. 负责AI技术在图帕斯户外产品中的应用规划，推动智能户外装备的创新与发展；
2. 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平；
3. 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行；
4. 持续跟踪市场趋势和技术发展，为产品迭代提供科学依据；
5. 与研发、设计、市场团队紧密合作，优化产品功能，增强用户体验。

任职要求：
1. 对AI技术及其应用场景有深刻理解，具备相关项目经验者优先；
2. 熟悉户外用品市场，了解用户痛点和需求，能够转化为产品特性；
3. 优秀的跨部门沟通与协调能力，能够推动多方合作达成目标；
4. 强烈的责任心和执行力，能够在快节奏环境中解决问题；
5. 具备良好的数据分析能力，能够通过数据驱动产品决策。
2025-08-21 16:08:32,752 - INFO - 已返回职位列表页
2025-08-21 16:08:33,781 - INFO - 处理第 2 个职位...
2025-08-21 16:08:33,793 - INFO - 职位标题: 人力资源主管
2025-08-21 16:08:33,824 - INFO - 职位信息: {'职位索引': 2, '职位名称': '人力资源主管', '工作地点': '珠海', '工作经验': '5-10年', '学历要求': '本科', '薪资范围': '6-7K', '招聘类型': '全职'}
2025-08-21 16:08:33,885 - INFO - 已点击职位标题，进入职位详情页
2025-08-21 16:08:37,920 - INFO - 职位详情页内容: 岗位职责：
1. 统筹公司人力资源管理工作，确保人力资源政策的有效执行；
2. 负责招聘、培训、绩效评估及员工关系等全员工生命周期的管理；
3. 设计和优化薪酬福利体系，提升员工满意度和公司竞争力；
4. 参与制定人力资源战略规划，支持公司业务发展及组织变革；
5. 监控部门运作效率，提出改进建议，优化工作流程。

任职要求：
1. 人力资源管理或相关专业本科及以上学历；
2. 5年以上人力资源相关工作经验，有主管或以上职位经验；
3. 熟悉人力资源各大模块，具备优秀的人才招聘、培训及绩效管理能力；
4. 具备良好的沟通协调能力和团队领导力，能够处理复杂的人事问题；
5. 对人力资源法律法规有深入了解，能够为公司提供合规的人力资源解决方案。
2025-08-21 16:08:37,936 - INFO - 已返回职位列表页
2025-08-21 16:08:38,962 - INFO - 成功提取到 2 个职位信息
2025-08-21 16:08:38,964 - INFO - 成功获取到 2 个职位信息
2025-08-21 16:08:38,964 - INFO - 用户信息已保存到文件: user_info_20250821_160838.json
2025-08-21 16:08:38,964 - INFO - 自动化程序执行完成，浏览器将保持打开状态
2025-08-21 16:08:38,964 - INFO - 您可以手动查看页面内容，完成后请关闭浏览器
2025-08-21 16:11:01,976 - INFO - 日志系统初始化成功
2025-08-21 16:11:01,976 - INFO - 开始运行BOSS直聘自动化程序 V2.0...
2025-08-21 16:11:02,015 - INFO - ====== WebDriver manager ======
2025-08-21 16:11:05,103 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 16:11:06,870 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 16:11:10,215 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\139.0.7258.138\chromedriver-win32/chromedriver.exe] found in cache
2025-08-21 16:11:12,213 - INFO - 使用自动下载的chromedriver
2025-08-21 16:11:12,256 - INFO - Chrome浏览器驱动设置成功
2025-08-21 16:11:12,256 - INFO - 访问登录页面: https://www.zhipin.com/web/user/?ka=header-login
2025-08-21 16:11:12,777 - INFO - 等待用户登录（超时时间：300秒）...
2025-08-21 16:11:12,777 - INFO - 请在浏览器中完成登录操作...
2025-08-21 16:11:32,447 - INFO - 检测到用户已登录，页面已跳转到聊天页面
2025-08-21 16:11:32,447 - INFO - 等待页面完全加载（超时时间：30秒）...
2025-08-21 16:11:35,480 - INFO - 页面加载完成
2025-08-21 16:11:36,494 - INFO - 检测到弹窗
2025-08-21 16:11:36,575 - INFO - 检测到弹窗并已关闭
2025-08-21 16:11:37,082 - INFO - 尝试点击个人中心...
2025-08-21 16:11:37,146 - INFO - 成功点击个人中心
2025-08-21 16:11:40,156 - INFO - 等待页面完全加载（超时时间：30秒）...
2025-08-21 16:11:43,166 - INFO - 页面加载完成
2025-08-21 16:11:43,166 - INFO - 开始提取用户信息...
2025-08-21 16:11:45,201 - INFO - 找到姓名: 杨锐雄
2025-08-21 16:11:45,225 - INFO - 找到公司名称: 图帕斯户外有限公司
2025-08-21 16:11:45,267 - INFO - 找到头像: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-08-21 16:11:45,268 - INFO - 用户信息已保存到文件: user_info_20250821_161145.json
2025-08-21 16:11:45,268 - INFO - ==================================================
2025-08-21 16:11:45,268 - INFO - 用户信息提取结果:
2025-08-21 16:11:45,268 - INFO - 姓名: 杨锐雄
2025-08-21 16:11:45,268 - INFO - 公司名称: 图帕斯户外有限公司
2025-08-21 16:11:45,268 - INFO - 头像: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-08-21 16:11:45,268 - INFO - ==================================================
2025-08-21 16:11:45,268 - INFO - 用户信息获取成功，开始点击职位管理按钮...
2025-08-21 16:11:45,269 - INFO - 尝试点击职位管理按钮...
2025-08-21 16:12:08,941 - INFO - 成功点击职位管理按钮，使用选择器: //a[contains(text(), '职位管理')]
2025-08-21 16:12:11,944 - INFO - 成功点击职位管理按钮
2025-08-21 16:12:14,956 - INFO - 开始提取职位列表信息...
2025-08-21 16:12:16,458 - INFO - 开始在iframe中查找职位列表...
2025-08-21 16:12:16,469 - INFO - 找到 1 个iframe
2025-08-21 16:12:16,481 - INFO - 已切换到第一个iframe
2025-08-21 16:12:16,513 - INFO - 第一个iframe下的div.job-list-warp内容: <div data-v-e9324162="" class="job-list-warp"><!----> <!----> <div data-v-e9324162="" class="list-inner-container"><!----> <ul data-v-e9324162="" class="job-list-content"><li data-v-37187c92="" data-v-e9324162="" data-id="41f8839f7881490003N63t6_EFJS" class="job-jobInfo-warp"><!----> <div data-v-37187c92="" class="single-check-box"></div> <div data-v-37187c92="" class="top-wrap"><div data-v-7344c8e0="" data-v-37187c92="" class="job-main-info-wrapper"><div data-v-7344c8e0="" class="job-title"><a data-v-7344c8e0="" href="javascript:;">AI产品经理</a> <div data-v-7344c8e0="" class="label-common label-normal">普</div> <!----> <!----> <!----> <!----></div> <div data-v-7344c8e0="" class="info-labels"><!----> <span data-v-7344c8e0="">珠海</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">3-5年</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">本科</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">14-20K</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">全职</span> <!----> <!----></div> <!----></div> <div data-v-fb03be90="" data-v-37187c92="" class="job-about-num-wrapper"><div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">17</div> <div data-v-fb03be90="" class="name">看过我</div></div> <div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">4</div> <div data-v-fb03be90="" class="name">沟通过</div></div> <div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">0</div> <div data-v-fb03be90="" class="name">感兴趣</div></div></div> <div data-v-6692a4a3="" data-v-37187c92="" class="job-status-wrapper"><div data-v-6692a4a3="" class="status-top"><div data-v-6692a4a3="" class="status-box status-opening"><span data-v-6692a4a3="" class="pointer" style="background: rgb(0, 190, 189);"></span>
            开放中
            <!----></div> <!----> <!----></div> <div data-v-6692a4a3="" class="status-bottom"><!----></div></div> <div data-v-37187c92="" class="jobInfo-container operation-container"><!----> <a data-v-37187c92="" href="javascript:;" class="position-edit opreat-btn">
                    编辑
                </a> <a data-v-37187c92="" href="javascript:;" class="position-close opreat-btn">
                    关闭
                </a> <!----> <div data-v-460bff2e="" data-v-37187c92="" class="job-operate-wrapper opreat-btn"><div data-v-460bff2e="" class="more-operate"><span data-v-460bff2e="" class="dot"><i data-v-460bff2e=""></i> <i data-v-460bff2e=""></i> <i data-v-460bff2e=""></i></span> <!----> <ul data-v-460bff2e="" class="job-operate-container"><li data-v-460bff2e="" class="job-operate-item">
                        预览
                        <!----></li><li data-v-460bff2e="" class="job-operate-share">
                        分享
                        <!----></li><li data-v-460bff2e="" class="job-operate-item">
                        复制
                        <span data-v-460bff2e="" class="new-icon">new</span></li><li data-v-460bff2e="" class="job-operate-item">
                        删除
                        <!----></li></ul></div></div></div></div> <!----></li><li data-v-37187c92="" data-v-e9324162="" data-id="52a786b20358e5e903N6396_GVdV" class="job-jobInfo-warp"><!----> <div data-v-37187c92="" class="single-check-box"></div> <div data-v-37187c92="" class="top-wrap"><div data-v-7344c8e0="" data-v-37187c92="" class="job-main-info-wrapper"><div data-v-7344c8e0="" class="job-title"><a data-v-7344c8e0="" href="javascript:;">人力资源主管</a> <div data-v-7344c8e0="" class="label-common label-normal">普</div> <!----> <!----> <!----> <!----></div> <div data-v-7344c8e0="" class="info-labels"><!----> <span data-v-7344c8e0="">珠海</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">5-10年</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">本科</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">6-7K</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">全职</span> <!----> <!----></div> <!----></div> <div data-v-fb03be90="" data-v-37187c92="" class="job-about-num-wrapper"><div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">61</div> <div data-v-fb03be90="" class="name">看过我</div></div> <div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">30</div> <div data-v-fb03be90="" class="name">沟通过</div></div> <div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">3</div> <div data-v-fb03be90="" class="name">感兴趣</div></div></div> <div data-v-6692a4a3="" data-v-37187c92="" class="job-status-wrapper"><div data-v-6692a4a3="" class="status-top"><div data-v-6692a4a3="" class="status-box status-opening"><span data-v-6692a4a3="" class="pointer" style="background: rgb(0, 190, 189);"></span>
            开放中
            <!----></div> <!----> <!----></div> <div data-v-6692a4a3="" class="status-bottom"><!----></div></div> <div data-v-37187c92="" class="jobInfo-container operation-container"><!----> <a data-v-37187c92="" href="javascript:;" class="position-edit opreat-btn">
                    编辑
                </a> <a data-v-37187c92="" href="javascript:;" class="position-close opreat-btn">
                    关闭
                </a> <!----> <div data-v-460bff2e="" data-v-37187c92="" class="job-operate-wrapper opreat-btn"><div data-v-460bff2e="" class="more-operate"><span data-v-460bff2e="" class="dot"><i data-v-460bff2e=""></i> <i data-v-460bff2e=""></i> <i data-v-460bff2e=""></i></span> <!----> <ul data-v-460bff2e="" class="job-operate-container"><li data-v-460bff2e="" class="job-operate-item">
                        预览
                        <!----></li><li data-v-460bff2e="" class="job-operate-share">
                        分享
                        <!----></li><li data-v-460bff2e="" class="job-operate-item">
                        复制
                        <span data-v-460bff2e="" class="new-icon">new</span></li><li data-v-460bff2e="" class="job-operate-item">
                        删除
                        <!----></li></ul></div></div></div></div> <!----></li></ul> <div data-v-e9324162="" class="page-content"><div data-v-e9324162="" class="total-num">
               共2个职位<!----></div> <div data-v-e9324162="" class="pager text-center"><!----> <div class="options-pages"><a href="javascript:;" class="disabled"><i class="ui-icon-arrow-left"></i></a> <a href="javascript:;" class="selected">1</a> <!---->  <!----> <!----> <a href="javascript:;" class="disabled"><i class="ui-icon-arrow-right"></i></a></div> <!----> <!----> <!----></div></div></div> <!----></div>
2025-08-21 16:12:16,519 - INFO - 在div.job-list-warp中找到 2 个li.job-jobInfo-warp 元素
2025-08-21 16:12:16,520 - INFO - 处理第 1 个职位...
2025-08-21 16:12:16,540 - INFO - 职位标题: AI产品经理
2025-08-21 16:12:16,574 - INFO - 职位信息: {'职位索引': 1, '职位名称': 'AI产品经理', '工作地点': '珠海', '工作经验': '3-5年', '学历要求': '本科', '薪资范围': '14-20K', '招聘类型': '全职'}
2025-08-21 16:12:16,639 - INFO - 已点击职位标题，进入职位详情页
2025-08-21 16:12:20,679 - INFO - 职位详情页内容: 岗位职责：
1. 负责AI技术在图帕斯户外产品中的应用规划，推动智能户外装备的创新与发展；
2. 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平；
3. 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行；
4. 持续跟踪市场趋势和技术发展，为产品迭代提供科学依据；
5. 与研发、设计、市场团队紧密合作，优化产品功能，增强用户体验。

任职要求：
1. 对AI技术及其应用场景有深刻理解，具备相关项目经验者优先；
2. 熟悉户外用品市场，了解用户痛点和需求，能够转化为产品特性；
3. 优秀的跨部门沟通与协调能力，能够推动多方合作达成目标；
4. 强烈的责任心和执行力，能够在快节奏环境中解决问题；
5. 具备良好的数据分析能力，能够通过数据驱动产品决策。
2025-08-21 16:12:20,693 - INFO - 已返回职位列表页
2025-08-21 16:12:21,721 - INFO - 处理第 2 个职位...
2025-08-21 16:12:21,734 - INFO - 职位标题: 人力资源主管
2025-08-21 16:12:21,787 - INFO - 职位信息: {'职位索引': 2, '职位名称': '人力资源主管', '工作地点': '珠海', '工作经验': '5-10年', '学历要求': '本科', '薪资范围': '6-7K', '招聘类型': '全职'}
2025-08-21 16:12:21,859 - INFO - 已点击职位标题，进入职位详情页
2025-08-21 16:12:25,898 - INFO - 职位详情页内容: 岗位职责：
1. 统筹公司人力资源管理工作，确保人力资源政策的有效执行；
2. 负责招聘、培训、绩效评估及员工关系等全员工生命周期的管理；
3. 设计和优化薪酬福利体系，提升员工满意度和公司竞争力；
4. 参与制定人力资源战略规划，支持公司业务发展及组织变革；
5. 监控部门运作效率，提出改进建议，优化工作流程。

任职要求：
1. 人力资源管理或相关专业本科及以上学历；
2. 5年以上人力资源相关工作经验，有主管或以上职位经验；
3. 熟悉人力资源各大模块，具备优秀的人才招聘、培训及绩效管理能力；
4. 具备良好的沟通协调能力和团队领导力，能够处理复杂的人事问题；
5. 对人力资源法律法规有深入了解，能够为公司提供合规的人力资源解决方案。
2025-08-21 16:12:25,911 - INFO - 已返回职位列表页
2025-08-21 16:12:26,940 - INFO - 成功提取到 2 个职位信息
2025-08-21 16:12:26,941 - INFO - 成功获取到 2 个职位信息
2025-08-21 16:12:26,942 - INFO - 用户信息已保存到文件: user_info_20250821_161226.json
2025-08-21 16:12:26,942 - INFO - 自动化程序执行完成，浏览器将保持打开状态
2025-08-21 16:12:26,942 - INFO - 您可以手动查看页面内容，完成后请关闭浏览器
2025-08-21 16:16:03,505 - INFO - 日志系统初始化成功
2025-08-21 16:16:03,505 - INFO - 开始运行BOSS直聘自动化程序 V2.0...
2025-08-21 16:16:03,550 - INFO - ====== WebDriver manager ======
2025-08-21 16:16:06,659 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 16:16:09,602 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 16:16:11,036 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\139.0.7258.138\chromedriver-win32/chromedriver.exe] found in cache
2025-08-21 16:16:13,090 - INFO - 使用自动下载的chromedriver
2025-08-21 16:16:13,123 - INFO - Chrome浏览器驱动设置成功
2025-08-21 16:16:13,123 - INFO - 访问登录页面: https://www.zhipin.com/web/user/?ka=header-login
2025-08-21 16:16:13,615 - INFO - 等待用户登录（超时时间：300秒）...
2025-08-21 16:16:13,615 - INFO - 请在浏览器中完成登录操作...
2025-08-21 16:16:21,837 - INFO - 检测到用户已登录，页面已跳转到聊天页面
2025-08-21 16:16:24,022 - ERROR - 自动化程序运行失败: 'BossAutomationV2' object has no attribute 'driver_class'
2025-08-21 16:16:24,022 - INFO - 自动化程序执行完成，浏览器将保持打开状态
2025-08-21 16:16:24,022 - INFO - 您可以手动查看页面内容，完成后请关闭浏览器
2025-08-21 16:21:16,076 - INFO - 日志系统初始化成功
2025-08-21 16:21:16,076 - INFO - 开始运行BOSS直聘自动化程序 V2.0...
2025-08-21 16:21:16,120 - INFO - ====== WebDriver manager ======
2025-08-21 16:21:19,340 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 16:21:20,872 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 16:21:21,960 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\139.0.7258.138\chromedriver-win32/chromedriver.exe] found in cache
2025-08-21 16:21:23,669 - INFO - 使用自动下载的chromedriver
2025-08-21 16:21:23,699 - INFO - Chrome浏览器驱动设置成功
2025-08-21 16:21:23,699 - INFO - 访问登录页面: https://www.zhipin.com/web/user/?ka=header-login
2025-08-21 16:21:24,177 - INFO - 等待用户登录（超时时间：300秒）...
2025-08-21 16:21:24,178 - INFO - 请在浏览器中完成登录操作...
2025-08-21 16:25:13,558 - INFO - 检测到用户已登录，页面已跳转到聊天页面
2025-08-21 16:25:43,103 - INFO - 自动化程序执行完成，浏览器将保持打开状态
2025-08-21 16:25:43,104 - INFO - 您可以手动查看页面内容，完成后请关闭浏览器
2025-08-21 16:25:55,619 - INFO - 日志系统初始化成功
2025-08-21 16:25:55,619 - INFO - 开始运行BOSS直聘自动化程序 V2.0...
2025-08-21 16:25:55,661 - INFO - ====== WebDriver manager ======
2025-08-21 16:25:58,820 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 16:25:59,799 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 16:26:00,805 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\139.0.7258.138\chromedriver-win32/chromedriver.exe] found in cache
2025-08-21 16:26:02,583 - INFO - 使用自动下载的chromedriver
2025-08-21 16:26:02,627 - INFO - Chrome浏览器驱动设置成功
2025-08-21 16:26:02,628 - INFO - 访问登录页面: https://www.zhipin.com/web/user/?ka=header-login
2025-08-21 16:26:03,117 - INFO - 等待用户登录（超时时间：300秒）...
2025-08-21 16:26:03,118 - INFO - 请在浏览器中完成登录操作...
2025-08-21 16:26:09,906 - INFO - 检测到用户已登录，页面已跳转到聊天页面
2025-08-21 16:26:21,440 - INFO - 已切换为无头模式，浏览器后台运行
2025-08-21 16:26:21,440 - INFO - 等待页面完全加载（超时时间：30秒）...
2025-08-21 16:26:24,451 - INFO - 页面加载完成
2025-08-21 16:26:25,474 - INFO - 尝试点击个人中心...
2025-08-21 16:26:45,506 - ERROR - 等待个人中心元素超时（20秒）
2025-08-21 16:26:45,507 - ERROR - 点击个人中心失败
2025-08-21 16:26:45,508 - INFO - 自动化程序执行完成，浏览器将保持打开状态
2025-08-21 16:26:45,508 - INFO - 您可以手动查看页面内容，完成后请关闭浏览器
2025-08-21 16:27:30,948 - INFO - 日志系统初始化成功
2025-08-21 16:27:30,948 - INFO - 开始运行BOSS直聘自动化程序 V2.0...
2025-08-21 16:27:30,988 - INFO - ====== WebDriver manager ======
2025-08-21 16:27:33,640 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 16:27:35,395 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 16:27:37,006 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\139.0.7258.138\chromedriver-win32/chromedriver.exe] found in cache
2025-08-21 16:27:38,697 - INFO - 使用自动下载的chromedriver
2025-08-21 16:27:38,744 - INFO - Chrome浏览器驱动设置成功
2025-08-21 16:27:38,744 - INFO - 访问登录页面: https://www.zhipin.com/web/user/?ka=header-login
2025-08-21 16:27:39,200 - INFO - 等待用户登录（超时时间：300秒）...
2025-08-21 16:27:39,200 - INFO - 请在浏览器中完成登录操作...
2025-08-21 16:27:48,004 - INFO - 检测到用户已登录，页面已跳转到聊天页面
2025-08-21 16:27:50,182 - ERROR - 自动化程序运行失败: 'BossAutomationV2' object has no attribute 'driver_class'
2025-08-21 16:27:50,182 - INFO - 自动化程序执行完成，浏览器将保持打开状态
2025-08-21 16:27:50,183 - INFO - 您可以手动查看页面内容，完成后请关闭浏览器
2025-08-21 16:35:12,362 - INFO - 日志系统初始化成功
2025-08-21 16:35:12,362 - INFO - 开始运行BOSS直聘自动化程序 V2.0...
2025-08-21 16:35:12,402 - INFO - ====== WebDriver manager ======
2025-08-21 16:35:15,594 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 16:35:20,890 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 16:35:21,982 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\139.0.7258.138\chromedriver-win32/chromedriver.exe] found in cache
2025-08-21 16:35:23,691 - INFO - 使用自动下载的chromedriver
2025-08-21 16:35:23,738 - INFO - Chrome浏览器驱动设置成功
2025-08-21 16:35:23,739 - INFO - 访问登录页面: https://www.zhipin.com/web/user/?ka=header-login
2025-08-21 16:35:24,553 - INFO - 等待用户登录（超时时间：300秒）...
2025-08-21 16:35:24,553 - INFO - 请在浏览器中完成登录操作...
2025-08-21 16:35:34,098 - INFO - 检测到用户已登录，页面已跳转到聊天页面
2025-08-21 16:35:36,277 - ERROR - 自动化程序运行失败: 'BossAutomationV2' object has no attribute 'driver_class'
2025-08-21 16:35:36,277 - INFO - 自动化程序执行完成，浏览器将保持打开状态
2025-08-21 16:35:36,278 - INFO - 您可以手动查看页面内容，完成后请关闭浏览器
2025-08-21 16:36:40,408 - INFO - 日志系统初始化成功
2025-08-21 16:36:40,408 - INFO - 开始运行BOSS直聘自动化程序 V2.0...
2025-08-21 16:36:40,448 - INFO - ====== WebDriver manager ======
2025-08-21 16:36:43,370 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 16:36:45,301 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 16:36:46,199 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\139.0.7258.138\chromedriver-win32/chromedriver.exe] found in cache
2025-08-21 16:36:47,942 - INFO - 使用自动下载的chromedriver
2025-08-21 16:36:47,979 - INFO - Chrome浏览器驱动设置成功
2025-08-21 16:36:47,979 - INFO - 访问登录页面: https://www.zhipin.com/web/user/?ka=header-login
2025-08-21 16:36:48,566 - INFO - 等待用户登录（超时时间：300秒）...
2025-08-21 16:36:48,566 - INFO - 请在浏览器中完成登录操作...
2025-08-21 16:36:54,662 - INFO - 检测到用户已登录，页面已跳转到聊天页面
2025-08-21 16:36:54,663 - INFO - 等待页面完全加载（超时时间：30秒）...
2025-08-21 16:36:57,690 - INFO - 页面加载完成
2025-08-21 16:36:58,709 - INFO - 检测到弹窗
2025-08-21 16:36:58,771 - INFO - 检测到弹窗并已关闭
2025-08-21 16:36:59,274 - INFO - 尝试点击个人中心...
2025-08-21 16:36:59,333 - INFO - 成功点击个人中心
2025-08-21 16:37:02,337 - INFO - 等待页面完全加载（超时时间：30秒）...
2025-08-21 16:37:05,349 - INFO - 页面加载完成
2025-08-21 16:37:05,349 - INFO - 开始提取用户信息...
2025-08-21 16:37:07,375 - INFO - 找到姓名: 杨锐雄
2025-08-21 16:37:07,394 - INFO - 找到公司名称: 图帕斯户外有限公司
2025-08-21 16:37:07,412 - INFO - 找到头像: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-08-21 16:37:07,413 - INFO - 用户信息已保存到文件: user_info_20250821_163707.json
2025-08-21 16:37:07,413 - INFO - ==================================================
2025-08-21 16:37:07,413 - INFO - 用户信息提取结果:
2025-08-21 16:37:07,413 - INFO - 姓名: 杨锐雄
2025-08-21 16:37:07,413 - INFO - 公司名称: 图帕斯户外有限公司
2025-08-21 16:37:07,413 - INFO - 头像: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-08-21 16:37:07,413 - INFO - ==================================================
2025-08-21 16:37:07,413 - INFO - 用户信息获取成功，开始点击职位管理按钮...
2025-08-21 16:37:07,413 - INFO - 尝试点击职位管理按钮...
2025-08-21 16:37:31,107 - INFO - 成功点击职位管理按钮，使用选择器: //a[contains(text(), '职位管理')]
2025-08-21 16:37:34,116 - INFO - 成功点击职位管理按钮
2025-08-21 16:37:37,122 - INFO - 开始提取职位列表信息...
2025-08-21 16:37:38,627 - INFO - 开始在iframe中查找职位列表...
2025-08-21 16:37:38,634 - INFO - 找到 1 个iframe
2025-08-21 16:37:38,646 - INFO - 已切换到第一个iframe
2025-08-21 16:37:38,659 - INFO - 第一个iframe下的div.job-list-warp内容: <div data-v-e9324162="" class="job-list-warp"><!----> <!----> <div data-v-e9324162="" class="list-inner-container"><!----> <ul data-v-e9324162="" class="job-list-content"><li data-v-37187c92="" data-v-e9324162="" data-id="41f8839f7881490003N63t6_EFJS" class="job-jobInfo-warp"><!----> <div data-v-37187c92="" class="single-check-box"></div> <div data-v-37187c92="" class="top-wrap"><div data-v-7344c8e0="" data-v-37187c92="" class="job-main-info-wrapper"><div data-v-7344c8e0="" class="job-title"><a data-v-7344c8e0="" href="javascript:;">AI产品经理</a> <div data-v-7344c8e0="" class="label-common label-normal">普</div> <!----> <!----> <!----> <!----></div> <div data-v-7344c8e0="" class="info-labels"><!----> <span data-v-7344c8e0="">珠海</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">3-5年</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">本科</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">14-20K</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">全职</span> <!----> <!----></div> <!----></div> <div data-v-fb03be90="" data-v-37187c92="" class="job-about-num-wrapper"><div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">17</div> <div data-v-fb03be90="" class="name">看过我</div></div> <div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">4</div> <div data-v-fb03be90="" class="name">沟通过</div></div> <div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">0</div> <div data-v-fb03be90="" class="name">感兴趣</div></div></div> <div data-v-6692a4a3="" data-v-37187c92="" class="job-status-wrapper"><div data-v-6692a4a3="" class="status-top"><div data-v-6692a4a3="" class="status-box status-opening"><span data-v-6692a4a3="" class="pointer" style="background: rgb(0, 190, 189);"></span>
            开放中
            <!----></div> <!----> <!----></div> <div data-v-6692a4a3="" class="status-bottom"><!----></div></div> <div data-v-37187c92="" class="jobInfo-container operation-container"><!----> <a data-v-37187c92="" href="javascript:;" class="position-edit opreat-btn">
                    编辑
                </a> <a data-v-37187c92="" href="javascript:;" class="position-close opreat-btn">
                    关闭
                </a> <!----> <div data-v-460bff2e="" data-v-37187c92="" class="job-operate-wrapper opreat-btn"><div data-v-460bff2e="" class="more-operate"><span data-v-460bff2e="" class="dot"><i data-v-460bff2e=""></i> <i data-v-460bff2e=""></i> <i data-v-460bff2e=""></i></span> <!----> <ul data-v-460bff2e="" class="job-operate-container"><li data-v-460bff2e="" class="job-operate-item">
                        预览
                        <!----></li><li data-v-460bff2e="" class="job-operate-share">
                        分享
                        <!----></li><li data-v-460bff2e="" class="job-operate-item">
                        复制
                        <span data-v-460bff2e="" class="new-icon">new</span></li><li data-v-460bff2e="" class="job-operate-item">
                        删除
                        <!----></li></ul></div></div></div></div> <!----></li><li data-v-37187c92="" data-v-e9324162="" data-id="52a786b20358e5e903N6396_GVdV" class="job-jobInfo-warp"><!----> <div data-v-37187c92="" class="single-check-box"></div> <div data-v-37187c92="" class="top-wrap"><div data-v-7344c8e0="" data-v-37187c92="" class="job-main-info-wrapper"><div data-v-7344c8e0="" class="job-title"><a data-v-7344c8e0="" href="javascript:;">人力资源主管</a> <div data-v-7344c8e0="" class="label-common label-normal">普</div> <!----> <!----> <!----> <!----></div> <div data-v-7344c8e0="" class="info-labels"><!----> <span data-v-7344c8e0="">珠海</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">5-10年</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">本科</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">6-7K</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">全职</span> <!----> <!----></div> <!----></div> <div data-v-fb03be90="" data-v-37187c92="" class="job-about-num-wrapper"><div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">61</div> <div data-v-fb03be90="" class="name">看过我</div></div> <div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">30</div> <div data-v-fb03be90="" class="name">沟通过</div></div> <div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">3</div> <div data-v-fb03be90="" class="name">感兴趣</div></div></div> <div data-v-6692a4a3="" data-v-37187c92="" class="job-status-wrapper"><div data-v-6692a4a3="" class="status-top"><div data-v-6692a4a3="" class="status-box status-opening"><span data-v-6692a4a3="" class="pointer" style="background: rgb(0, 190, 189);"></span>
            开放中
            <!----></div> <!----> <!----></div> <div data-v-6692a4a3="" class="status-bottom"><!----></div></div> <div data-v-37187c92="" class="jobInfo-container operation-container"><!----> <a data-v-37187c92="" href="javascript:;" class="position-edit opreat-btn">
                    编辑
                </a> <a data-v-37187c92="" href="javascript:;" class="position-close opreat-btn">
                    关闭
                </a> <!----> <div data-v-460bff2e="" data-v-37187c92="" class="job-operate-wrapper opreat-btn"><div data-v-460bff2e="" class="more-operate"><span data-v-460bff2e="" class="dot"><i data-v-460bff2e=""></i> <i data-v-460bff2e=""></i> <i data-v-460bff2e=""></i></span> <!----> <ul data-v-460bff2e="" class="job-operate-container"><li data-v-460bff2e="" class="job-operate-item">
                        预览
                        <!----></li><li data-v-460bff2e="" class="job-operate-share">
                        分享
                        <!----></li><li data-v-460bff2e="" class="job-operate-item">
                        复制
                        <span data-v-460bff2e="" class="new-icon">new</span></li><li data-v-460bff2e="" class="job-operate-item">
                        删除
                        <!----></li></ul></div></div></div></div> <!----></li></ul> <div data-v-e9324162="" class="page-content"><div data-v-e9324162="" class="total-num">
               共2个职位<!----></div> <div data-v-e9324162="" class="pager text-center"><!----> <div class="options-pages"><a href="javascript:;" class="disabled"><i class="ui-icon-arrow-left"></i></a> <a href="javascript:;" class="selected">1</a> <!---->  <!----> <!----> <a href="javascript:;" class="disabled"><i class="ui-icon-arrow-right"></i></a></div> <!----> <!----> <!----></div></div></div> <!----></div>
2025-08-21 16:37:38,667 - INFO - 在div.job-list-warp中找到 2 个li.job-jobInfo-warp 元素
2025-08-21 16:37:38,667 - INFO - 处理第 1 个职位...
2025-08-21 16:37:38,687 - INFO - 职位标题: AI产品经理
2025-08-21 16:37:38,742 - INFO - 职位信息: {'职位索引': 1, '职位名称': 'AI产品经理', '工作地点': '珠海', '工作经验': '3-5年', '学历要求': '本科', '薪资范围': '14-20K', '招聘类型': '全职'}
2025-08-21 16:37:38,839 - INFO - 已点击职位标题，进入职位详情页
2025-08-21 16:37:42,888 - INFO - 职位详情页内容: 岗位职责：
1. 负责AI技术在图帕斯户外产品中的应用规划，推动智能户外装备的创新与发展；
2. 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平；
3. 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行；
4. 持续跟踪市场趋势和技术发展，为产品迭代提供科学依据；
5. 与研发、设计、市场团队紧密合作，优化产品功能，增强用户体验。

任职要求：
1. 对AI技术及其应用场景有深刻理解，具备相关项目经验者优先；
2. 熟悉户外用品市场，了解用户痛点和需求，能够转化为产品特性；
3. 优秀的跨部门沟通与协调能力，能够推动多方合作达成目标；
4. 强烈的责任心和执行力，能够在快节奏环境中解决问题；
5. 具备良好的数据分析能力，能够通过数据驱动产品决策。
2025-08-21 16:37:42,900 - INFO - 已返回职位列表页
2025-08-21 16:37:43,932 - INFO - 处理第 2 个职位...
2025-08-21 16:37:43,943 - INFO - 职位标题: 人力资源主管
2025-08-21 16:37:43,972 - INFO - 职位信息: {'职位索引': 2, '职位名称': '人力资源主管', '工作地点': '珠海', '工作经验': '5-10年', '学历要求': '本科', '薪资范围': '6-7K', '招聘类型': '全职'}
2025-08-21 16:37:44,035 - INFO - 已点击职位标题，进入职位详情页
2025-08-21 16:37:48,077 - INFO - 职位详情页内容: 岗位职责：
1. 统筹公司人力资源管理工作，确保人力资源政策的有效执行；
2. 负责招聘、培训、绩效评估及员工关系等全员工生命周期的管理；
3. 设计和优化薪酬福利体系，提升员工满意度和公司竞争力；
4. 参与制定人力资源战略规划，支持公司业务发展及组织变革；
5. 监控部门运作效率，提出改进建议，优化工作流程。

任职要求：
1. 人力资源管理或相关专业本科及以上学历；
2. 5年以上人力资源相关工作经验，有主管或以上职位经验；
3. 熟悉人力资源各大模块，具备优秀的人才招聘、培训及绩效管理能力；
4. 具备良好的沟通协调能力和团队领导力，能够处理复杂的人事问题；
5. 对人力资源法律法规有深入了解，能够为公司提供合规的人力资源解决方案。
2025-08-21 16:37:48,089 - INFO - 已返回职位列表页
2025-08-21 16:37:49,111 - INFO - 成功提取到 2 个职位信息
2025-08-21 16:37:49,113 - INFO - 成功获取到 2 个职位信息
2025-08-21 16:37:49,114 - INFO - 用户信息已保存到文件: user_info_20250821_163749.json
2025-08-21 16:37:49,114 - INFO - 自动化程序执行完成，浏览器将保持打开状态
2025-08-21 16:37:49,114 - INFO - 您可以手动查看页面内容，完成后请关闭浏览器
2025-08-21 16:44:13,933 - INFO - 日志系统初始化成功
2025-08-21 16:44:13,933 - INFO - 开始运行BOSS直聘自动化程序 V2.0...
2025-08-21 16:44:13,975 - INFO - ====== WebDriver manager ======
2025-08-21 16:44:17,186 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 16:44:17,753 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 16:44:18,496 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\139.0.7258.138\chromedriver-win32/chromedriver.exe] found in cache
2025-08-21 16:44:20,398 - INFO - 使用自动下载的chromedriver
2025-08-21 16:44:20,428 - INFO - Chrome浏览器驱动设置成功
2025-08-21 16:44:20,429 - INFO - 访问登录页面: https://www.zhipin.com/web/user/?ka=header-login
2025-08-21 16:44:21,111 - INFO - 等待用户登录（超时时间：300秒）...
2025-08-21 16:44:21,111 - INFO - 请在浏览器中完成登录操作...
2025-08-21 16:44:32,259 - INFO - 检测到用户已登录，页面已跳转到聊天页面
2025-08-21 16:44:43,823 - INFO - 已切换为无头模式，浏览器后台运行
2025-08-21 16:44:43,824 - INFO - 等待页面完全加载（超时时间：30秒）...
2025-08-21 16:44:46,840 - INFO - 页面加载完成
2025-08-21 16:44:47,863 - INFO - 尝试点击个人中心...
2025-08-21 16:45:07,904 - ERROR - 等待个人中心元素超时（20秒）
2025-08-21 16:45:07,904 - ERROR - 点击个人中心失败
2025-08-21 16:45:07,904 - INFO - 自动化程序执行完成，浏览器将保持打开状态
2025-08-21 16:45:07,904 - INFO - 您可以手动查看页面内容，完成后请关闭浏览器
2025-08-21 16:55:33,135 - INFO - 日志系统初始化成功
2025-08-21 16:55:33,136 - INFO - 开始运行BOSS直聘自动化程序 V2.0...
2025-08-21 16:55:33,175 - INFO - ====== WebDriver manager ======
2025-08-21 16:55:36,428 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 16:55:37,395 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 16:55:38,298 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\139.0.7258.138\chromedriver-win32/chromedriver.exe] found in cache
2025-08-21 16:55:40,381 - INFO - 使用自动下载的chromedriver
2025-08-21 16:55:40,426 - INFO - Chrome浏览器驱动设置成功
2025-08-21 16:55:40,426 - INFO - 访问登录页面: https://www.zhipin.com/web/user/?ka=header-login
2025-08-21 16:55:41,034 - INFO - 等待用户登录（超时时间：300秒）...
2025-08-21 16:55:41,034 - INFO - 请在浏览器中完成登录操作...
2025-08-21 16:55:50,273 - ERROR - 等待用户登录时发生错误: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=139.0.7258.128)
Stacktrace:
	GetHandleVerifier [0x0x7affc3+65331]
	GetHandleVerifier [0x0x7b0004+65396]
	(No symbol) [0x0x5a3f63]
	(No symbol) [0x0x5938e0]
	(No symbol) [0x0x5b1709]
	(No symbol) [0x0x617e4c]
	(No symbol) [0x0x6324d9]
	(No symbol) [0x0x6112d6]
	(No symbol) [0x0x5e0910]
	(No symbol) [0x0x5e1784]
	GetHandleVerifier [0x0x9f38b3+2439203]
	GetHandleVerifier [0x0x9eeae2+2419282]
	GetHandleVerifier [0x0x7d712a+225434]
	GetHandleVerifier [0x0x7c6e08+159096]
	GetHandleVerifier [0x0x7cdd5d+187597]
	GetHandleVerifier [0x0x7b7ad8+96840]
	GetHandleVerifier [0x0x7b7c62+97234]
	GetHandleVerifier [0x0x7a277a+9962]
	BaseThreadInitThunk [0x0x76da5d49+25]
	RtlInitializeExceptionChain [0x0x77d4d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x77d4d281+561]

2025-08-21 16:55:50,273 - ERROR - 用户登录失败或超时
2025-08-21 16:55:50,273 - INFO - 自动化程序执行完成，浏览器将保持打开状态
2025-08-21 16:55:50,273 - INFO - 您可以手动查看页面内容，完成后请关闭浏览器
2025-08-21 16:55:55,617 - INFO - 日志系统初始化成功
2025-08-21 16:55:55,617 - INFO - 开始运行BOSS直聘自动化程序 V2.0...
2025-08-21 16:55:55,655 - INFO - ====== WebDriver manager ======
2025-08-21 16:55:58,640 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 16:56:01,190 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 16:56:03,474 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\139.0.7258.138\chromedriver-win32/chromedriver.exe] found in cache
2025-08-21 16:56:05,132 - INFO - 使用自动下载的chromedriver
2025-08-21 16:56:05,177 - INFO - Chrome浏览器驱动设置成功
2025-08-21 16:56:05,177 - INFO - 访问登录页面: https://www.zhipin.com/web/user/?ka=header-login
2025-08-21 16:56:05,733 - INFO - 等待用户登录（超时时间：300秒）...
2025-08-21 16:56:05,734 - INFO - 请在浏览器中完成登录操作...
2025-08-21 16:56:13,125 - INFO - 检测到用户已登录，页面已跳转到聊天页面
2025-08-21 16:56:13,125 - ERROR - 自动化程序运行失败: name 'chrome_options' is not defined
2025-08-21 16:56:13,126 - INFO - 自动化程序执行完成，浏览器将保持打开状态
2025-08-21 16:56:13,126 - INFO - 您可以手动查看页面内容，完成后请关闭浏览器
2025-08-21 16:57:12,549 - INFO - 日志系统初始化成功
2025-08-21 16:57:12,550 - INFO - 开始运行BOSS直聘自动化程序 V2.0...
2025-08-21 16:57:12,592 - INFO - ====== WebDriver manager ======
2025-08-21 16:57:15,664 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 16:57:17,582 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 16:57:18,444 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\139.0.7258.138\chromedriver-win32/chromedriver.exe] found in cache
2025-08-21 16:57:20,095 - INFO - 使用自动下载的chromedriver
2025-08-21 16:57:20,126 - INFO - Chrome浏览器驱动设置成功
2025-08-21 16:57:20,127 - INFO - 访问登录页面: https://www.zhipin.com/web/user/?ka=header-login
2025-08-21 16:57:20,731 - INFO - 等待用户登录（超时时间：300秒）...
2025-08-21 16:57:20,731 - INFO - 请在浏览器中完成登录操作...
2025-08-21 16:57:26,724 - INFO - 检测到用户已登录，页面已跳转到聊天页面
2025-08-21 16:57:26,724 - INFO - 等待页面完全加载（超时时间：30秒）...
2025-08-21 16:57:29,741 - INFO - 页面加载完成
2025-08-21 16:57:30,763 - INFO - 检测到弹窗
2025-08-21 16:57:30,810 - INFO - 检测到弹窗并已关闭
2025-08-21 16:57:31,318 - INFO - 尝试点击个人中心...
2025-08-21 16:57:31,383 - INFO - 成功点击个人中心
2025-08-21 16:57:34,395 - INFO - 等待页面完全加载（超时时间：30秒）...
2025-08-21 16:57:37,409 - INFO - 页面加载完成
2025-08-21 16:57:37,410 - INFO - 开始提取用户信息...
2025-08-21 16:57:39,442 - INFO - 找到姓名: 杨锐雄
2025-08-21 16:57:39,462 - INFO - 找到公司名称: 图帕斯户外有限公司
2025-08-21 16:57:39,477 - INFO - 找到头像: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-08-21 16:57:39,478 - INFO - 用户信息已保存到文件: user_info_20250821_165739.json
2025-08-21 16:57:39,479 - INFO - ==================================================
2025-08-21 16:57:39,479 - INFO - 用户信息提取结果:
2025-08-21 16:57:39,479 - INFO - 姓名: 杨锐雄
2025-08-21 16:57:39,479 - INFO - 公司名称: 图帕斯户外有限公司
2025-08-21 16:57:39,479 - INFO - 头像: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-08-21 16:57:39,479 - INFO - ==================================================
2025-08-21 16:57:39,479 - INFO - 用户信息获取成功，开始点击职位管理按钮...
2025-08-21 16:57:39,479 - INFO - 尝试点击职位管理按钮...
2025-08-21 16:57:54,357 - WARNING - 使用选择器 //div[contains(text(), '职位管理')] 点击时发生错误: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=139.0.7258.128)
Stacktrace:
	GetHandleVerifier [0x0x7affc3+65331]
	GetHandleVerifier [0x0x7b0004+65396]
	(No symbol) [0x0x5a3f63]
	(No symbol) [0x0x5938e0]
	(No symbol) [0x0x5b1709]
	(No symbol) [0x0x617e4c]
	(No symbol) [0x0x6324d9]
	(No symbol) [0x0x6112d6]
	(No symbol) [0x0x5e0910]
	(No symbol) [0x0x5e1784]
	GetHandleVerifier [0x0x9f38b3+2439203]
	GetHandleVerifier [0x0x9eeae2+2419282]
	GetHandleVerifier [0x0x7d712a+225434]
	GetHandleVerifier [0x0x7c6e08+159096]
	GetHandleVerifier [0x0x7cdd5d+187597]
	GetHandleVerifier [0x0x7b7ad8+96840]
	GetHandleVerifier [0x0x7b7c62+97234]
	GetHandleVerifier [0x0x7a277a+9962]
	BaseThreadInitThunk [0x0x76da5d49+25]
	RtlInitializeExceptionChain [0x0x77d4d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x77d4d281+561]

2025-08-21 16:57:54,513 - WARNING - 使用选择器 //a[contains(text(), '职位管理')] 点击时发生错误: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7affc3+65331]
	GetHandleVerifier [0x0x7b0004+65396]
	(No symbol) [0x0x5a3dc0]
	(No symbol) [0x0x5dfbd8]
	(No symbol) [0x0x611396]
	(No symbol) [0x0x60cf70]
	(No symbol) [0x0x60c4f6]
	(No symbol) [0x0x5759b5]
	(No symbol) [0x0x575f0e]
	(No symbol) [0x0x57639d]
	GetHandleVerifier [0x0x9f38b3+2439203]
	GetHandleVerifier [0x0x9eeae2+2419282]
	GetHandleVerifier [0x0x7d712a+225434]
	GetHandleVerifier [0x0x7c6e08+159096]
	GetHandleVerifier [0x0x7cdd5d+187597]
	(No symbol) [0x0x575680]
	(No symbol) [0x0x574e8e]
	GetHandleVerifier [0x0xb0a2ec+3580508]
	BaseThreadInitThunk [0x0x76da5d49+25]
	RtlInitializeExceptionChain [0x0x77d4d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x77d4d281+561]

2025-08-21 16:57:54,514 - WARNING - 使用选择器 //button[contains(text(), '职位管理')] 点击时发生错误: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7affc3+65331]
	GetHandleVerifier [0x0x7b0004+65396]
	(No symbol) [0x0x5a3dc0]
	(No symbol) [0x0x5dfbd8]
	(No symbol) [0x0x611396]
	(No symbol) [0x0x60cf70]
	(No symbol) [0x0x60c4f6]
	(No symbol) [0x0x5759b5]
	(No symbol) [0x0x575f0e]
	(No symbol) [0x0x57639d]
	GetHandleVerifier [0x0x9f38b3+2439203]
	GetHandleVerifier [0x0x9eeae2+2419282]
	GetHandleVerifier [0x0x7d712a+225434]
	GetHandleVerifier [0x0x7c6e08+159096]
	GetHandleVerifier [0x0x7cdd5d+187597]
	(No symbol) [0x0x575680]
	(No symbol) [0x0x574e8e]
	GetHandleVerifier [0x0xb0a2ec+3580508]
	BaseThreadInitThunk [0x0x76da5d49+25]
	RtlInitializeExceptionChain [0x0x77d4d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x77d4d281+561]

2025-08-21 16:57:54,516 - WARNING - 使用选择器 //span[contains(@class, 'job-manage')] 点击时发生错误: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7affc3+65331]
	GetHandleVerifier [0x0x7b0004+65396]
	(No symbol) [0x0x5a3dc0]
	(No symbol) [0x0x5dfbd8]
	(No symbol) [0x0x611396]
	(No symbol) [0x0x60cf70]
	(No symbol) [0x0x60c4f6]
	(No symbol) [0x0x5759b5]
	(No symbol) [0x0x575f0e]
	(No symbol) [0x0x57639d]
	GetHandleVerifier [0x0x9f38b3+2439203]
	GetHandleVerifier [0x0x9eeae2+2419282]
	GetHandleVerifier [0x0x7d712a+225434]
	GetHandleVerifier [0x0x7c6e08+159096]
	GetHandleVerifier [0x0x7cdd5d+187597]
	(No symbol) [0x0x575680]
	(No symbol) [0x0x574e8e]
	GetHandleVerifier [0x0xb0a2ec+3580508]
	BaseThreadInitThunk [0x0x76da5d49+25]
	RtlInitializeExceptionChain [0x0x77d4d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x77d4d281+561]

2025-08-21 16:57:54,517 - WARNING - 使用选择器 //div[contains(@class, 'job-manage')] 点击时发生错误: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7affc3+65331]
	GetHandleVerifier [0x0x7b0004+65396]
	(No symbol) [0x0x5a3dc0]
	(No symbol) [0x0x5dfbd8]
	(No symbol) [0x0x611396]
	(No symbol) [0x0x60cf70]
	(No symbol) [0x0x60c4f6]
	(No symbol) [0x0x5759b5]
	(No symbol) [0x0x575f0e]
	(No symbol) [0x0x57639d]
	GetHandleVerifier [0x0x9f38b3+2439203]
	GetHandleVerifier [0x0x9eeae2+2419282]
	GetHandleVerifier [0x0x7d712a+225434]
	GetHandleVerifier [0x0x7c6e08+159096]
	GetHandleVerifier [0x0x7cdd5d+187597]
	(No symbol) [0x0x575680]
	(No symbol) [0x0x574e8e]
	GetHandleVerifier [0x0xb0a2ec+3580508]
	BaseThreadInitThunk [0x0x76da5d49+25]
	RtlInitializeExceptionChain [0x0x77d4d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x77d4d281+561]

2025-08-21 16:57:54,519 - WARNING - 使用选择器 //a[contains(@class, 'job-manage')] 点击时发生错误: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7affc3+65331]
	GetHandleVerifier [0x0x7b0004+65396]
	(No symbol) [0x0x5a3dc0]
	(No symbol) [0x0x5dfbd8]
	(No symbol) [0x0x611396]
	(No symbol) [0x0x60cf70]
	(No symbol) [0x0x60c4f6]
	(No symbol) [0x0x5759b5]
	(No symbol) [0x0x575f0e]
	(No symbol) [0x0x57639d]
	GetHandleVerifier [0x0x9f38b3+2439203]
	GetHandleVerifier [0x0x9eeae2+2419282]
	GetHandleVerifier [0x0x7d712a+225434]
	GetHandleVerifier [0x0x7c6e08+159096]
	GetHandleVerifier [0x0x7cdd5d+187597]
	(No symbol) [0x0x575680]
	(No symbol) [0x0x574e8e]
	GetHandleVerifier [0x0xb0a2ec+3580508]
	BaseThreadInitThunk [0x0x76da5d49+25]
	RtlInitializeExceptionChain [0x0x77d4d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x77d4d281+561]

2025-08-21 16:57:54,520 - WARNING - 使用选择器 button[contains(@class, 'job-manage')] 点击时发生错误: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7affc3+65331]
	GetHandleVerifier [0x0x7b0004+65396]
	(No symbol) [0x0x5a3dc0]
	(No symbol) [0x0x5dfbd8]
	(No symbol) [0x0x611396]
	(No symbol) [0x0x60cf70]
	(No symbol) [0x0x60c4f6]
	(No symbol) [0x0x5759b5]
	(No symbol) [0x0x575f0e]
	(No symbol) [0x0x57639d]
	GetHandleVerifier [0x0x9f38b3+2439203]
	GetHandleVerifier [0x0x9eeae2+2419282]
	GetHandleVerifier [0x0x7d712a+225434]
	GetHandleVerifier [0x0x7c6e08+159096]
	GetHandleVerifier [0x0x7cdd5d+187597]
	(No symbol) [0x0x575680]
	(No symbol) [0x0x574e8e]
	GetHandleVerifier [0x0xb0a2ec+3580508]
	BaseThreadInitThunk [0x0x76da5d49+25]
	RtlInitializeExceptionChain [0x0x77d4d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x77d4d281+561]

2025-08-21 16:57:54,522 - WARNING - 使用选择器 //*[contains(text(), '职位管理')] 点击时发生错误: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7affc3+65331]
	GetHandleVerifier [0x0x7b0004+65396]
	(No symbol) [0x0x5a3dc0]
	(No symbol) [0x0x5dfbd8]
	(No symbol) [0x0x611396]
	(No symbol) [0x0x60cf70]
	(No symbol) [0x0x60c4f6]
	(No symbol) [0x0x5759b5]
	(No symbol) [0x0x575f0e]
	(No symbol) [0x0x57639d]
	GetHandleVerifier [0x0x9f38b3+2439203]
	GetHandleVerifier [0x0x9eeae2+2419282]
	GetHandleVerifier [0x0x7d712a+225434]
	GetHandleVerifier [0x0x7c6e08+159096]
	GetHandleVerifier [0x0x7cdd5d+187597]
	(No symbol) [0x0x575680]
	(No symbol) [0x0x574e8e]
	GetHandleVerifier [0x0xb0a2ec+3580508]
	BaseThreadInitThunk [0x0x76da5d49+25]
	RtlInitializeExceptionChain [0x0x77d4d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x77d4d281+561]

2025-08-21 16:57:54,522 - WARNING - 常规方法未找到职位管理按钮，尝试其他方法...
2025-08-21 16:57:54,524 - ERROR - 未找到可点击的职位管理按钮
2025-08-21 16:57:54,524 - WARNING - 点击职位管理按钮失败
2025-08-21 16:57:54,524 - INFO - 自动化程序执行完成，浏览器将保持打开状态
2025-08-21 16:57:54,525 - INFO - 您可以手动查看页面内容，完成后请关闭浏览器
2025-08-21 16:58:01,523 - INFO - 日志系统初始化成功
2025-08-21 16:58:01,524 - INFO - 开始运行BOSS直聘自动化程序 V2.0...
2025-08-21 16:58:01,561 - INFO - ====== WebDriver manager ======
2025-08-21 16:58:04,502 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 16:58:05,450 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 16:58:06,749 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\139.0.7258.138\chromedriver-win32/chromedriver.exe] found in cache
2025-08-21 16:58:08,527 - INFO - 使用自动下载的chromedriver
2025-08-21 16:58:08,555 - INFO - Chrome浏览器驱动设置成功
2025-08-21 16:58:08,555 - INFO - 访问登录页面: https://www.zhipin.com/web/user/?ka=header-login
2025-08-21 16:58:09,072 - INFO - 等待用户登录（超时时间：300秒）...
2025-08-21 16:58:09,072 - INFO - 请在浏览器中完成登录操作...
2025-08-21 17:00:13,739 - ERROR - 等待用户登录时发生错误: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=139.0.7258.128)
Stacktrace:
	GetHandleVerifier [0x0x7affc3+65331]
	GetHandleVerifier [0x0x7b0004+65396]
	(No symbol) [0x0x5a3f63]
	(No symbol) [0x0x5938e0]
	(No symbol) [0x0x5b1709]
	(No symbol) [0x0x617e4c]
	(No symbol) [0x0x6324d9]
	(No symbol) [0x0x6112d6]
	(No symbol) [0x0x5e0910]
	(No symbol) [0x0x5e1784]
	GetHandleVerifier [0x0x9f38b3+2439203]
	GetHandleVerifier [0x0x9eeae2+2419282]
	GetHandleVerifier [0x0x7d712a+225434]
	GetHandleVerifier [0x0x7c6e08+159096]
	GetHandleVerifier [0x0x7cdd5d+187597]
	GetHandleVerifier [0x0x7b7ad8+96840]
	GetHandleVerifier [0x0x7b7c62+97234]
	GetHandleVerifier [0x0x7a277a+9962]
	BaseThreadInitThunk [0x0x76da5d49+25]
	RtlInitializeExceptionChain [0x0x77d4d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x77d4d281+561]

2025-08-21 17:00:13,739 - ERROR - 用户登录失败或超时
2025-08-21 17:00:13,740 - INFO - 自动化程序执行完成，浏览器将保持打开状态
2025-08-21 17:00:13,740 - INFO - 您可以手动查看页面内容，完成后请关闭浏览器
2025-08-21 17:01:03,386 - INFO - 日志系统初始化成功
2025-08-21 17:01:03,387 - INFO - 开始运行BOSS直聘自动化程序 V2.0...
2025-08-21 17:01:03,432 - INFO - ====== WebDriver manager ======
2025-08-21 17:01:06,487 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 17:01:07,477 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 17:01:08,402 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\139.0.7258.138\chromedriver-win32/chromedriver.exe] found in cache
2025-08-21 17:01:10,080 - INFO - 使用自动下载的chromedriver
2025-08-21 17:01:10,112 - INFO - Chrome浏览器驱动设置成功
2025-08-21 17:01:10,112 - INFO - 访问登录页面: https://www.zhipin.com/web/user/?ka=header-login
2025-08-21 17:01:10,595 - INFO - 等待用户登录（超时时间：300秒）...
2025-08-21 17:01:10,595 - INFO - 请在浏览器中完成登录操作...
2025-08-21 17:01:28,535 - INFO - 检测到用户已登录，页面已跳转到聊天页面
2025-08-21 17:01:28,536 - INFO - 等待页面完全加载（超时时间：30秒）...
2025-08-21 17:01:31,569 - INFO - 页面加载完成
2025-08-21 17:01:32,590 - INFO - 检测到弹窗
2025-08-21 17:01:32,639 - INFO - 检测到弹窗并已关闭
2025-08-21 17:01:33,151 - INFO - 尝试点击个人中心...
2025-08-21 17:01:33,215 - INFO - 成功点击个人中心
2025-08-21 17:01:36,230 - INFO - 等待页面完全加载（超时时间：30秒）...
2025-08-21 17:01:39,247 - INFO - 页面加载完成
2025-08-21 17:01:39,247 - INFO - 开始提取用户信息...
2025-08-21 17:01:41,307 - INFO - 找到姓名: 杨锐雄
2025-08-21 17:01:41,377 - WARNING - 未找到公司名称信息
2025-08-21 17:01:41,392 - INFO - 找到头像: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-08-21 17:01:41,402 - INFO - 用户信息已保存到文件: user_info_20250821_170141.json
2025-08-21 17:01:41,402 - INFO - ==================================================
2025-08-21 17:01:41,402 - INFO - 用户信息提取结果:
2025-08-21 17:01:41,402 - INFO - 姓名: 杨锐雄
2025-08-21 17:01:41,402 - INFO - 公司名称: 未找到
2025-08-21 17:01:41,404 - INFO - 头像: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-08-21 17:01:41,404 - INFO - ==================================================
2025-08-21 17:01:41,404 - INFO - 用户信息获取成功，开始点击职位管理按钮...
2025-08-21 17:01:41,404 - INFO - 尝试点击职位管理按钮...
2025-08-21 17:02:05,153 - INFO - 成功点击职位管理按钮，使用选择器: //a[contains(text(), '职位管理')]
2025-08-21 17:02:08,159 - INFO - 成功点击职位管理按钮
2025-08-21 17:02:11,174 - INFO - 开始提取职位列表信息...
2025-08-21 17:02:12,684 - INFO - 开始在iframe中查找职位列表...
2025-08-21 17:02:12,698 - INFO - 找到 1 个iframe
2025-08-21 17:02:12,712 - INFO - 已切换到第一个iframe
2025-08-21 17:02:12,728 - INFO - 第一个iframe下的div.job-list-warp内容: <div data-v-e9324162="" class="job-list-warp"><!----> <!----> <div data-v-e9324162="" class="list-inner-container"><!----> <ul data-v-e9324162="" class="job-list-content"><li data-v-37187c92="" data-v-e9324162="" data-id="41f8839f7881490003N63t6_EFJS" class="job-jobInfo-warp"><!----> <div data-v-37187c92="" class="single-check-box"></div> <div data-v-37187c92="" class="top-wrap"><div data-v-7344c8e0="" data-v-37187c92="" class="job-main-info-wrapper"><div data-v-7344c8e0="" class="job-title"><a data-v-7344c8e0="" href="javascript:;">AI产品经理</a> <div data-v-7344c8e0="" class="label-common label-normal">普</div> <!----> <!----> <!----> <!----></div> <div data-v-7344c8e0="" class="info-labels"><!----> <span data-v-7344c8e0="">珠海</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">3-5年</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">本科</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">14-20K</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">全职</span> <!----> <!----></div> <!----></div> <div data-v-fb03be90="" data-v-37187c92="" class="job-about-num-wrapper"><div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">17</div> <div data-v-fb03be90="" class="name">看过我</div></div> <div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">4</div> <div data-v-fb03be90="" class="name">沟通过</div></div> <div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">0</div> <div data-v-fb03be90="" class="name">感兴趣</div></div></div> <div data-v-6692a4a3="" data-v-37187c92="" class="job-status-wrapper"><div data-v-6692a4a3="" class="status-top"><div data-v-6692a4a3="" class="status-box status-opening"><span data-v-6692a4a3="" class="pointer" style="background: rgb(0, 190, 189);"></span>
            开放中
            <!----></div> <!----> <!----></div> <div data-v-6692a4a3="" class="status-bottom"><!----></div></div> <div data-v-37187c92="" class="jobInfo-container operation-container"><!----> <a data-v-37187c92="" href="javascript:;" class="position-edit opreat-btn">
                    编辑
                </a> <a data-v-37187c92="" href="javascript:;" class="position-close opreat-btn">
                    关闭
                </a> <!----> <div data-v-460bff2e="" data-v-37187c92="" class="job-operate-wrapper opreat-btn"><div data-v-460bff2e="" class="more-operate"><span data-v-460bff2e="" class="dot"><i data-v-460bff2e=""></i> <i data-v-460bff2e=""></i> <i data-v-460bff2e=""></i></span> <!----> <ul data-v-460bff2e="" class="job-operate-container"><li data-v-460bff2e="" class="job-operate-item">
                        预览
                        <!----></li><li data-v-460bff2e="" class="job-operate-share">
                        分享
                        <!----></li><li data-v-460bff2e="" class="job-operate-item">
                        复制
                        <span data-v-460bff2e="" class="new-icon">new</span></li><li data-v-460bff2e="" class="job-operate-item">
                        删除
                        <!----></li></ul></div></div></div></div> <!----></li><li data-v-37187c92="" data-v-e9324162="" data-id="52a786b20358e5e903N6396_GVdV" class="job-jobInfo-warp"><!----> <div data-v-37187c92="" class="single-check-box"></div> <div data-v-37187c92="" class="top-wrap"><div data-v-7344c8e0="" data-v-37187c92="" class="job-main-info-wrapper"><div data-v-7344c8e0="" class="job-title"><a data-v-7344c8e0="" href="javascript:;">人力资源主管</a> <div data-v-7344c8e0="" class="label-common label-normal">普</div> <!----> <!----> <!----> <!----></div> <div data-v-7344c8e0="" class="info-labels"><!----> <span data-v-7344c8e0="">珠海</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">5-10年</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">本科</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">6-7K</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">全职</span> <!----> <!----></div> <!----></div> <div data-v-fb03be90="" data-v-37187c92="" class="job-about-num-wrapper"><div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">61</div> <div data-v-fb03be90="" class="name">看过我</div></div> <div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">30</div> <div data-v-fb03be90="" class="name">沟通过</div></div> <div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">3</div> <div data-v-fb03be90="" class="name">感兴趣</div></div></div> <div data-v-6692a4a3="" data-v-37187c92="" class="job-status-wrapper"><div data-v-6692a4a3="" class="status-top"><div data-v-6692a4a3="" class="status-box status-opening"><span data-v-6692a4a3="" class="pointer" style="background: rgb(0, 190, 189);"></span>
            开放中
            <!----></div> <!----> <!----></div> <div data-v-6692a4a3="" class="status-bottom"><!----></div></div> <div data-v-37187c92="" class="jobInfo-container operation-container"><!----> <a data-v-37187c92="" href="javascript:;" class="position-edit opreat-btn">
                    编辑
                </a> <a data-v-37187c92="" href="javascript:;" class="position-close opreat-btn">
                    关闭
                </a> <!----> <div data-v-460bff2e="" data-v-37187c92="" class="job-operate-wrapper opreat-btn"><div data-v-460bff2e="" class="more-operate"><span data-v-460bff2e="" class="dot"><i data-v-460bff2e=""></i> <i data-v-460bff2e=""></i> <i data-v-460bff2e=""></i></span> <!----> <ul data-v-460bff2e="" class="job-operate-container"><li data-v-460bff2e="" class="job-operate-item">
                        预览
                        <!----></li><li data-v-460bff2e="" class="job-operate-share">
                        分享
                        <!----></li><li data-v-460bff2e="" class="job-operate-item">
                        复制
                        <span data-v-460bff2e="" class="new-icon">new</span></li><li data-v-460bff2e="" class="job-operate-item">
                        删除
                        <!----></li></ul></div></div></div></div> <!----></li></ul> <div data-v-e9324162="" class="page-content"><div data-v-e9324162="" class="total-num">
               共2个职位<!----></div> <div data-v-e9324162="" class="pager text-center"><!----> <div class="options-pages"><a href="javascript:;" class="disabled"><i class="ui-icon-arrow-left"></i></a> <a href="javascript:;" class="selected">1</a> <!---->  <!----> <!----> <a href="javascript:;" class="disabled"><i class="ui-icon-arrow-right"></i></a></div> <!----> <!----> <!----></div></div></div> <!----></div>
2025-08-21 17:02:12,735 - INFO - 在div.job-list-warp中找到 2 个li.job-jobInfo-warp 元素
2025-08-21 17:02:12,735 - INFO - 处理第 1 个职位...
2025-08-21 17:02:12,751 - INFO - 职位标题: AI产品经理
2025-08-21 17:02:12,784 - INFO - 职位信息: {'职位索引': 1, '职位名称': 'AI产品经理', '工作地点': '珠海', '工作经验': '3-5年', '学历要求': '本科', '薪资范围': '14-20K', '招聘类型': '全职'}
2025-08-21 17:02:12,862 - INFO - 已点击职位标题，进入职位详情页
2025-08-21 17:02:16,912 - INFO - 职位详情页内容: 岗位职责：
1. 负责AI技术在图帕斯户外产品中的应用规划，推动智能户外装备的创新与发展；
2. 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平；
3. 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行；
4. 持续跟踪市场趋势和技术发展，为产品迭代提供科学依据；
5. 与研发、设计、市场团队紧密合作，优化产品功能，增强用户体验。

任职要求：
1. 对AI技术及其应用场景有深刻理解，具备相关项目经验者优先；
2. 熟悉户外用品市场，了解用户痛点和需求，能够转化为产品特性；
3. 优秀的跨部门沟通与协调能力，能够推动多方合作达成目标；
4. 强烈的责任心和执行力，能够在快节奏环境中解决问题；
5. 具备良好的数据分析能力，能够通过数据驱动产品决策。
2025-08-21 17:02:16,923 - INFO - 已返回职位列表页
2025-08-21 17:02:17,956 - INFO - 处理第 2 个职位...
2025-08-21 17:02:17,967 - INFO - 职位标题: 人力资源主管
2025-08-21 17:02:17,996 - INFO - 职位信息: {'职位索引': 2, '职位名称': '人力资源主管', '工作地点': '珠海', '工作经验': '5-10年', '学历要求': '本科', '薪资范围': '6-7K', '招聘类型': '全职'}
2025-08-21 17:02:18,091 - INFO - 已点击职位标题，进入职位详情页
2025-08-21 17:02:22,123 - INFO - 职位详情页内容: 岗位职责：
1. 统筹公司人力资源管理工作，确保人力资源政策的有效执行；
2. 负责招聘、培训、绩效评估及员工关系等全员工生命周期的管理；
3. 设计和优化薪酬福利体系，提升员工满意度和公司竞争力；
4. 参与制定人力资源战略规划，支持公司业务发展及组织变革；
5. 监控部门运作效率，提出改进建议，优化工作流程。

任职要求：
1. 人力资源管理或相关专业本科及以上学历；
2. 5年以上人力资源相关工作经验，有主管或以上职位经验；
3. 熟悉人力资源各大模块，具备优秀的人才招聘、培训及绩效管理能力；
4. 具备良好的沟通协调能力和团队领导力，能够处理复杂的人事问题；
5. 对人力资源法律法规有深入了解，能够为公司提供合规的人力资源解决方案。
2025-08-21 17:02:22,135 - INFO - 已返回职位列表页
2025-08-21 17:02:23,154 - INFO - 成功提取到 2 个职位信息
2025-08-21 17:02:23,156 - INFO - 成功获取到 2 个职位信息
2025-08-21 17:02:23,166 - INFO - 用户信息已保存到文件: user_info_20250821_170223.json
2025-08-21 17:02:25,379 - INFO - 浏览器已关闭
2025-08-21 17:02:25,379 - INFO - 自动化程序执行完成，浏览器将保持打开状态
2025-08-21 17:02:25,380 - INFO - 您可以手动查看页面内容，完成后请关闭浏览器
2025-08-21 17:40:44,255 - INFO - 日志系统初始化成功
2025-08-21 17:40:44,255 - INFO - 开始运行BOSS直聘自动化程序 V2.0...
2025-08-21 17:40:44,298 - INFO - ====== WebDriver manager ======
2025-08-21 17:40:47,499 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 17:40:48,372 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 17:40:49,326 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\139.0.7258.138\chromedriver-win32/chromedriver.exe] found in cache
2025-08-21 17:40:51,033 - INFO - 使用自动下载的chromedriver
2025-08-21 17:40:51,077 - INFO - Chrome浏览器驱动设置成功
2025-08-21 17:40:51,077 - INFO - 访问登录页面: https://www.zhipin.com/web/user/?ka=header-login
2025-08-21 17:40:51,532 - INFO - 等待用户登录（超时时间：300秒）...
2025-08-21 17:40:51,532 - INFO - 请在浏览器中完成登录操作...
2025-08-21 17:41:01,194 - INFO - 检测到用户已登录，页面已跳转到聊天页面
2025-08-21 17:41:01,194 - INFO - 等待页面完全加载（超时时间：30秒）...
2025-08-21 17:41:04,216 - INFO - 页面加载完成
2025-08-21 17:41:05,231 - INFO - 检测到弹窗
2025-08-21 17:41:05,275 - INFO - 检测到弹窗并已关闭
2025-08-21 17:41:05,779 - INFO - 尝试点击个人中心...
2025-08-21 17:41:05,846 - INFO - 成功点击个人中心
2025-08-21 17:41:08,859 - INFO - 等待页面完全加载（超时时间：30秒）...
2025-08-21 17:41:11,871 - INFO - 页面加载完成
2025-08-21 17:41:11,871 - INFO - 开始提取用户信息...
2025-08-21 17:41:13,906 - INFO - 找到姓名: 杨锐雄
2025-08-21 17:41:13,953 - WARNING - 未找到公司名称信息
2025-08-21 17:41:13,967 - INFO - 找到头像: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-08-21 17:41:13,968 - INFO - 用户信息已保存到文件: user_info_20250821_174113.json
2025-08-21 17:41:13,968 - INFO - ==================================================
2025-08-21 17:41:13,968 - INFO - 用户信息提取结果:
2025-08-21 17:41:13,968 - INFO - 姓名: 杨锐雄
2025-08-21 17:41:13,968 - INFO - 公司名称: 未找到
2025-08-21 17:41:13,969 - INFO - 头像: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-08-21 17:41:13,969 - INFO - ==================================================
2025-08-21 17:41:13,969 - INFO - 用户信息获取成功，开始点击职位管理按钮...
2025-08-21 17:41:13,969 - INFO - 尝试点击职位管理按钮...
2025-08-21 17:41:37,676 - INFO - 成功点击职位管理按钮，使用选择器: //a[contains(text(), '职位管理')]
2025-08-21 17:41:40,677 - INFO - 成功点击职位管理按钮
2025-08-21 17:41:43,686 - INFO - 开始提取职位列表信息...
2025-08-21 17:41:45,192 - INFO - 开始在iframe中查找职位列表...
2025-08-21 17:41:45,206 - INFO - 找到 1 个iframe
2025-08-21 17:41:45,218 - INFO - 已切换到第一个iframe
2025-08-21 17:41:45,229 - INFO - 第一个iframe下的div.job-list-warp内容: <div data-v-e9324162="" class="job-list-warp"><!----> <!----> <div data-v-e9324162="" class="list-inner-container"><!----> <ul data-v-e9324162="" class="job-list-content"><li data-v-37187c92="" data-v-e9324162="" data-id="41f8839f7881490003N63t6_EFJS" class="job-jobInfo-warp"><!----> <div data-v-37187c92="" class="single-check-box"></div> <div data-v-37187c92="" class="top-wrap"><div data-v-7344c8e0="" data-v-37187c92="" class="job-main-info-wrapper"><div data-v-7344c8e0="" class="job-title"><a data-v-7344c8e0="" href="javascript:;">AI产品经理</a> <div data-v-7344c8e0="" class="label-common label-normal">普</div> <!----> <!----> <!----> <!----></div> <div data-v-7344c8e0="" class="info-labels"><!----> <span data-v-7344c8e0="">珠海</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">3-5年</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">本科</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">14-20K</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">全职</span> <!----> <!----></div> <!----></div> <div data-v-fb03be90="" data-v-37187c92="" class="job-about-num-wrapper"><div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">17</div> <div data-v-fb03be90="" class="name">看过我</div></div> <div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">4</div> <div data-v-fb03be90="" class="name">沟通过</div></div> <div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">0</div> <div data-v-fb03be90="" class="name">感兴趣</div></div></div> <div data-v-6692a4a3="" data-v-37187c92="" class="job-status-wrapper"><div data-v-6692a4a3="" class="status-top"><div data-v-6692a4a3="" class="status-box status-opening"><span data-v-6692a4a3="" class="pointer" style="background: rgb(0, 190, 189);"></span>
            开放中
            <!----></div> <!----> <!----></div> <div data-v-6692a4a3="" class="status-bottom"><!----></div></div> <div data-v-37187c92="" class="jobInfo-container operation-container"><!----> <a data-v-37187c92="" href="javascript:;" class="position-edit opreat-btn">
                    编辑
                </a> <a data-v-37187c92="" href="javascript:;" class="position-close opreat-btn">
                    关闭
                </a> <!----> <div data-v-460bff2e="" data-v-37187c92="" class="job-operate-wrapper opreat-btn"><div data-v-460bff2e="" class="more-operate"><span data-v-460bff2e="" class="dot"><i data-v-460bff2e=""></i> <i data-v-460bff2e=""></i> <i data-v-460bff2e=""></i></span> <!----> <ul data-v-460bff2e="" class="job-operate-container"><li data-v-460bff2e="" class="job-operate-item">
                        预览
                        <!----></li><li data-v-460bff2e="" class="job-operate-share">
                        分享
                        <!----></li><li data-v-460bff2e="" class="job-operate-item">
                        复制
                        <span data-v-460bff2e="" class="new-icon">new</span></li><li data-v-460bff2e="" class="job-operate-item">
                        删除
                        <!----></li></ul></div></div></div></div> <!----></li><li data-v-37187c92="" data-v-e9324162="" data-id="52a786b20358e5e903N6396_GVdV" class="job-jobInfo-warp"><!----> <div data-v-37187c92="" class="single-check-box"></div> <div data-v-37187c92="" class="top-wrap"><div data-v-7344c8e0="" data-v-37187c92="" class="job-main-info-wrapper"><div data-v-7344c8e0="" class="job-title"><a data-v-7344c8e0="" href="javascript:;">人力资源主管</a> <div data-v-7344c8e0="" class="label-common label-normal">普</div> <!----> <!----> <!----> <!----></div> <div data-v-7344c8e0="" class="info-labels"><!----> <span data-v-7344c8e0="">珠海</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">5-10年</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">本科</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">6-7K</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">全职</span> <!----> <!----></div> <!----></div> <div data-v-fb03be90="" data-v-37187c92="" class="job-about-num-wrapper"><div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">61</div> <div data-v-fb03be90="" class="name">看过我</div></div> <div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">30</div> <div data-v-fb03be90="" class="name">沟通过</div></div> <div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">3</div> <div data-v-fb03be90="" class="name">感兴趣</div></div></div> <div data-v-6692a4a3="" data-v-37187c92="" class="job-status-wrapper"><div data-v-6692a4a3="" class="status-top"><div data-v-6692a4a3="" class="status-box status-opening"><span data-v-6692a4a3="" class="pointer" style="background: rgb(0, 190, 189);"></span>
            开放中
            <!----></div> <!----> <!----></div> <div data-v-6692a4a3="" class="status-bottom"><!----></div></div> <div data-v-37187c92="" class="jobInfo-container operation-container"><!----> <a data-v-37187c92="" href="javascript:;" class="position-edit opreat-btn">
                    编辑
                </a> <a data-v-37187c92="" href="javascript:;" class="position-close opreat-btn">
                    关闭
                </a> <!----> <div data-v-460bff2e="" data-v-37187c92="" class="job-operate-wrapper opreat-btn"><div data-v-460bff2e="" class="more-operate"><span data-v-460bff2e="" class="dot"><i data-v-460bff2e=""></i> <i data-v-460bff2e=""></i> <i data-v-460bff2e=""></i></span> <!----> <ul data-v-460bff2e="" class="job-operate-container"><li data-v-460bff2e="" class="job-operate-item">
                        预览
                        <!----></li><li data-v-460bff2e="" class="job-operate-share">
                        分享
                        <!----></li><li data-v-460bff2e="" class="job-operate-item">
                        复制
                        <span data-v-460bff2e="" class="new-icon">new</span></li><li data-v-460bff2e="" class="job-operate-item">
                        删除
                        <!----></li></ul></div></div></div></div> <!----></li></ul> <div data-v-e9324162="" class="page-content"><div data-v-e9324162="" class="total-num">
               共2个职位<!----></div> <div data-v-e9324162="" class="pager text-center"><!----> <div class="options-pages"><a href="javascript:;" class="disabled"><i class="ui-icon-arrow-left"></i></a> <a href="javascript:;" class="selected">1</a> <!---->  <!----> <!----> <a href="javascript:;" class="disabled"><i class="ui-icon-arrow-right"></i></a></div> <!----> <!----> <!----></div></div></div> <!----></div>
2025-08-21 17:41:45,237 - INFO - 在div.job-list-warp中找到 2 个li.job-jobInfo-warp 元素
2025-08-21 17:41:45,237 - INFO - 处理第 1 个职位...
2025-08-21 17:41:45,252 - INFO - 职位标题: AI产品经理
2025-08-21 17:41:45,295 - INFO - 职位信息: {'职位索引': 1, '职位名称': 'AI产品经理', '工作地点': '珠海', '工作经验': '3-5年', '学历要求': '本科', '薪资范围': '14-20K', '招聘类型': '全职'}
2025-08-21 17:41:45,366 - INFO - 已点击职位标题，进入职位详情页
2025-08-21 17:41:49,413 - INFO - 职位详情页内容: 岗位职责：
1. 负责AI技术在图帕斯户外产品中的应用规划，推动智能户外装备的创新与发展；
2. 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平；
3. 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行；
4. 持续跟踪市场趋势和技术发展，为产品迭代提供科学依据；
5. 与研发、设计、市场团队紧密合作，优化产品功能，增强用户体验。

任职要求：
1. 对AI技术及其应用场景有深刻理解，具备相关项目经验者优先；
2. 熟悉户外用品市场，了解用户痛点和需求，能够转化为产品特性；
3. 优秀的跨部门沟通与协调能力，能够推动多方合作达成目标；
4. 强烈的责任心和执行力，能够在快节奏环境中解决问题；
5. 具备良好的数据分析能力，能够通过数据驱动产品决策。
2025-08-21 17:41:49,427 - INFO - 已返回职位列表页
2025-08-21 17:41:50,456 - INFO - 处理第 2 个职位...
2025-08-21 17:41:50,494 - INFO - 职位标题: 人力资源主管
2025-08-21 17:41:50,554 - INFO - 职位信息: {'职位索引': 2, '职位名称': '人力资源主管', '工作地点': '珠海', '工作经验': '5-10年', '学历要求': '本科', '薪资范围': '6-7K', '招聘类型': '全职'}
2025-08-21 17:41:50,656 - INFO - 已点击职位标题，进入职位详情页
2025-08-21 17:41:54,714 - INFO - 职位详情页内容: 岗位职责：
1. 统筹公司人力资源管理工作，确保人力资源政策的有效执行；
2. 负责招聘、培训、绩效评估及员工关系等全员工生命周期的管理；
3. 设计和优化薪酬福利体系，提升员工满意度和公司竞争力；
4. 参与制定人力资源战略规划，支持公司业务发展及组织变革；
5. 监控部门运作效率，提出改进建议，优化工作流程。

任职要求：
1. 人力资源管理或相关专业本科及以上学历；
2. 5年以上人力资源相关工作经验，有主管或以上职位经验；
3. 熟悉人力资源各大模块，具备优秀的人才招聘、培训及绩效管理能力；
4. 具备良好的沟通协调能力和团队领导力，能够处理复杂的人事问题；
5. 对人力资源法律法规有深入了解，能够为公司提供合规的人力资源解决方案。
2025-08-21 17:41:54,727 - INFO - 已返回职位列表页
2025-08-21 17:41:55,758 - INFO - 成功提取到 2 个职位信息
2025-08-21 17:41:55,760 - INFO - 成功获取到 2 个职位信息
2025-08-21 17:41:55,760 - INFO - 用户信息已保存到文件: user_info_20250821_174155.json
2025-08-21 17:41:57,963 - INFO - 浏览器已关闭
2025-08-21 17:41:57,963 - INFO - 自动化程序执行完成，浏览器将保持打开状态
2025-08-21 17:41:57,963 - INFO - 您可以手动查看页面内容，完成后请关闭浏览器
2025-08-21 22:43:59,870 - INFO - 日志系统初始化成功
2025-08-21 22:43:59,870 - INFO - 开始运行BOSS直聘自动化程序 V2.0...
2025-08-21 22:43:59,917 - INFO - ====== WebDriver manager ======
2025-08-21 22:44:02,802 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 22:44:06,307 - ERROR - 设置Chrome驱动失败: Could not reach host. Are you offline?
2025-08-21 22:44:06,308 - ERROR - 自动化程序运行失败: Could not reach host. Are you offline?
2025-08-21 22:44:06,308 - INFO - 自动化程序执行完成，浏览器将保持打开状态
2025-08-21 22:44:06,308 - INFO - 您可以手动查看页面内容，完成后请关闭浏览器
2025-08-21 22:44:50,243 - INFO - 日志系统初始化成功
2025-08-21 22:44:50,243 - INFO - 开始运行BOSS直聘自动化程序 V2.0...
2025-08-21 22:44:50,285 - INFO - ====== WebDriver manager ======
2025-08-21 22:44:53,396 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 22:44:54,653 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 22:44:55,352 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\139.0.7258.138\chromedriver-win32/chromedriver.exe] found in cache
2025-08-21 22:44:57,405 - INFO - 使用自动下载的chromedriver
2025-08-21 22:44:57,440 - INFO - Chrome浏览器驱动设置成功
2025-08-21 22:44:57,440 - INFO - 访问登录页面: https://www.zhipin.com/web/user/?ka=header-login
2025-08-21 22:44:58,232 - INFO - 等待用户登录（超时时间：300秒）...
2025-08-21 22:44:58,232 - INFO - 请在浏览器中完成登录操作...
2025-08-21 22:45:03,416 - INFO - 检测到用户已登录，页面已跳转到聊天页面
2025-08-21 22:45:03,417 - INFO - 等待页面完全加载（超时时间：30秒）...
2025-08-21 22:45:06,443 - INFO - 页面加载完成
2025-08-21 22:45:07,465 - INFO - 检测到弹窗
2025-08-21 22:45:07,529 - INFO - 检测到弹窗并已关闭
2025-08-21 22:45:08,044 - INFO - 尝试点击个人中心...
2025-08-21 22:45:08,119 - INFO - 成功点击个人中心
2025-08-21 22:45:11,121 - INFO - 等待页面完全加载（超时时间：30秒）...
2025-08-21 22:45:14,129 - INFO - 页面加载完成
2025-08-21 22:45:14,129 - INFO - 开始提取用户信息...
2025-08-21 22:45:16,158 - INFO - 找到姓名: 杨锐雄
2025-08-21 22:45:16,214 - WARNING - 未找到公司名称信息
2025-08-21 22:45:16,236 - INFO - 找到头像: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-08-21 22:45:16,237 - INFO - 用户信息已保存到文件: user_info_20250821_224516.json
2025-08-21 22:45:16,237 - INFO - ==================================================
2025-08-21 22:45:16,237 - INFO - 用户信息提取结果:
2025-08-21 22:45:16,237 - INFO - 姓名: 杨锐雄
2025-08-21 22:45:16,237 - INFO - 公司名称: 未找到
2025-08-21 22:45:16,237 - INFO - 头像: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-08-21 22:45:16,237 - INFO - ==================================================
2025-08-21 22:45:16,238 - INFO - 用户信息获取成功，开始点击职位管理按钮...
2025-08-21 22:45:16,238 - INFO - 尝试点击职位管理按钮...
2025-08-21 22:45:40,019 - INFO - 成功点击职位管理按钮，使用选择器: //a[contains(text(), '职位管理')]
2025-08-21 22:45:43,033 - INFO - 成功点击职位管理按钮
2025-08-21 22:45:46,048 - INFO - 开始提取职位列表信息...
2025-08-21 22:45:47,557 - INFO - 开始在iframe中查找职位列表...
2025-08-21 22:45:47,570 - INFO - 找到 1 个iframe
2025-08-21 22:45:47,585 - INFO - 已切换到第一个iframe
2025-08-21 22:45:47,598 - INFO - 第一个iframe下的div.job-list-warp内容: <div data-v-e9324162="" class="job-list-warp"><!----> <!----> <div data-v-e9324162="" class="list-inner-container"><!----> <ul data-v-e9324162="" class="job-list-content"><li data-v-37187c92="" data-v-e9324162="" data-id="41f8839f7881490003N63t6_EFJS" class="job-jobInfo-warp"><!----> <div data-v-37187c92="" class="single-check-box"></div> <div data-v-37187c92="" class="top-wrap"><div data-v-7344c8e0="" data-v-37187c92="" class="job-main-info-wrapper"><div data-v-7344c8e0="" class="job-title"><a data-v-7344c8e0="" href="javascript:;">AI产品经理</a> <div data-v-7344c8e0="" class="label-common label-normal">普</div> <!----> <!----> <!----> <!----></div> <div data-v-7344c8e0="" class="info-labels"><!----> <span data-v-7344c8e0="">珠海</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">3-5年</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">本科</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">14-20K</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">全职</span> <!----> <!----></div> <!----></div> <div data-v-fb03be90="" data-v-37187c92="" class="job-about-num-wrapper"><div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">17</div> <div data-v-fb03be90="" class="name">看过我</div></div> <div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">4</div> <div data-v-fb03be90="" class="name">沟通过</div></div> <div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">0</div> <div data-v-fb03be90="" class="name">感兴趣</div></div></div> <div data-v-6692a4a3="" data-v-37187c92="" class="job-status-wrapper"><div data-v-6692a4a3="" class="status-top"><div data-v-6692a4a3="" class="status-box status-opening"><span data-v-6692a4a3="" class="pointer" style="background: rgb(0, 190, 189);"></span>
            开放中
            <!----></div> <!----> <!----></div> <div data-v-6692a4a3="" class="status-bottom"><!----></div></div> <div data-v-37187c92="" class="jobInfo-container operation-container"><!----> <a data-v-37187c92="" href="javascript:;" class="position-edit opreat-btn">
                    编辑
                </a> <a data-v-37187c92="" href="javascript:;" class="position-close opreat-btn">
                    关闭
                </a> <!----> <div data-v-460bff2e="" data-v-37187c92="" class="job-operate-wrapper opreat-btn"><div data-v-460bff2e="" class="more-operate"><span data-v-460bff2e="" class="dot"><i data-v-460bff2e=""></i> <i data-v-460bff2e=""></i> <i data-v-460bff2e=""></i></span> <!----> <ul data-v-460bff2e="" class="job-operate-container"><li data-v-460bff2e="" class="job-operate-item">
                        预览
                        <!----></li><li data-v-460bff2e="" class="job-operate-share">
                        分享
                        <!----></li><li data-v-460bff2e="" class="job-operate-item">
                        复制
                        <span data-v-460bff2e="" class="new-icon">new</span></li><li data-v-460bff2e="" class="job-operate-item">
                        删除
                        <!----></li></ul></div></div></div></div> <!----></li><li data-v-37187c92="" data-v-e9324162="" data-id="52a786b20358e5e903N6396_GVdV" class="job-jobInfo-warp"><!----> <div data-v-37187c92="" class="single-check-box"></div> <div data-v-37187c92="" class="top-wrap"><div data-v-7344c8e0="" data-v-37187c92="" class="job-main-info-wrapper"><div data-v-7344c8e0="" class="job-title"><a data-v-7344c8e0="" href="javascript:;">人力资源主管</a> <div data-v-7344c8e0="" class="label-common label-normal">普</div> <!----> <!----> <!----> <!----></div> <div data-v-7344c8e0="" class="info-labels"><!----> <span data-v-7344c8e0="">珠海</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">5-10年</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">本科</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">6-7K</span> <em data-v-7344c8e0="" class="vline"></em> <span data-v-7344c8e0="">全职</span> <!----> <!----></div> <!----></div> <div data-v-fb03be90="" data-v-37187c92="" class="job-about-num-wrapper"><div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">63</div> <div data-v-fb03be90="" class="name">看过我</div></div> <div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">30</div> <div data-v-fb03be90="" class="name">沟通过</div></div> <div data-v-fb03be90="" class="inner-box"><div data-v-fb03be90="" class="num">3</div> <div data-v-fb03be90="" class="name">感兴趣</div></div></div> <div data-v-6692a4a3="" data-v-37187c92="" class="job-status-wrapper"><div data-v-6692a4a3="" class="status-top"><div data-v-6692a4a3="" class="status-box status-opening"><span data-v-6692a4a3="" class="pointer" style="background: rgb(0, 190, 189);"></span>
            开放中
            <!----></div> <!----> <!----></div> <div data-v-6692a4a3="" class="status-bottom"><!----></div></div> <div data-v-37187c92="" class="jobInfo-container operation-container"><!----> <a data-v-37187c92="" href="javascript:;" class="position-edit opreat-btn">
                    编辑
                </a> <a data-v-37187c92="" href="javascript:;" class="position-close opreat-btn">
                    关闭
                </a> <!----> <div data-v-460bff2e="" data-v-37187c92="" class="job-operate-wrapper opreat-btn"><div data-v-460bff2e="" class="more-operate"><span data-v-460bff2e="" class="dot"><i data-v-460bff2e=""></i> <i data-v-460bff2e=""></i> <i data-v-460bff2e=""></i></span> <!----> <ul data-v-460bff2e="" class="job-operate-container"><li data-v-460bff2e="" class="job-operate-item">
                        预览
                        <!----></li><li data-v-460bff2e="" class="job-operate-share">
                        分享
                        <!----></li><li data-v-460bff2e="" class="job-operate-item">
                        复制
                        <span data-v-460bff2e="" class="new-icon">new</span></li><li data-v-460bff2e="" class="job-operate-item">
                        删除
                        <!----></li></ul></div></div></div></div> <!----></li></ul> <div data-v-e9324162="" class="page-content"><div data-v-e9324162="" class="total-num">
               共2个职位<!----></div> <div data-v-e9324162="" class="pager text-center"><!----> <div class="options-pages"><a href="javascript:;" class="disabled"><i class="ui-icon-arrow-left"></i></a> <a href="javascript:;" class="selected">1</a> <!---->  <!----> <!----> <a href="javascript:;" class="disabled"><i class="ui-icon-arrow-right"></i></a></div> <!----> <!----> <!----></div></div></div> <!----></div>
2025-08-21 22:45:47,605 - INFO - 在div.job-list-warp中找到 2 个li.job-jobInfo-warp 元素
2025-08-21 22:45:47,605 - INFO - 处理第 1 个职位...
2025-08-21 22:45:47,626 - INFO - 职位标题: AI产品经理
2025-08-21 22:45:47,663 - INFO - 职位信息: {'职位索引': 1, '职位名称': 'AI产品经理', '工作地点': '珠海', '工作经验': '3-5年', '学历要求': '本科', '薪资范围': '14-20K', '招聘类型': '全职'}
2025-08-21 22:45:47,775 - INFO - 已点击职位标题，进入职位详情页
2025-08-21 22:45:51,853 - INFO - 职位详情页内容: 岗位职责：
1. 负责AI技术在图帕斯户外产品中的应用规划，推动智能户外装备的创新与发展；
2. 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平；
3. 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行；
4. 持续跟踪市场趋势和技术发展，为产品迭代提供科学依据；
5. 与研发、设计、市场团队紧密合作，优化产品功能，增强用户体验。

任职要求：
1. 对AI技术及其应用场景有深刻理解，具备相关项目经验者优先；
2. 熟悉户外用品市场，了解用户痛点和需求，能够转化为产品特性；
3. 优秀的跨部门沟通与协调能力，能够推动多方合作达成目标；
4. 强烈的责任心和执行力，能够在快节奏环境中解决问题；
5. 具备良好的数据分析能力，能够通过数据驱动产品决策。
2025-08-21 22:45:51,872 - INFO - 已返回职位列表页
2025-08-21 22:45:52,901 - INFO - 处理第 2 个职位...
2025-08-21 22:45:52,940 - INFO - 职位标题: 人力资源主管
2025-08-21 22:45:52,981 - INFO - 职位信息: {'职位索引': 2, '职位名称': '人力资源主管', '工作地点': '珠海', '工作经验': '5-10年', '学历要求': '本科', '薪资范围': '6-7K', '招聘类型': '全职'}
2025-08-21 22:45:53,107 - INFO - 已点击职位标题，进入职位详情页
2025-08-21 22:45:57,266 - INFO - 职位详情页内容: 岗位职责：
1. 统筹公司人力资源管理工作，确保人力资源政策的有效执行；
2. 负责招聘、培训、绩效评估及员工关系等全员工生命周期的管理；
3. 设计和优化薪酬福利体系，提升员工满意度和公司竞争力；
4. 参与制定人力资源战略规划，支持公司业务发展及组织变革；
5. 监控部门运作效率，提出改进建议，优化工作流程。

任职要求：
1. 人力资源管理或相关专业本科及以上学历；
2. 5年以上人力资源相关工作经验，有主管或以上职位经验；
3. 熟悉人力资源各大模块，具备优秀的人才招聘、培训及绩效管理能力；
4. 具备良好的沟通协调能力和团队领导力，能够处理复杂的人事问题；
5. 对人力资源法律法规有深入了解，能够为公司提供合规的人力资源解决方案。
2025-08-21 22:45:57,283 - INFO - 已返回职位列表页
2025-08-21 22:45:58,338 - INFO - 成功提取到 2 个职位信息
2025-08-21 22:45:58,340 - INFO - 成功获取到 2 个职位信息
2025-08-21 22:45:58,340 - INFO - 用户信息已保存到文件: user_info_20250821_224558.json
2025-08-21 22:46:00,524 - INFO - 浏览器已关闭
2025-08-21 22:46:00,524 - INFO - 自动化程序执行完成，浏览器将保持打开状态
2025-08-21 22:46:00,524 - INFO - 您可以手动查看页面内容，完成后请关闭浏览器
2025-08-21 23:25:47,671 - INFO - 日志系统初始化成功
2025-08-21 23:25:47,671 - INFO - 开始运行BOSS直聘自动化程序 V2.0...
2025-08-21 23:25:47,711 - INFO - ====== WebDriver manager ======
2025-08-21 23:25:51,179 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 23:25:51,800 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 23:25:52,855 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\139.0.7258.138\chromedriver-win32/chromedriver.exe] found in cache
2025-08-21 23:25:54,684 - INFO - 使用自动下载的chromedriver
2025-08-21 23:25:54,716 - INFO - Chrome浏览器驱动设置成功
2025-08-21 23:25:54,717 - INFO - 访问登录页面: https://www.zhipin.com/web/user/?ka=header-login
2025-08-21 23:25:55,353 - INFO - 等待用户登录（超时时间：300秒）...
2025-08-21 23:25:55,354 - INFO - 请在浏览器中完成登录操作...
2025-08-21 23:30:55,587 - ERROR - 等待用户登录超时（300秒）
2025-08-21 23:30:55,587 - ERROR - 用户登录失败或超时
2025-08-21 23:30:55,588 - INFO - 自动化程序执行完成，浏览器将保持打开状态
2025-08-21 23:30:55,588 - INFO - 您可以手动查看页面内容，完成后请关闭浏览器
2025-08-21 23:36:46,188 - INFO - 日志系统初始化成功
2025-08-21 23:36:46,188 - INFO - 开始运行BOSS直聘自动化程序 V2.0...
2025-08-21 23:36:46,224 - INFO - ====== WebDriver manager ======
2025-08-21 23:36:49,731 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 23:36:50,811 - INFO - Get LATEST chromedriver version for google-chrome
2025-08-21 23:36:55,123 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\139.0.7258.138\chromedriver-win32/chromedriver.exe] found in cache
2025-08-21 23:36:56,915 - INFO - 使用自动下载的chromedriver
2025-08-21 23:36:56,941 - INFO - Chrome浏览器驱动设置成功
2025-08-21 23:36:56,941 - INFO - 访问登录页面: https://www.zhipin.com/web/user/?ka=header-login
2025-08-21 23:36:57,507 - INFO - 等待用户登录（超时时间：300秒）...
2025-08-21 23:36:57,507 - INFO - 请在浏览器中完成登录操作...
2025-08-21 23:37:05,739 - ERROR - 等待用户登录时发生错误: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=139.0.7258.128)
Stacktrace:
	GetHandleVerifier [0x0x9bffc3+65331]
	GetHandleVerifier [0x0x9c0004+65396]
	(No symbol) [0x0x7b3f63]
	(No symbol) [0x0x7a38e0]
	(No symbol) [0x0x7c1709]
	(No symbol) [0x0x827e4c]
	(No symbol) [0x0x8424d9]
	(No symbol) [0x0x8212d6]
	(No symbol) [0x0x7f0910]
	(No symbol) [0x0x7f1784]
	GetHandleVerifier [0x0xc038b3+2439203]
	GetHandleVerifier [0x0xbfeae2+2419282]
	GetHandleVerifier [0x0x9e712a+225434]
	GetHandleVerifier [0x0x9d6e08+159096]
	GetHandleVerifier [0x0x9ddd5d+187597]
	GetHandleVerifier [0x0x9c7ad8+96840]
	GetHandleVerifier [0x0x9c7c62+97234]
	GetHandleVerifier [0x0x9b277a+9962]
	BaseThreadInitThunk [0x0x754a5d49+25]
	RtlInitializeExceptionChain [0x0x7748d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7748d281+561]

2025-08-21 23:37:05,739 - ERROR - 用户登录失败或超时
2025-08-21 23:37:05,740 - INFO - 自动化程序执行完成，浏览器将保持打开状态
2025-08-21 23:37:05,740 - INFO - 您可以手动查看页面内容，完成后请关闭浏览器
