# 智能招聘系统流程图

## 1. 系统整体架构流程

```mermaid
graph TB
    A[用户登录] --> B[选择功能模块]
    B --> C{功能选择}
    
    C -->|JD生成| D[JD生成模块]
    C -->|简历筛选| E[简历筛选模块]
    C -->|平台管理| F[招聘平台绑定]
    
    D --> D1[4轮对话收集需求]
    D1 --> D2[AI生成JD]
    D2 --> D3[JD确认保存]
    
    E --> E1[上传简历文件]
    E1 --> E2[多轮AI筛选]
    E2 --> E3[生成筛选报告]
    
    F --> F1[绑定Boss直聘]
    F --> F2[绑定智联招聘]
    F --> F3[绑定其他平台]
    
    D3 --> G[结果存储]
    E3 --> G
    G --> H[历史记录查看]
```

## 2. JD生成详细流程

```mermaid
graph TB
    A[开始JD生成] --> B[用户输入岗位基本信息]
    B --> C[第1轮：核心职责确定]
    
    C --> C1{AI生成职责选项}
    C1 --> C2[用户选择核心职责]
    C2 --> D[第2轮：核心技能需求]
    
    D --> D1{AI生成技能选项}
    D1 --> D2[用户选择必备技能]
    D2 --> E[第3轮：扩展技能工具]
    
    E --> E1{AI生成工具选项}
    E1 --> E2[用户选择优先技能]
    E2 --> F[第4轮：简历加分项]
    
    F --> F1{AI生成加分项选项}
    F1 --> F2[用户选择加分项]
    F2 --> G[信息收集完成]
    
    G --> H[AI生成完整JD]
    H --> I[用户确认修改]
    I --> J{是否满意?}
    
    J -->|是| K[保存JD到JD库]
    J -->|否| L[修改要求]
    L --> H
    
    K --> M[生成配套评估标准]
    M --> N[JD生成完成]

    subgraph "AI模型配置"
        O[阿里云通义千问]
        P[qwen-plus模型]
        Q[系统Prompt模板]
    end
    
    C1 --> O
    D1 --> O  
    E1 --> O
    F1 --> O
    H --> O
```

## 3. 简历筛选详细流程

```mermaid
graph TB
    A[选择JD] --> B[上传简历文件]
    B --> C{文件格式识别}
    
    C -->|PDF| D1[PDF文本提取]
    C -->|图片| D2[OCR图像识别]
    C -->|文本| D3[直接读取]
    
    D1 --> E[简历文本预处理]
    D2 --> E
    D3 --> E
    
    E --> F[第1轮：初筛]
    F --> F1{硬性门槛检查}
    F1 -->|不符合| F2[初筛淘汰]
    F1 -->|符合| G[第2轮：专业画像分析]
    
    G --> G1{专业能力评估}
    G1 --> H[第2.5轮：匹配度评估]
    H --> H1{JD匹配度分析}
    H1 -->|不匹配| H2[二筛淘汰]
    H1 -->|匹配| I[第3轮：终极筛选]
    
    I --> I1{综合评分}
    I1 -->|低分| I2[最终淘汰]
    I1 -->|高分| J[最终通过]
    
    F2 --> K[生成筛选报告]
    H2 --> K
    I2 --> K
    J --> K
    
    K --> L[保存到Result目录]
    L --> M[更新筛选历史]

    subgraph "AI模型配置"
        N1[第1轮：qwen-plus]
        N2[第2轮：qwen-plus] 
        N3[第2.5轮：qwen-plus]
        N4[第3轮：qwen-plus]
        N5[VL识别：qwen-vl-plus]
    end
    
    D2 --> N5
    F --> N1
    G --> N2
    H --> N3  
    I --> N4
```

## 4. 技术架构流程

```mermaid
graph LR
    subgraph "前端层"
        A1[HTML5界面]
        A2[CSS3样式]
        A3[JavaScript交互]
    end
    
    subgraph "API网关层"
        B1[JD生成API:8000]
        B2[简历筛选API:8008]
        B3[CORS中间件]
    end
    
    subgraph "业务逻辑层"
        C1[JD生成器]
        C2[简历评估器]
        C3[权重管理器]
        C4[用户状态管理]
    end
    
    subgraph "AI服务层"
        D1[阿里云通义千问]
        D2[qwen-plus模型]
        D3[qwen-vl-plus模型]
    end
    
    subgraph "数据存储层"
        E1[JD库]
        E2[简历文件]
        E3[筛选结果]
        E4[用户会话]
        E5[配置文件]
    end
    
    A1 --> B1
    A1 --> B2
    B1 --> C1
    B2 --> C2
    C1 --> D1
    C2 --> D1
    C1 --> E1
    C2 --> E3
    C3 --> E5
    C4 --> E4

    subgraph "外部平台"
        F1[Boss直聘]
        F2[智联招聘]
        F3[前程无忧]
    end
    
    C2 --> F1
    C2 --> F2
    C2 --> F3
```

## 5. 数据流转图

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端界面
    participant J as JD生成API
    participant R as 简历筛选API
    participant AI as AI模型
    participant DB as 数据存储

    Note over U,DB: JD生成流程
    U->>F: 输入岗位信息
    F->>J: POST /api/v1/conversation
    J->>AI: 调用qwen-plus生成问题
    AI->>J: 返回结构化问题
    J->>F: 返回问题选项
    F->>U: 展示选择题
    U->>F: 选择答案
    Note over U,F: 重复4轮对话
    F->>J: POST /api/v1/generate_jd
    J->>AI: 调用qwen-plus生成JD
    AI->>J: 返回完整JD
    J->>DB: 保存JD到JD库
    J->>F: 返回生成结果

    Note over U,DB: 简历筛选流程  
    U->>F: 选择JD + 上传简历
    F->>R: POST /start_task
    R->>AI: 第1轮初筛(qwen-plus)
    AI->>R: 返回初筛结果
    R->>AI: 第2轮画像分析(qwen-plus)  
    AI->>R: 返回专业画像
    R->>AI: 第2.5轮匹配评估(qwen-plus)
    AI->>R: 返回匹配度
    R->>AI: 第3轮终极筛选(qwen-plus)
    AI->>R: 返回最终评分
    R->>DB: 保存筛选结果
    R->>F: 返回筛选报告
```

### 🎯 核心Prompt节点配置

#### 系统级Prompt (JD生成专用助手)
- **角色定义**: 专业JD生成助手
- **核心原则**: 精简高效、业务价值导向、逻辑严谨
- **技能配比策略**: 必备技能3-4个，优先技能2-3个，加分项3-4个

#### 轮次专用Prompt
1. **第1轮**: 核心职责确定 → 具体工作内容收集
2. **第2轮**: 核心技能需求 → 必备专业能力定义  
3. **第3轮**: 扩展技能工具 → 优先技能和工具确定
4. **第4轮**: 简历加分项 → 客观加分项权重设置

#### 生成控制Prompt
- **JD模板严格控制**: 岗位职责、任职要求、基本信息
- **长度限制**: 整个JD不超过800字
- **格式要求**: 全部使用numbered list
- **业务化转换**: 专业术语转换为业务价值描述

生成控制的

**模型**: 统一使用阿里云通义千问 `qwen-plus`，确保输出一致性和质量稳定性。