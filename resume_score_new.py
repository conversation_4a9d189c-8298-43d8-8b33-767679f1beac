import asyncio
import json
import os
import threading
import uuid
from datetime import datetime
from pathlib import Path
from typing import List
import time
import uvicorn
from fastapi import FastAPI, File, UploadFile, Form, HTTPException, Body, BackgroundTasks, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel, Field
import requests
import pandas as pd
import io
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
# 导入配置和工具
from config import config
from evaluator import ResumeEvaluator
from jd_extractor import JDExtractor
from utils import setup_logging, ThreadSafeCounter, ProgressMonitor, process_uploaded_file, read_jd, \
    extract_position_name, extract_candidate_basic_info

from user_state_manager import user_state
from bosszp.boss_automation_drissionpage import BossAutomation


# 初始化日志
logger = setup_logging()

# 验证配置
config_errors = config.validate_config()
if config_errors:
    logger.error("配置验证失败:")
    for error in config_errors:
        logger.error(f" - {error}")
    raise ValueError("配置验证失败，请检查配置文件")

# 全局计数器
processed_counter = ThreadSafeCounter()
success_counter = ThreadSafeCounter()

# 创建评估器实例
evaluator = ResumeEvaluator()



# 统一登录请求模型
class LoginRequest(BaseModel):
    login_type: str = Field(..., description="登录类型：normal 或 boss")
    username: str = Field(None, description="用户名（普通登录时使用）")
    password: str = Field(None, description="密码（普通登录时使用）")
    boss_action: str = Field("login_boss", description="Boss操作类型（boss登录时使用）")

# 统一登录响应模型
class LoginResponse(BaseModel):
    success: bool
    message: str
    user_info: dict = None
    login_type: str = None
    boss_status: str = None  # Boss自动化状态

# 创建FastAPI应用
app = FastAPI(title="智能简历评估系统", description="基于Qwen API的智能简历评估系统")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 协作API数据模型
class ResumeData(BaseModel):
    """简历数据模型"""
    candidate_name: str
    resume_content: str  # 简历文本内容
    resume_format: str = "txt"  # 简历格式：txt, pdf, image
    source_url: str = None  # 来源URL
    crawl_time: str = None  # 爬取时间
    additional_info: dict = None  # 额外信息

class JobDescription(BaseModel):
    """职位描述模型"""
    job_title: str
    jd_content: str  # JD内容
    company_name: str = None
    salary_range: str = None
    requirements: dict = None

class CollaborationRequest(BaseModel):
    """协作评估请求模型"""
    resumes: List[ResumeData]
    job_description: JobDescription
    evaluation_config: dict = None  # 评估配置
    callback_url: str = None  # 回调URL



@app.get("/")
def root():
    """返回前端HTML页面"""
    try:
        frontend_path = os.path.join('frontend', 'resume_screening_tool.html')
        with open(frontend_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        return HTMLResponse(content=html_content)
    except FileNotFoundError:
        return HTMLResponse(content="""
        <html>
        <body>
            <h1>智能简历评估系统</h1>
            <p>前端文件未找到，请确保 frontend/resume_screening_tool.html 文件存在</p>
            <p><a href="/docs">查看API文档</a></p>
        </body>
        </html>
        """)


@app.get("/status")
def get_status():
    """获取系统状态"""
    return {
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "processed_count": processed_counter.get(),
        "success_count": success_counter.get(),
        "api_key_configured": bool(config.ALIYUN_API_KEY),
        "jd_file_exists": os.path.exists(config.JD_PATH),
        "prompt_files_exist": [os.path.exists(path) for path in config.PROMPT_PATHS],
        "result_dirs": "动态创建",
        "max_workers": config.MAX_WORKERS,
        "host": config.HOST,
        "port": config.PORT
    }

# 简化的任务状态管理
TASK_STATES = {}




# ==================== 登录接口 ====================
# 统一登录接口：使用 /login 进行所有类型的登录
# 支持普通登录和Boss自动化登录
# ================================================

@app.post("/login")
async def login(request: LoginRequest):
    """统一登录接口，支持普通登录和Boss登录"""
    try:
        login_type = request.login_type
        logger.info(f"🔐 统一登录请求: {login_type}")
        
        if login_type == "normal":
            # 普通登录

            if not request.username or not request.password:
                return LoginResponse(
                    success=False,
                    message="普通登录需要提供用户名和密码",
                    login_type=login_type
                )
            
            username = request.username
            password = request.password
            
            # 简单的密码验证
            if password == "admin":
                user_info = {
                    "姓名": username,
                    "公司": "用户公司",
                    "头像": username[0].upper() if username else "U",
                    "登录时间": datetime.now().isoformat(),
                    "登录类型": "normal"
                }
                user_state.login_type = "normal"
                # 使用统一的用户状态管理器
                user_state.set_current_user(user_info)
                
                logger.info(f"✅ 普通用户 {username} 登录成功")
                
                user_state.save_user_info()
                return LoginResponse(
                    success=True,
                    message="普通登录成功",
                    user_info=user_info,
                    login_type=login_type
                )
            else:
                logger.warning(f"❌ 用户 {username} 密码错误")
                return LoginResponse(
                    success=False,
                    message="密码错误，请使用 admin 作为密码",
                    login_type=login_type
                )
                
        elif login_type == "boss":
            # Boss登录 - 使用Boss自动化登录
            try:
                # 初始化Boss自动化实例
                global _boss_automation_instance
                user_state.login_type = "boss"
                if _boss_automation_instance is None:
                    _boss_automation_instance = BossAutomation(headless=False)
                
                # 执行Boss自动化登录和信息提取
                result = _boss_automation_instance.login_and_extract_user_info()
                
                if result:
                    
                    # 构建统一的用户信息格式
                    user_info = {
                        "姓名": result.get('姓名', ''),
                        "公司": result.get('公司', ''),
                        "头像": result.get('头像', '') or (result.get('姓名', 'Boss用户')[0].upper() if result.get('姓名', 'Boss用户') else 'B'),
                        "登录时间": datetime.now().isoformat(),
                        "登录类型": "boss",
                        "boss_info": result  # 保留原始Boss信息
                    }
                    # 使用统一的用户状态管理器存储用户信息
                    user_state.set_current_user(user_info)
                    user_state.save_user_info()
                    logger.info(f"✅ Boss用户登录成功，用户信息: {user_info['姓名']}")
                    
                    
                    return LoginResponse(
                        success=True,
                        message="Boss登录成功（自动化提取信息）",
                        user_info=user_info,
                        login_type=login_type,
                        boss_status="automation_success"
                    )
                else:
                    # 如果自动化失败，返回失败信息，不进入主页面
                    logger.warning(f"⚠️ Boss自动化失败，无法提取用户信息")
                    return LoginResponse(
                        success=False,
                        message="Boss自动化失败，无法获取用户信息，请检查网络连接或稍后重试",
                        login_type=login_type,
                        boss_status="automation_failed"
                    )
                    
            except Exception as e:
                logger.error(f"Boss自动化登录失败: {e}")
                # 自动化异常，返回失败信息，不进入主页面
                return LoginResponse(
                    success=False,
                    message=f"Boss自动化异常: {str(e)}，请检查网络连接或稍后重试",
                    login_type=login_type,
                    boss_status="automation_error"
                )
            
        else:
            return LoginResponse(
                success=False,
                message=f"不支持的登录类型: {login_type}",
                login_type=login_type
            )
            
    except Exception as e:
        logger.error(f"❌ 统一登录接口异常: {e}")
        return LoginResponse(
            success=False,
            message=f"登录接口异常: {str(e)}",
            login_type=request.login_type if hasattr(request, 'login_type') else None
        )

# Boss自动化实例，用于执行自动化操作
_boss_automation_instance = None



@app.get("/check_boss_login_status")
async def check_boss_login_status():
    """检查Boss登录状态"""
    try:
        global _boss_automation_instance
        
        if _boss_automation_instance is None:
            return {
                "success": False,
                "message": "Boss自动化实例未初始化",
                "status": "not_initialized"
            }
        
        # 获取当前用户信息
        current_user = user_state.get_current_user()
        if current_user and current_user.get('登录类型') == 'boss':
            return {
                "success": True,
                "message": "Boss用户已登录",
                "status": "logged_in",
                "user_info": current_user,
                "boss_info": current_user.get('boss_info', {})
            }
        else:
            return {
                "success": False,
                "message": "Boss自动化完成但用户未登录",
                "status": "automation_complete_no_login"
            }
  
    except Exception as e:
        logger.error(f"检查Boss登录状态失败: {e}")
        return {
            "success": False,
            "message": f"检查Boss登录状态失败: {str(e)}",
            "status": "error"
        }


@app.post("/close_boss_automation")
async def close_boss_automation():
    """关闭Boss自动化实例"""
    try:
        global _boss_automation_instance
        
        if _boss_automation_instance:
            _boss_automation_instance.close_driver()
            _boss_automation_instance = None
            
            logger.info("Boss自动化实例已关闭")
            return {
                "success": True,
                "message": "Boss自动化实例已关闭"
            }
        else:
            return {
                "success": False,
                "message": "Boss自动化实例不存在"
            }
            
    except Exception as e:
        logger.error(f"关闭Boss自动化实例失败: {e}")
        return {
            "success": False,
            "message": f"关闭Boss自动化实例失败: {str(e)}"
        }

@app.get("/api/refresh_jd")
async def refresh_jd():
    """刷新JD"""
    try:
        user_name = user_state.get_current_user_name()
        if user_state.login_type == "boss":
            jobs = _boss_automation_instance.extract_jobs()
            for job in jobs:
                user_state.save_jd(job['职位详情'], job['职位名称'], user_state.get_current_user_name(), 'md')
        jds = user_state.get_user_jds(user_name)
        jd_list = [
            {
                "id": jd + '.md',
                "title": jd,
                "filename": jd + '.md',
                "file_size": len(jds[jd]),
                "modified_time": time.time()
            }
            for jd in jds.keys()
        ]
        return {"success": True,"jd_list": jd_list}
    except Exception as e:
        logger.error(f"获取用户JD库失败: {e}")
        return {"success": False,"message": f"获取用户JD库失败: {str(e)}"}


@app.get("/api/jd_list")
async def get_jd_list():
    """获取用户的JD列表"""
    try:
        user_name = user_state.get_current_user_name()
        if user_state.login_type == "boss" and not user_state.user_sessions.get(user_name,{}).get('jd',{}):
            jobs = _boss_automation_instance.extract_jobs()
            for job in jobs:
                user_state.save_jd(job, job['职位名称'], user_state.get_current_user_name(), 'md')
        jds = user_state.get_user_jds(user_name)
        jd_list = [
            {
                "id": jd,
                "title": jd,
                "filename": jd + '.md',
                "file_size": len(jds[jd]),
                "modified_time": time.time()
            }
            for jd in jds.keys()
        ]
        return {"success": True,"jd_list": jd_list}
    except Exception as e:
        logger.error(f"获取用户JD库失败: {e}")
        return {"success": False,"message": f"获取用户JD库失败: {str(e)}"}


@app.get("/api/jd_content/{jd_id}")
async def get_jd_content(jd_id: str):
    try:
        user_name = user_state.get_current_user_name()
        jd_content = user_state.get_jd_content(jd_id, user_name)
        return {"success": True, "content": jd_content}
    except Exception as e:
        logger.error(f"获取JD内容失败: {e}")
        return {"success": False, "message": f"获取JD内容失败: {e}"}





#===========================================================
# 爬虫部分
#===========================================================

@app.get("/crawl_status/{task_id}")
async def get_crawl_status(task_id: str):
    """获取爬取任务状态"""
    try:
        init_crawl_tasks() # 确保字典已初始化
        if task_id not in CRAWL_TASKS:
            return {
                "success": False,
                "message": "爬取任务不存在"
            }
        
        status = CRAWL_TASKS[task_id]
        return {
            "success": True,
            "status": status
        }
        
    except Exception as e:
        logger.error(f"获取爬取状态失败: {str(e)}")
        return {
            "success": False,
            "message": f"获取爬取状态失败: {str(e)}"
        }

# 存储爬取任务状态
CRAWL_TASKS = None

def init_crawl_tasks():
    """初始化 CRAWL_TASKS 字典"""
    global CRAWL_TASKS
    if CRAWL_TASKS is None:
        # 直接使用普通字典，避免进程间共享的问题
        CRAWL_TASKS = {}
@app.post("/trigger_crawl")
async def trigger_crawl(request: Request, background_tasks: BackgroundTasks):
    """触发爬虫系统开始爬取简历"""
    try:
        # 确保 CRAWL_TASKS 已初始化
        init_crawl_tasks()
        
        data = await request.json()
        job_title = data.get("job_title", "")
        job_description = data.get("job_description", "")
        crawl_count = data.get("crawl_count", 20)
        platforms = data.get("platforms", ["boss", "zhaopin", "qiancheng"])
        
        # 生成爬取任务ID
        task_id = str(uuid.uuid4())
        
        # 初始化爬取任务状态
        CRAWL_TASKS[task_id] = {
            "status": "pending",
            "job_title": job_title,
            "job_description": job_description,
            "crawl_count": crawl_count,
            "platforms": platforms,
            "completed": 0,
            "total": crawl_count,
            "resumes": [],
            "error": None,
            "start_time": datetime.now().isoformat()
        }
        
        # 启动爬取任务（异步）
        background_tasks.add_task(process_crawl_task, task_id, data)

        logger.info(f"爬取任务已启动: {task_id}, 职位: {job_title}")
        
        return {
            "success": True,
            "task_id": task_id,
            "message": "爬取任务已启动"
        }
        
    except Exception as e:
        logger.error(f"触发爬取失败: {str(e)}")
        return {
            "success": False,
            "message": f"触发爬取失败: {str(e)}"
        }



async def process_crawl_task(task_id: str, crawl_data: dict):
    """处理爬取任务 - 调用爬虫程序"""
    try:
        # 更新任务状态为运行中
        init_crawl_tasks() # 确保字典已初始化
        CRAWL_TASKS[task_id]["status"] = "running"
        
        job_title = crawl_data.get("job_title", "")
        crawl_count = crawl_data.get("crawl_count", 20)
        
        logger.info(f"🚀 开始调用爬虫程序: {task_id}, 职位: {job_title}, 目标数量: {crawl_count}")
        
        # 调用爬虫程序的API
        try:
            # 首先检查岗位匹配
            logger.info("🔍 检查岗位匹配...")
            match_response = await check_job_match_with_crawler(job_title)
            
            if not match_response.get("success"):
                raise Exception(f"岗位匹配失败: {match_response.get('message', '未知错误')}")
            
            logger.info("✅ 岗位匹配成功，开始爬取简历...")
            
            # 开始爬取简历
            collection_response = await start_collection_with_crawler(job_title, crawl_count)
            
            # 检查是否有task_id，这是成功的关键标志
            collection_task_id = collection_response.get("task_id")
            if not collection_task_id:
                raise Exception(f"启动爬取失败: {collection_response.get('message', '未知错误')}")
            
            logger.info(f"✅ 爬取任务已启动: {collection_task_id}")
            
            # 轮询爬取进度
            await monitor_crawl_progress(task_id, collection_task_id, crawl_count)
            
        except Exception as e:
            logger.error(f"调用爬虫程序失败: {str(e)}")
            CRAWL_TASKS[task_id]["status"] = "failed"
            CRAWL_TASKS[task_id]["error"] = str(e)
            CRAWL_TASKS[task_id]["end_time"] = datetime.now().isoformat()
            return
        
    except Exception as e:
        logger.error(f"处理爬取任务 {task_id} 失败: {str(e)}")
        CRAWL_TASKS[task_id]["status"] = "failed"
        CRAWL_TASKS[task_id]["error"] = str(e)
        CRAWL_TASKS[task_id]["end_time"] = datetime.now().isoformat()




async def check_job_match_with_crawler(job_title: str):
    """调用爬虫程序的岗位匹配API"""
    try:
        import aiohttp
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                "http://localhost:8000/api/check-job-match",
                json={
                    "job_title": job_title,
                    "max_results": 1  # 只是检查，不需要实际爬取
                },
                timeout=30
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    logger.info(f"岗位匹配结果: {result}")
                    return result
                else:
                    logger.error(f"岗位匹配API调用失败: HTTP {response.status}")
                    return {"success": False, "message": f"API调用失败: HTTP {response.status}"}
    except Exception as e:
        logger.error(f"岗位匹配失败: {str(e)}")
        return {"success": False, "message": f"岗位匹配失败: {str(e)}"}


async def start_collection_with_crawler(job_title: str, crawl_count: int):
    """调用爬虫程序的开始爬取API"""
    try:
        import aiohttp
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                "http://localhost:8000/api/start-collection",
                json={
                    "job_title": job_title,
                    "max_results": crawl_count,
                    "browser_initialized": True  # 使用已初始化的浏览器
                },
                timeout=30
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    logger.info(f"爬取任务启动结果: {result}")
                    # 爬虫系统返回的格式是 {"task_id": "task_1", "message": "收集任务已启动"}
                    # 这是成功的响应，不需要检查success字段
                    return result
                else:
                    logger.error(f"爬取任务启动API调用失败: HTTP {response.status}")
                    return {"success": False, "message": f"API调用失败: HTTP {response.status}"}
    except Exception as e:
        logger.error(f"启动爬取失败: {str(e)}")
        return {"success": False, "message": f"启动爬取失败: {str(e)}"}


async def monitor_crawl_progress(task_id: str, collection_task_id: str, target_count: int):
    """监控爬取进度"""
    try:
        import aiohttp
        
        logger.info(f"开始监控爬取进度: {collection_task_id}")
        
        # 从任务状态中获取job_title
        job_title = CRAWL_TASKS[task_id].get("job_title", "")
        
        async with aiohttp.ClientSession() as session:
            while True:
                try:
                    # 获取爬取状态
                    async with session.get(
                        f"http://localhost:8000/api/status/{collection_task_id}",
                        timeout=10
                    ) as response:
                        if response.status == 200:
                            status_data = await response.json()
                            logger.info(f"爬取状态: {status_data}")
                            
                            # 更新我们的任务状态
                            current_count = status_data.get("results_count", 0)
                            progress = status_data.get("progress", 0)
                            status = status_data.get("status", "running")
                            
                            CRAWL_TASKS[task_id]["completed"] = current_count
                            CRAWL_TASKS[task_id]["progress"] = progress
                            
                            if status == "completed":
                                # 爬取完成，获取简历列表
                                logger.info("爬取完成，获取简历列表...")
                                resumes = await get_crawled_resumes(job_title)
                                CRAWL_TASKS[task_id]["resumes"] = resumes
                                CRAWL_TASKS[task_id]["status"] = "completed"
                                CRAWL_TASKS[task_id]["end_time"] = datetime.now().isoformat()
                                logger.info(f"爬取任务完成: {task_id}, 共获取 {len(resumes)} 份简历")
                                break
                            elif status == "failed":
                                CRAWL_TASKS[task_id]["status"] = "failed"
                                CRAWL_TASKS[task_id]["error"] = "爬取任务失败"
                                CRAWL_TASKS[task_id]["end_time"] = datetime.now().isoformat()
                                logger.error(f"爬取任务失败: {collection_task_id}")
                                break
                            else:
                                # 继续监控
                                await asyncio.sleep(5)
                        else:
                            logger.error(f"获取爬取状态失败: HTTP {response.status}")
                            await asyncio.sleep(10)
                            
                except Exception as e:
                    logger.error(f"监控爬取进度异常: {str(e)}")
                    await asyncio.sleep(10)
                    
    except Exception as e:
        logger.error(f"监控爬取进度失败: {str(e)}")
        CRAWL_TASKS[task_id]["status"] = "failed"
        CRAWL_TASKS[task_id]["error"] = str(e)
        CRAWL_TASKS[task_id]["end_time"] = datetime.now().isoformat()


async def get_crawled_resumes(job_title: str):
    """获取爬取到的简历列表"""
    try:
        import aiohttp
        
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"http://localhost:8000/api/existing-resumes?job_title={job_title}",
                timeout=10
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    resumes = result.get("resumes", [])
                    
                    # 转换为评估系统需要的格式
                    formatted_resumes = []
                    for resume in resumes:
                        formatted_resume = {
                            "candidate_name": resume.get("candidate_name", "未知候选人"),
                            "source_url": resume.get("file_path", ""),
                            "crawl_time": datetime.fromtimestamp(resume.get("created_time", time.time())).isoformat(),
                            "platform": "boss",
                            "file_path": resume.get("file_path", "")
                        }
                        formatted_resumes.append(formatted_resume)
                    
                    logger.info(f"获取到 {len(formatted_resumes)} 份简历")
                    return formatted_resumes
                else:
                    logger.error(f"获取简历列表失败: HTTP {response.status}")
                    return []
    except Exception as e:
        logger.error(f"获取简历列表失败: {str(e)}")
        return []


# 爬虫通信API端点

@app.get("/pending_crawl_tasks")
async def get_pending_crawl_tasks():
    """获取待处理的爬取任务（供爬虫系统调用）"""
    try:
        init_crawl_tasks() # 确保字典已初始化
        
        # 添加调试信息
        logger.info(f"🔍 检查待处理任务，当前CRAWL_TASKS数量: {len(CRAWL_TASKS)}")
        for task_id, task_data in CRAWL_TASKS.items():
            logger.info(f"   任务 {task_id}: 状态={task_data.get('status')}")
        
        # 查找状态为pending或waiting_for_crawler的任务
        pending_tasks = []
        for task_id, task_data in CRAWL_TASKS.items():
            if task_data.get("status") in ["pending", "waiting_for_crawler"]:
                pending_tasks.append({
                    "task_id": task_id,
                    **task_data
                })
        
        logger.info(f"📋 找到 {len(pending_tasks)} 个待处理任务")
        
        if pending_tasks:
            # 返回第一个待处理任务
            task = pending_tasks[0]
            # 标记为已分配
            CRAWL_TASKS[task["task_id"]]["status"] = "assigned"
            logger.info(f"✅ 分配任务: {task['task_id']}, 职位: {task.get('job_title')}")
            return {
                "success": True,
                "task": task
            }
        else:
            logger.info("❌ 没有找到待处理任务")
            return {
                "success": True,
                "task": None
            }
            
    except Exception as e:
        logger.error(f"获取待处理爬取任务失败: {str(e)}")
        return {
            "success": False,
            "message": f"获取待处理爬取任务失败: {str(e)}"
        }


@app.post("/update_crawl_progress")
async def update_crawl_progress(request: Request):
    """更新爬取进度（供爬虫系统调用）"""
    try:
        init_crawl_tasks() # 确保字典已初始化
        data = await request.json()
        task_id = data.get("task_id")
        completed = data.get("completed", 0)
        total = data.get("total", 0)
        resumes = data.get("resumes", [])
        
        if task_id not in CRAWL_TASKS:
            return {
                "success": False,
                "message": "爬取任务不存在"
            }
        
        # 更新进度
        CRAWL_TASKS[task_id]["completed"] = completed
        CRAWL_TASKS[task_id]["total"] = total
        if resumes:
            CRAWL_TASKS[task_id]["resumes"] = resumes
        
        logger.info(f"爬取任务 {task_id} 进度更新: {completed}/{total}")
        
        return {
            "success": True,
            "message": "进度更新成功"
        }
        
    except Exception as e:
        logger.error(f"更新爬取进度失败: {str(e)}")
        return {
            "success": False,
            "message": f"更新爬取进度失败: {str(e)}"
        }


@app.post("/complete_crawl_task")
async def complete_crawl_task(request: Request, background_tasks: BackgroundTasks):
    """完成爬取任务（供爬虫系统调用）"""
    try:
        init_crawl_tasks() # 确保字典已初始化
        data = await request.json()
        task_id = data.get("task_id")
        resumes = data.get("resumes", [])
        status = data.get("status", "completed")
        
        logger.info(f"收到完成爬取任务请求: task_id={task_id}, status={status}, resumes_count={len(resumes)}")
        logger.info(f"当前CRAWL_TASKS中的任务: {list(CRAWL_TASKS.keys())}")
        
        if task_id not in CRAWL_TASKS:
            logger.error(f"任务 {task_id} 不存在于CRAWL_TASKS中")
            return {
                "success": False,
                "message": "爬取任务不存在"
            }
        
        # 更新任务状态
        old_status = CRAWL_TASKS[task_id].get("status", "unknown")
        logger.info(f"更新任务状态: {old_status} -> {status}")
        
        CRAWL_TASKS[task_id]["status"] = status
        CRAWL_TASKS[task_id]["resumes"] = resumes
        CRAWL_TASKS[task_id]["end_time"] = datetime.now().isoformat()
        
        # 验证更新是否成功
        updated_status = CRAWL_TASKS[task_id].get("status", "unknown")
        logger.info(f"更新后的状态: {updated_status}")
        
        logger.info(f"爬取任务 {task_id} 完成，共爬取 {len(resumes)} 份简历")
        
        # 爬取完成后，等待前端调用评估API
        logger.info(f"✅ 爬取任务完成，等待前端调用评估API...")
        
        return {
            "success": True,
            "message": "爬取任务完成"
        }
        
    except Exception as e:
        logger.error(f"完成爬取任务失败: {str(e)}")
        return {
            "success": False,
            "message": f"完成爬取任务失败: {str(e)}"
        }


@app.post("/clear_task_state/{task_id}")
async def clear_task_state(task_id: str):
    """清理指定任务的状态，用于重新开始评估"""
    try:
        if task_id in TASK_STATES:
            del TASK_STATES[task_id]
            return {"success": True, "message": f"任务 {task_id} 状态已清理"}
        else:
            return {"success": False, "message": "任务不存在"}
    except Exception as e:
        return {"success": False, "message": f"清理失败: {str(e)}"}

@app.post("/clear_all_task_states")
async def clear_all_task_states():
    """清理所有任务状态"""
    try:
        TASK_STATES.clear()
        return {"success": True, "message": "所有任务状态已清理"}
    except Exception as e:
        return {"success": False, "message": f"清理失败: {str(e)}"}


@app.get("/get_task_states")
async def get_task_states():
    """获取所有任务状态信息"""
    try:
        tasks = []
        for task_id, state in TASK_STATES.items():
            task_info = dict(state)
            task_info['task_id'] = task_id
            tasks.append(task_info)
        return {"success": True, "tasks": tasks}
    except Exception as e:
        return {"success": False, "message": f"获取任务状态失败: {str(e)}"}


#===========================================================
# 上传、评估
#===========================================================


@app.post("/upload_and_extract")
async def upload_and_extract(
        jd_file: UploadFile = File(None),
        files: List[UploadFile] = File(...),
        candidate_names: List[str] = Form(...)
):
    """上传简历文件并立即提取基本信息"""
    try:
        if len(files) != len(candidate_names):
            return {"success": False, "message": "文件数量与候选人姓名数量不匹配"}

        # 准备JD内容
        jd_bytes = await jd_file.read()
        print(jd_file.filename)
        jd_content = jd_bytes.decode('utf-8', errors='ignore')
        
        # 处理JD文件格式转换
        if jd_file.filename.endswith('.md') or jd_file.filename.endswith('.txt'):
            try:
                logger.info(f"开始转换MD格式JD: {jd_file.filename}")
                
                # 获取用户名称，构建用户JD目录路径
                user_name = user_state.get_current_user_name()
                
                # 构建用户JD目录路径
                user_jd_dir = os.path.join('user', user_name, 'jd')
                os.makedirs(user_jd_dir, exist_ok=True)
                
                json_filename = jd_file.filename.replace('.md', '.json').replace('.txt', '.json')
                json_path = os.path.join(user_jd_dir, json_filename)

                if os.path.exists(json_path):
                    logger.info(f"✅ 发现已存在的JSON文件: {json_path}")
                    with open(json_path, 'r', encoding='utf-8') as f:
                        jd_content = f.read()
                    try:
                        json.loads(jd_content)
                        logger.info(f"✅ JSON文件格式验证通过")
                    except json.JSONDecodeError:
                        jd_json = JDExtractor.md_to_json(jd_content)
                        jd_content = jd_json
                        with open(json_path, 'w', encoding='utf-8') as f:
                            f.write(jd_json)
                else:
                    jd_json = JDExtractor.md_to_json(jd_content)
                    jd_content = jd_json
                    with open(json_path, 'w', encoding='utf-8') as f:
                        f.write(jd_json)
                    logger.info(f"✅ 成功将MD格式JD转换为JSON格式并保存到用户目录: {json_path}")
            except Exception as e:
                logger.error(f"❌ MD转JSON失败: {e}")
                return {"success": False, "message": f"JD文件处理失败: {e}"}

        # 处理简历文件并提取基本信息
        candidates_info = []
        for f, name in zip(files, candidate_names):
            try:
                content = await f.read()
                
                # 设置当前处理的岗位信息，用于VL识别结果保存
                # 从JD文件名中提取岗位信息
                position_title = None
                if jd_file.filename:
                    import re
                    # 尝试从JD文件名中提取岗位信息
                    position_match = re.search(r'(.+?)_JD\.', jd_file.filename)
                    if position_match:
                        position_title = position_match.group(1)
                
                set_current_position(position_title)
                
                resume_text, is_vl_result = process_uploaded_file(content, f.filename, name)
                
                if not resume_text:
                    candidates_info.append({
                        "candidate_name": name,
                        "success": False,
                        "message": "无法提取文件内容"
                    })
                    continue
                
                # 提取候选人基本信息
                basic_info = extract_candidate_basic_info(resume_text)
                
                candidates_info.append({
                    "candidate_name": name,
                    "success": True,
                    "basic_info": basic_info,
                    "resume_text": resume_text[:1000] + "..." if len(resume_text) > 1000 else resume_text  # 截取前1000字符
                })
                
            except Exception as e:
                logger.error(f"处理候选人 {name} 的简历失败: {e}")
                candidates_info.append({
                    "candidate_name": name,
                    "success": False,
                    "message": str(e)
                })

        return {
            "success": True,
            "candidates": candidates_info,
            "jd_content": jd_content[:500] + "..." if len(jd_content) > 500 else jd_content  # 截取前500字符
        }
        
    except Exception as e:
        logger.error(f"上传和提取失败: {e}")
        return {"success": False, "message": f"上传和提取失败: {e}"}



@app.post("/start_task")
async def start_task(
        jd_file: UploadFile = File(None),
        files: List[UploadFile] = File(...),
        candidate_names: List[str] = Form(...),
        round_select: List[float] = Form(None)
):
    """启动新的评估任务（独立进程）。允许在评估中继续发起新任务。"""
    try:
        if len(files) != len(candidate_names):
            return {"success": False, "message": "文件数量与候选人姓名数量不匹配"}

        # 准备JD内容（仅支持上传）
        jd_bytes = await jd_file.read()
        jd_content = jd_bytes.decode('utf-8', errors='ignore')
        jd_content_original = jd_content  # 保存原始MD内容用于重新生成

        if jd_file.filename.endswith('.md') or jd_file.filename.endswith('.txt'):
            try:
                logger.info(f"开始转换MD格式JD: {jd_file.filename}")

                # 获取用户名称，构建用户JD目录路径
                user_name = user_state.get_current_user_name()
                
                # 构建用户JD目录路径
                user_jd_dir = os.path.join('user', user_name, 'jd')
                os.makedirs(user_jd_dir, exist_ok=True)
                
                # 检查是否已存在对应的JSON文件
                json_filename = jd_file.filename.replace('.md', '.json').replace('.txt', '.json')
                json_path = os.path.join(user_jd_dir, json_filename)

                if os.path.exists(json_path):
                    logger.info(f"✅ 发现已存在的JSON文件: {json_path}")
                    with open(json_path, 'r', encoding='utf-8') as f:
                        jd_content = f.read()

                    # 验证已存在的JSON文件格式是否正确
                    try:
                        json.loads(jd_content)
                        logger.info(f"✅ JSON文件格式验证通过")
                    except json.JSONDecodeError as json_error:
                        logger.warning(f"⚠️ 已存在的JSON文件格式有问题: {json_error}")
                        logger.info(f"🔄 将重新从MD生成JSON文件")

                        # 重新转换MD为JSON
                        jd_json = JDExtractor.md_to_json(jd_content_original)
                        jd_content = jd_json

                        # 重新保存修复后的JSON文件
                        with open(json_path, 'w', encoding='utf-8') as f:
                            f.write(jd_json)
                        logger.info(f"✅ 已重新生成并保存JSON文件: {json_path}")
                else:
                    # 转换MD为JSON
                    jd_json = JDExtractor.md_to_json(jd_content)
                    jd_content = jd_json

                    # 保存JSON文件到用户JD目录
                    with open(json_path, 'w', encoding='utf-8') as f:
                        f.write(jd_json)
                    logger.info(f"✅ 成功将MD格式JD转换为JSON格式并保存到用户目录: {json_path}")

            except Exception as e:
                logger.error(f"❌ MD转JSON失败: {e}")
                error_msg = f"JD文件处理失败: {e}"

                # 提供更详细的错误信息
                if "无法提取JD信息" in str(e):
                    error_msg += "\n建议：请检查JD内容是否完整，包含职位名称、职责描述等必要信息"
                elif "API调用失败" in str(e):
                    error_msg += "\n建议：请检查网络连接和API配置"
                elif "JSON" in str(e).upper() or "json" in str(e):
                    error_msg += "\n建议：AI返回内容格式异常，请重试或联系管理员"

                return {"success": False, "message": error_msg}
        else:
            logger.info(f"处理JD文件: {jd_file.filename} (非MD格式)")
            # 如果是非MD格式，也需要验证JSON格式
            if jd_file.filename.endswith('.json'):
                try:
                    json.loads(jd_content)
                    logger.info(f"✅ JSON格式验证通过")
                except json.JSONDecodeError as json_error:
                    logger.error(f"❌ 上传的JSON文件格式错误: {json_error}")
                    return {"success": False, "message": f"JSON文件格式错误: {json_error}"}

        # 提取岗位名称
        position_title = extract_position_name(jd_content)
        logger.info(f"提取的岗位名称: '{position_title}'")

        # 验证和清理岗位名称
        if not position_title or position_title.strip() == "":
            position_title = "招聘岗位"
        elif position_title.strip().upper() == "JD":
            position_title = "招聘岗位"
        elif position_title.strip() == "未知岗位":
            position_title = "招聘岗位"

        logger.info(f"最终使用的岗位名称: '{position_title}'")

        # 轮次选择
        if not round_select:
            round_select = config.ROUND_SELECT
        else:
            # 表单会传字符串，转换为float或int，并将1.0/2.0/3.0规范为整数（保留2.5）
            parsed = []
            for r in round_select:
                try:
                    val = float(r) if '.' in str(r) else int(r)
                    if isinstance(val, float) and abs(val - 2.5) > 1e-9 and val.is_integer():
                        val = int(val)
                    parsed.append(val)
                except Exception:
                    continue
            round_select = parsed or config.ROUND_SELECT

        # 读取简历文件内容
        resume_items = []
        for f, name in zip(files, candidate_names):
            content = await f.read()
            resume_items.append((f.filename, content, name))

        # 固定任务的时间戳，用于生成稳定的保存路径
        date_time = config.get_now_str()

        task_id = str(uuid.uuid4())
        TASK_STATES[task_id] = {
            "status": "starting",
            "created_at": datetime.now().isoformat(),
            "position_title": None,
            "total": len(resume_items),
            "completed": 0,
            "success": 0,
            "failed": 0,
            "details": [],
            "save_dir": None,
            "vl_dir": None
        }

        # 获取用户名称
        user_name = user_state.get_current_user_name()
        
        # 同步处理任务（不使用多进程）
        try:
            _run_task_sync(task_id, jd_content, resume_items, round_select, date_time, user_name)
        except Exception as e:
            logger.error(f"任务处理失败: {e}")
            TASK_STATES[task_id]["status"] = "failed"
            TASK_STATES[task_id]["error"] = str(e)

        return {"success": True, "task_id": task_id}
    except Exception as e:
        logger.error(f"启动任务失败: {e}")
        return {"success": False, "message": f"启动任务失败: {e}"}



@app.get("/task_status/{task_id}")
def task_status(task_id: str):
    """查询任务状态与进度"""
    try:
        if task_id not in TASK_STATES:
            return {"success": False, "message": "任务不存在"}
        state = dict(TASK_STATES[task_id])
        return {"success": True, "task": state}
    except Exception as e:
        return {"success": False, "message": str(e)}




@app.get("/get_all_tasks")
def get_all_tasks():
    """获取所有任务的状态和信息"""
    try:
        tasks = []
        for task_id, state in TASK_STATES.items():
            task_info = dict(state)
            task_info['task_id'] = task_id
            # 计算统计信息
            # 候选人总数始终使用 total 字段（这是真正的候选人总数）
            task_info['candidatesCount'] = task_info.get('total', 0)

            if 'details' in task_info:
                details = task_info['details']
                # details 包含已完成评估的候选人，用于计算通过率和平均分
                task_info['passedCount'] = len([d for d in details if d.get('qualified', False)])
                task_info['failedCount'] = len([d for d in details if not d.get('qualified', False)])
                if details:
                    avg_score = sum(d.get('final_score', 0) for d in details) / len(details)
                    task_info['averageScore'] = round(avg_score, 1)
                else:
                    task_info['averageScore'] = 0
            else:
                # 对于没有 details 的情况，使用 success/failed 字段
                task_info['passedCount'] = task_info.get('success', 0)
                task_info['failedCount'] = task_info.get('failed', 0)
                task_info['averageScore'] = 0

            # 格式化创建时间
            if 'created_at' in task_info:
                from datetime import datetime
                try:
                    created_time = datetime.fromisoformat(task_info['created_at'])
                    task_info['createTime'] = created_time.strftime('%Y-%m-%d %H:%M:%S')
                except:
                    task_info['createTime'] = task_info['created_at']
            else:
                task_info['createTime'] = '未知时间'

            # 设置任务标题
            position_title = task_info.get('position_title', '未知岗位')
            task_info['title'] = f"{position_title}评估任务"

            tasks.append(task_info)

        # 按创建时间倒序排列
        tasks.sort(key=lambda x: x.get('created_at', ''), reverse=True)

        return {"success": True, "tasks": tasks}
    except Exception as e:
        logger.error(f"获取任务列表失败: {e}")
        return {"success": False, "message": str(e), "tasks": []}


@app.get("/get_resume_content/{task_id}/{candidate_name}")
def get_resume_content_by_task(task_id: str, candidate_name: str):
    try:
        if task_id not in TASK_STATES:
            return {"success": False, "message": "任务不存在"}
        state = TASK_STATES[task_id]
        vl_dir = state.get("vl_dir")
        if not vl_dir:
            return {"success": False, "message": "任务未记录简历目录"}
        file_path = os.path.join(vl_dir, f"{candidate_name}_VL识别结果.txt")
        if not os.path.exists(file_path):
            return {"success": False, "message": "未找到简历内容文件"}
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        return {"success": True, "content": content, "path": file_path}
    except Exception as e:
        return {"success": False, "message": str(e)}


@app.get("/get_resume_content/{candidate_name}")
def get_resume_content(candidate_name: str):
    try:
        # 在所有任务的 vl_dir 中寻找
        for tid, state in TASK_STATES.items():
            vl_dir = state.get("vl_dir")
            if not vl_dir:
                continue
            file_path = os.path.join(vl_dir, f"{candidate_name}_VL识别结果.txt")
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                return {"success": True, "content": content, "path": file_path, "task_id": tid}
        return {"success": False, "message": "未找到简历内容文件"}
    except Exception as e:
        return {"success": False, "message": str(e)}



@app.get("/get_evaluation_reports/{task_id}/{candidate_name}")
def get_evaluation_reports_by_task(task_id: str, candidate_name: str):
    try:
        
        # 首先尝试从内存中的任务状态获取保存目录
        save_dir = None
        if task_id in TASK_STATES:
            state = TASK_STATES[task_id]
            save_dir = state.get("save_dir")
        
        # 如果内存中没有，尝试从文件系统中查找
        if not save_dir:
            # 获取用户名称，构建用户Result目录路径
            user_name = user_state.get_current_user_name()
            
            # 查找用户Result目录下的所有子目录
            result_dir = Path(path_manager.get_user_result_dir(user_name))
            if result_dir.exists():
                for subdir in result_dir.iterdir():
                    if subdir.is_dir():
                        # 检查目录中是否包含该候选人的报告
                        for report_file in subdir.rglob("*.md"):
                            if candidate_name in report_file.name:
                                save_dir = str(subdir)
                                logger.info(f"从文件系统找到报告目录: {save_dir}")
                                break
                        if save_dir:
                            break
        
        if not save_dir:
            return {"success": False, "message": f"未找到候选人 {candidate_name} 的报告目录"}
        
        reports = []
        for root, _, files in os.walk(save_dir):
            for fname in files:
                if candidate_name in fname and fname.endswith(('.md', '.txt')):
                    path = os.path.join(root, fname)
                    try:
                        with open(path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        # 解析轮次
                        round_num = 3
                        if '画像分析' in fname:
                            round_num = 2
                        else:
                            import re
                            m = re.search(r'第([0-9]+(?:\.[0-9])?)轮', fname)
                            if m:
                                val = m.group(1)
                                round_num = float(val) if '.' in val else int(val)
                        reports.append({"round": round_num, "content": content, "path": path})
                        logger.info(f"找到报告: {fname}, 轮次: {round_num}")
                    except Exception as e:
                        logger.error(f"读取报告文件失败 {path}: {e}")
                        continue
        
        # 按 round 排序
        reports.sort(key=lambda x: (isinstance(x['round'], float), x['round']))
        logger.info(f"候选人 {candidate_name} 找到 {len(reports)} 个报告")
        return {"success": True, "reports": reports}
    except Exception as e:
        logger.error(f"获取评估报告失败: {e}")
        return {"success": False, "message": str(e)}


@app.get("/get_evaluation_reports/{candidate_name}")
def get_evaluation_reports(candidate_name: str):
    try:
        # 扫描所有任务的 save_dir 和文件系统
        aggregated = []
        
        # 1. 从内存中的任务状态查找
        for tid, state in TASK_STATES.items():
            save_dir = state.get("save_dir")
            if not save_dir:
                continue
            for root, _, files in os.walk(save_dir):
                for fname in files:
                    if candidate_name in fname and fname.endswith(('.md', '.txt')):
                        path = os.path.join(root, fname)
                        try:
                            with open(path, 'r', encoding='utf-8') as f:
                                content = f.read()
                            import re
                            round_num = 3
                            if '画像分析' in fname:
                                round_num = 2
                            else:
                                m = re.search(r'第([0-9]+(?:\.[0-9])?)轮', fname)
                                if m:
                                    val = m.group(1)
                                    round_num = float(val) if '.' in val else int(val)
                            aggregated.append({"round": round_num, "content": content, "path": path, "task_id": tid})
                        except Exception as e:
                            logger.error(f"读取报告文件失败 {path}: {e}")
                            continue
        
        # 2. 如果内存中没有找到，从文件系统中查找
        if not aggregated:
            # 获取用户名称，构建用户Result目录路径
            user_name = user_state.get_current_user_name()
            
            result_dir = Path(path_manager.get_user_result_dir(user_name))
            if result_dir.exists():
                for subdir in result_dir.iterdir():
                    if subdir.is_dir():
                        for report_file in subdir.rglob("*.md"):
                            if candidate_name in report_file.name:
                                try:
                                    with open(report_file, 'r', encoding='utf-8') as f:
                                        content = f.read()
                                    import re
                                    round_num = 3
                                    if '画像分析' in report_file.name:
                                        round_num = 2
                                    else:
                                        m = re.search(r'第([0-9]+(?:\.[0-9])?)轮', report_file.name)
                                        if m:
                                            val = m.group(1)
                                            round_num = float(val) if '.' in val else int(val)
                                    aggregated.append({
                                        "round": round_num, 
                                        "content": content, 
                                        "path": str(report_file), 
                                        "task_id": "unknown"
                                    })
                                except Exception as e:
                                    logger.error(f"读取报告文件失败 {report_file}: {e}")
                                    continue
        
        aggregated.sort(key=lambda x: (isinstance(x['round'], float), x['round']))
        logger.info(f"候选人 {candidate_name} 找到 {len(aggregated)} 个报告")
        return {"success": True, "reports": aggregated}
    except Exception as e:
        logger.error(f"获取评估报告失败: {e}")
        return {"success": False, "message": str(e)}




# 简化的任务状态管理（不使用多进程）
TASK_STATES = {}


def set_current_position(position_title):
    """设置当前处理的岗位信息，用于VL识别结果保存"""
    from config import config
    import re
    # 确保岗位名称不为空且有效
    if position_title and position_title.strip():
        config.CURRENT_POSITION = position_title.strip()
    else:
        # 如果岗位名称为空，尝试从JD文件名提取
        if hasattr(config, 'JD_PATH') and config.JD_PATH:
            jd_filename = os.path.basename(config.JD_PATH)
            position_match = re.search(r'(.+?)_JD\.', jd_filename)
            if position_match:
                config.CURRENT_POSITION = position_match.group(1)
            else:
                config.CURRENT_POSITION = '未知岗位'
        else:
            config.CURRENT_POSITION = '未知岗位'


def _run_task_sync(task_id: str, jd_content: str, resume_items: list, round_select: list[float],
                   date_time: str, user_name: str = "未知用户"):
    """同步执行简历评估任务（替代多进程版本）"""
    try:
        # 提取岗位名称并设置结果目录、VL目录
        try:
            position_title = extract_position_name(jd_content)

            # 如果提取的岗位名称是"未知岗位"或"JD"，尝试从文件名或其他方式获取
            if position_title in ["未知岗位", "JD"]:
                logger.warning(f"岗位名称提取失败，尝试备用方法: {position_title}")
                # 尝试从JD内容中查找岗位名称
                import re
                patterns = [
                    r'"job_name":\s*"([^"]+)"',
                    r'招聘岗位[：:]\s*([^\n\r]+)',
                    r'职位[：:]\s*([^\n\r]+)',
                    r'岗位[：:]\s*([^\n\r]+)',
                    r'#\s*([^\n\r]+)',  # Markdown标题
                ]

                for pattern in patterns:
                    match = re.search(pattern, jd_content)
                    if match:
                        position_title = match.group(1).strip()
                        logger.info(f"通过正则表达式提取到岗位名称: '{position_title}'")
                        break

                # 如果还是没找到，使用默认名称
                if position_title in ["未知岗位", "JD"]:
                    position_title = "招聘岗位"
                    logger.warning(f"使用默认岗位名称: '{position_title}'")

        except Exception:
            position_title = "招聘岗位"

        # 独立结果目录（不污染全局）
        from config import config as global_config
        
        # 构建用户Result目录路径
        user_result_dir = os.path.join('user', user_name, 'result')
        os.makedirs(user_result_dir, exist_ok=True)
        
        # 获取结果目录，直接使用用户目录下的路径
        result_dirs = global_config.get_result_dirs_for_title(position_title, round_select, date_time, user_name)
        
        # 获取保存目录，直接使用用户目录下的路径
        save_dir = global_config.get_save_dir_for_title(position_title, round_select, date_time, user_name)

        # VL目录按岗位名称组织（相同岗位共享同一目录）
        safe_title = position_title.strip().replace('/', '_').replace('\\', '_') if position_title else '未知岗位'
        vl_dir = os.path.join('user', user_name, 'vl', f'VL_Result_{safe_title}')
        resume_dir = os.path.join('user', user_name, 'resume', safe_title)
        global_config.VL_RESULT_DIR = vl_dir
        global_config.RESUME_DIR = resume_dir
        os.makedirs(vl_dir, exist_ok=True)
        evaluator_local = ResumeEvaluator()
        # 将所选轮次注入评估器实例，确保与前端勾选一致
        setattr(evaluator_local, "round_select", round_select)

        # 初始化任务状态 - 每次评估都重新开始，避免结果叠加
        state = {
            "status": "running",
            "position_title": position_title,
            "total": len(resume_items),
            "completed": 0,
            "success": 0,
            "failed": 0,
            "details": [],  # 清空之前的结果
            "save_dir": save_dir,
            "vl_dir": vl_dir,
            "resume_dir": resume_dir,
            "created_at": datetime.now().isoformat(),  # 添加创建时间
            "user_name": user_name,  # 添加用户名称
            "task_id": task_id,  # 添加任务ID
            "round_select": round_select  # 添加轮次选择
        }
        TASK_STATES[task_id] = state

        # 顺序处理简历
        for filename, file_bytes, candidate_name in resume_items:
            try:
                # 设置当前处理的岗位信息，用于VL识别结果保存
                set_current_position(position_title)
                
                resume_text, is_vl_result = process_uploaded_file(file_bytes, filename, candidate_name)
                if not resume_text:
                    raise ValueError("无法提取文件内容")
                # 对于非VL识别结果，也保存到VL目录（同名候选人跳过）
                if not is_vl_result:
                    vl_result_path = os.path.join(vl_dir, f"{candidate_name}_VL识别结果.txt")
                    if not os.path.exists(vl_result_path):
                        with open(vl_result_path, 'w', encoding='utf-8') as f:
                            f.write(resume_text)
                        logger.info(f"✅ 保存新候选人简历到VL目录: {vl_result_path}")
                    else:
                        logger.info(f"⚠️ 跳过同名候选人: {candidate_name}，文件已存在")

                result, error = evaluator_local.process_single_resume_from_text(
                    resume_text, candidate_name, jd_content, result_dirs, round_select=round_select
                )

                detail = {"candidate_name": candidate_name}
                if result:
                    # 优先使用从初筛报告中提取的基本信息
                    basic_info = result.get('basic_info')
                    if not basic_info:
                        # 如果没有从初筛报告中提取到基本信息，则从简历文本中提取
                        basic_info = extract_candidate_basic_info(resume_text)
                    
                    detail.update({
                        "success": True,
                        "final_score": result['final_score'],
                        "final_round": result['final_round'],
                        "qualified": result['qualified'],
                        "report_paths": result['report_paths'],
                        "basic_info": basic_info,
                        "advantages": result.get('advantages', '查看详细报告了解优势'),
                        "risks": result.get('risks', '查看详细报告了解风险')
                    })
                    print(detail)
                    state["success"] += 1
                else:
                    detail.update({
                        "success": False,
                        "message": error or "评估失败"
                    })
                    state["failed"] += 1

                state["completed"] += 1
                state["details"].append(detail)
                TASK_STATES[task_id] = state

            except Exception as e:
                state["completed"] += 1
                state["failed"] += 1
                state["details"].append({
                    "candidate_name": candidate_name,
                    "success": False,
                    "message": f"处理失败: {e}"
                })
                TASK_STATES[task_id] = state

        state["status"] = "completed"
        TASK_STATES[task_id] = state
    except Exception as e:
        # 任务级失败
        state = TASK_STATES.get(task_id, {})
        state.update({"status": "failed", "error": str(e)})
        TASK_STATES[task_id] = state




@app.get("/api/history")
async def get_history():
    """获取历史评估记录列表"""
    try:
        # 获取用户名称，构建用户Result目录路径
        user_name = user_state.get_current_user_name()
        
        # 使用路径管理器获取用户结果目录
        result_base_path = Path(os.path.join('user', user_name, 'result'))
        logger.info(f"查找历史记录路径: {result_base_path.absolute()}")

        if not result_base_path.exists():
            logger.warning(f"Result文件夹不存在: {result_base_path.absolute()}")
            return {"success": True, "data": []}

        history_folders = []

        # 遍历Result文件夹下的所有文件夹
        for folder_path in result_base_path.iterdir():
            if folder_path.is_dir():
                folder_name = folder_path.name

                # 解析文件夹名称获取信息
                try:
                    # 解析格式：8.15_13：14_弱电工程预算员_二点五三轮筛选结果
                    parts = folder_name.split('_')
                    if len(parts) >= 3:
                        date_part = parts[0]  # 8.15 日期
                        time_part = parts[1]  # 13：14 时间
                        position_and_type = '_'.join(parts[2:])  # 弱电工程预算员_二点五三轮筛选结果

                        # 统计各分类下的文件数量
                        stats = {}
                        total_files = 0

                        for sub_folder in folder_path.iterdir():
                            if sub_folder.is_dir():
                                file_count = len([f for f in sub_folder.iterdir() if f.is_file() and f.suffix == '.md'])
                                stats[sub_folder.name] = file_count
                                total_files += file_count

                        history_item = {
                            "folder_name": folder_name,
                            "date": date_part,
                            "time": time_part,
                            "position_info": position_and_type,
                            "total_candidates": total_files,
                            "stats": stats,
                            "created_time": folder_path.stat().st_mtime
                        }

                        history_folders.append(history_item)

                except Exception as e:
                    logger.warning(f"解析文件夹名称失败: {folder_name}, 错误: {e}")
                    continue

        # 按创建时间倒序排列
        history_folders.sort(key=lambda x: x['created_time'], reverse=True)

        return {"success": True, "data": history_folders}

    except Exception as e:
        logger.error(f"获取历史记录失败: {e}")
        return {"success": False, "message": f"获取历史记录失败: {e}"}


@app.get("/api/history/{folder_name}")
async def get_history_detail(folder_name: str):
    """获取指定历史记录的详细内容"""
    try:
        # 获取用户名称，构建用户Result目录路径
        user_name = user_state.get_current_user_name()
        
        folder_path = Path(os.path.join('user', user_name, 'result')) / folder_name
        if not folder_path.exists():
            raise HTTPException(status_code=404, detail="历史记录不存在")

        result = {
            "folder_name": folder_name,
            "categories": {}
        }

        # 遍历子文件夹
        for sub_folder in folder_path.iterdir():
            if sub_folder.is_dir():
                category_name = sub_folder.name
                files = []

                # 获取该分类下的所有md文件
                for file_path in sub_folder.iterdir():
                    if file_path.is_file() and file_path.suffix == '.md':
                        # 解析文件名获取信息
                        try:
                            file_name = file_path.stem  # 去掉.md后缀
                            parts = file_name.split('_')

                            # 判断文件格式
                            if len(parts) >= 4 and (parts[3] == '评估报告' or '评估报告' in parts[3]):
                                # 格式1：得分_姓名_第几轮_评估报告.md
                                score = parts[0]
                                name = parts[1]
                                round_info = parts[2]
                            elif len(parts) >= 3 and ('画像分析' in parts[2] or '画像分析' in file_name):
                                # 格式2：姓名_第几轮_画像分析.md
                                score = "画像分析"
                                name = parts[0]
                                round_info = parts[1]
                            elif len(parts) >= 3:
                                # 其他格式：尝试通用解析
                                score = parts[0] if not parts[0].startswith('第') else "未知"
                                name = parts[0] if parts[0].startswith('第') else parts[1] if len(parts) > 1 else parts[
                                    0]
                                round_info = next((p for p in parts if p.startswith('第') and '轮' in p), "第2轮")
                            else:
                                # 解析失败，使用默认值
                                score = "未知"
                                name = parts[0] if parts else "未知"
                                round_info = "第2轮"

                            file_info = {
                                "filename": file_path.name,
                                "candidate_name": name,
                                "score": score,
                                "round": round_info,
                                "file_size": file_path.stat().st_size,
                                "modified_time": file_path.stat().st_mtime
                            }
                            files.append(file_info)
                        except Exception as e:
                            logger.warning(f"解析文件名失败: {file_path.name}, 错误: {e}")
                            continue

                # 按分数排序（降序）
                try:
                    files.sort(key=lambda x: float(x['score'].replace('分', '')), reverse=True)
                except:
                    files.sort(key=lambda x: x['candidate_name'])

                result["categories"][category_name] = files

        return {"success": True, "data": result}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取历史记录详情失败: {e}")
        return {"success": False, "message": f"获取历史记录详情失败: {e}"}


@app.get("/api/history/{folder_name}/{category}/{filename}")
async def get_history_file_content(folder_name: str, category: str, filename: str):
    """获取历史记录文件的具体内容"""
    try:
        # 获取用户名称，构建用户Result目录路径
        user_name = user_state.get_current_user_name()
        
        file_path = Path(os.path.join('user', user_name, 'result')) / folder_name / category / filename
        if not file_path.exists() or not file_path.is_file():
            raise HTTPException(status_code=404, detail="文件不存在")

        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        return {
            "success": True,
            "data": {
                "filename": filename,
                "content": content,
                "file_size": file_path.stat().st_size,
                "modified_time": file_path.stat().st_mtime
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"读取文件内容失败: {e}")
        return {"success": False, "message": f"读取文件内容失败: {e}"}


@app.get("/api/resume_file/{candidate_name}")
async def get_resume_file_path(candidate_name: str):
    """获取候选人简历文件路径（支持图片和PDF）"""
    try:
        resume_base_path = Path(os.path.join('user', user_state.get_current_user_name(), 'resume'))

        # 遍历所有子目录寻找匹配的简历文件
        for subfolder in resume_base_path.iterdir():
            if subfolder.is_dir():
                for file_path in subfolder.iterdir():
                    if file_path.is_file() and file_path.suffix.lower() in ['.png', '.jpg', '.jpeg', '.pdf']:
                        # 检查文件名是否匹配候选人姓名
                        filename_stem = file_path.stem

                        # 方式1：文件名格式：候选人名_职位_日期时间.扩展名
                        filename_parts = filename_stem.split('_')
                        if len(filename_parts) >= 3 and filename_parts[0] == candidate_name:
                            relative_path = f"{subfolder.name}/{file_path.name}"
                            file_type = 'pdf' if file_path.suffix.lower() == '.pdf' else 'image'
                            return {
                                "success": True,
                                "file_url": f"/resume_images/{relative_path}",
                                "filename": file_path.name,
                                "file_type": file_type
                            }

                        # 方式2：直接匹配候选人姓名
                        # 使用更精确的匹配：候选人姓名必须在文件名开头或者被分隔符包围
                        if (filename_stem.startswith(candidate_name + '-') or
                                filename_stem.startswith(candidate_name + '_') or
                                filename_stem == candidate_name or
                                (candidate_name + '.') in file_path.name):
                            relative_path = f"{subfolder.name}/{file_path.name}"
                            file_type = 'pdf' if file_path.suffix.lower() == '.pdf' else 'image'
                            return {
                                "success": True,
                                "file_url": f"/resume_images/{relative_path}",
                                "filename": file_path.name,
                                "file_type": file_type
                            }

        return {"success": False, "message": f"未找到候选人 {candidate_name} 的简历文件"}

    except Exception as e:
        logger.error(f"获取简历文件路径失败: {e}")
        return {"success": False, "message": f"获取简历文件路径失败: {e}"}



def main():
    """主函数 - 启动简历评估系统"""
    os.makedirs(config.VL_RESULT_DIR, exist_ok=True)

    # 启动服务器
    logger.info("🚀 启动智能简历评估系统")
    logger.info("📋 系统功能:")
    logger.info("  - 支持TXT、PDF、图片格式简历")
    logger.info("  - 多线程并发处理")
    logger.info("  - 实时进度监控")
    logger.info("  - 集成Boss直聘爬虫系统")
    logger.info(f"📱 前端访问地址: http://{config.HOST}:8008")
    logger.info(f" - JD来源: {config.JD_PATH}")
    logger.info("🔗 爬虫系统地址: http://localhost:8000")
    # uvicorn.run(app, host=config.HOST, port=8008, reload=True)
    uvicorn.run("resume_score_new:app", host=config.HOST, port=8008, reload=False)

@app.get("/api/current_user")
async def get_current_user_status():
    """获取当前用户状态信息"""
    try:
        current_user = user_state.get_current_user()
        if current_user:
            return {
                "success": True,
                "user_info": current_user,
                "message": "获取用户信息成功"
            }
        else:
            return {
                "success": False,
                "message": "用户未登录"
            }
    except Exception as e:
        logger.error(f"获取用户状态失败: {e}")
        return {
            "success": False,
            "message": f"获取用户状态失败: {str(e)}"
        }

@app.get("/api/user_jds")
async def get_user_jds():
    """获取当前用户的JD库"""
    try:
        user_name = user_state.get_current_user_name()
        jds = user_state.get_user_jds(user_name)
        return {
            "success": True,
            "user_name": user_name,
            "jd_count": len(jds),
            "jd_list": list(jds.keys())
        }
    except Exception as e:
        logger.error(f"获取用户JD库失败: {e}")
        return {
            "success": False,
            "message": f"获取用户JD库失败: {str(e)}"
        }

@app.get("/logout")
def logout():
    """用户登出"""
    user_state.clear_current_user()
    return {"success": True, "message": "已登出"}



@app.delete("/api/jd/{jd_title}")
async def delete_jd(jd_title: str):
    """删除指定的JD文件及其所有同名文件（不同扩展名）"""
    try:
        user_name = user_state.get_current_user_name()
        jd_library_dir = os.path.join('user', user_name, 'jd')
        # 检查是否存在至少一个同名文件
        delete_happen = False
        deleted_files = []

        # 删除所有同名文件（不同扩展名）
        for ext in ['.md', '.json', '.txt']:
            file_path = os.path.join(jd_library_dir, jd_title + ext)
            print("file_path:",file_path)
            if os.path.exists(file_path):
                print('exist:',file_path)
                os.remove(file_path)
                deleted_files.append(jd_title + ext)
                delete_happen = True
                logger.info(f"用户 {user_name} 删除了JD文件: {jd_title + ext}")

        if not delete_happen:
            return {"success": False, "message": "JD文件不存在"}

        # 返回删除的文件列表
        return {
            "success": True,
            "message": f"成功删除 {len(deleted_files)} 个相关文件",
            "deleted_files": deleted_files
        }
    except Exception as e:
        logger.error(f"删除JD失败: {e}")
        return {"success": False, "message": f"删除JD失败: {e}"}


if __name__ == "__main__":
    main()